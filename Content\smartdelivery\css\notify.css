﻿/* ========================================================================
   Component: notify.less
 ========================================================================== */
.uk-notify {
    position: fixed;
    top: 50px;
    left: 50px;
    z-index: 1060;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    width: 350px;
}

.uk-notify-top-right,
.uk-notify-bottom-right {
    left: auto;
    right: 50px;
}

.uk-notify-top-center,
.uk-notify-bottom-center {
    left: 50%;
    margin-left: -175px;
}

.uk-notify-bottom-left,
.uk-notify-bottom-right,
.uk-notify-bottom-center {
    top: auto;
    bottom: 50px;
}

@media (max-width: 480px) {
    .uk-notify {
        left: 10px;
        right: 10px;
        width: auto;
        margin: 0;
    }
}

.uk-notify-message {
    position: relative;
    margin-bottom: 10px;
    padding: 15px;
    font-size: 16px;
    line-height: 22px;
    border-radius: 3px;
    padding-right: 35px;
    cursor: pointer;
}

.uk-notify-message.alert.alert-normal {
    background: #444444;
    color: #ffffff;
}

.uk-notify-message > .close {
    visibility: hidden;
}

.uk-notify-message:hover > .close {
    visibility: visible;
}