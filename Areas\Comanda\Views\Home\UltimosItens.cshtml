﻿@using Consumer.Resources.Mobile.Comanda.Views.Home
@model RAL.Consumer.Mobile.Areas.Comanda.Models.UltimosItensViewModel

@{
    ViewBag.Title = UltimosItensViewModelRes.MeusUltimosItens;
}

<div>
    @if (Model.Itens.Count() > 0)
    {
        <table class="table table-condensed table-responsive tabela-pedido-itens ultimos-itens">
            @foreach (var item in Model.Itens.Where(i => !i.CodigoPai.HasValue))
            {
                <tr class="linha-pai">
                    <td @(item.Quantidade == 0 ? "colspan=2" : "")>
                        <i class="glyphicon glyphicon-chevron-right"></i>
                        @Html.Raw(item.NomeProduto)
                        @if (!string.IsNullOrWhiteSpace(item.Detalhes))
                        {
                            <i class="small">*@item.Detalhes</i>
                        }
                    </td>
                    @if (item.Quantidade > 0)
                    {
                        <td class="item-produto-qtde">@item.Quantidade</td>
                    }
                    <td>
                        @{
                            if (item.NumeroMesa > 0)
                            {
                                if (item.PedidoFechado)
                                {
                                    <span class="label label-default" data-toggle="tooltip" data-placement="top" title="Veja os detalhes no Consumer">Fechado</span>
                                }
                                else
                                {
                                    <a class="label label-success" title="@UltimosItensViewModelRes.IrParaMesaComanda" href="@Url.Action("Details", "Home", new { Numero = item.NumeroMesa })">
                                        @item.MesaBalcaoCaixaDeliveyDescricao
                                    </a>
                                }
                                <br />

                                <label id="lblCodigoPedido" class="control-label" title="Pedido #@item.CodigoPedido">Ped.#@item.CodigoPedido</label>

                                if (item.CodigoPersonalizado.HasValue)
                                {
                                    <label id="lblCodigoPersonalizado" class="control-label small" title="Código Personalizado"> (@item.CodigoPersonalizado)</label>
                                }
                            }
                            else
                            {
                                @item.MesaBalcaoCaixaDeliveyDescricao
                            }
                        }
                    </td>
                    <td>@item.DataHoraCadastro.ToString("dd/MM HH:mm")</td>
                </tr>
                //Filho
                foreach (var itemFilho in Model.Itens.Where(i => i.CodigoPai == item.Codigo).OrderBy(c => Array.IndexOf(new int[] { 2, 1, 3, 4, 5, 6, 7, 8 }, c.CodigoItemPedidoTipo)))
                {
                    <tr class="linha-filho">
                        <td>
                            <i class="glyphicon glyphicon-@(itemFilho.CodigoItemPedidoTipo == 2 ? "plus-sign" : (itemFilho.CodigoItemPedidoTipo == 7) ? "adjust" : "inbox")"></i>

                            @Html.Raw(itemFilho.NomeProduto)
                            @if (!string.IsNullOrWhiteSpace(itemFilho.Detalhes))
                            {
                                <i class="small">* @itemFilho.Detalhes</i>
                            }
                        </td>
                        <td>@itemFilho.Quantidade</td>
                        <td colspan="2"></td>
                    </tr>
                    //Neto
                    foreach (var itemNeto in Model.Itens.Where(i => i.CodigoPai == itemFilho.Codigo).OrderBy(c => Array.IndexOf(new int[] { 2, 1, 3, 4, 5, 6, 7, 8 }, c.CodigoItemPedidoTipo)))
                    {
                        <tr class="linha-neto">
                            <td>
                                <i class="glyphicon glyphicon-plus-sign"></i>
                                @Html.Raw(itemNeto.NomeProduto)
                                @if (!string.IsNullOrWhiteSpace(itemNeto.Detalhes))
                                {
                                    <i class="small">* @itemNeto.Detalhes</i>
                                }
                            </td>
                            <td>@itemNeto.Quantidade</td>
                            <td colspan="2">
                            </td>
                        </tr>
                    }
                }
            }
        </table>
    }
</div>

<nav id="actions" class="navbar navbar-default navbar-fixed-bottom">
    <div id="pedido-actions" class="container-fluid">
        <div class="col-xs-3">
            <a id="btn-voltar" class="btn btn-lg btn-default btn-block navbar-btn" title="@DetailsRes.Voltar"><i class="glyphicon glyphicon-circle-arrow-left"></i> <span class="hidden-xs">@DetailsRes.Voltar</span><small class="visible-xs">@DetailsRes.Voltar</small></a>
        </div>
    </div>
</nav>


@section Scripts{
    <script>
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
    </script>
}