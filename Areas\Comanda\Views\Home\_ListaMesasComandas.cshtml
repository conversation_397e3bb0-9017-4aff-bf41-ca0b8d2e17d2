﻿@model Consumer.ServiceAbstractions.Services.Pedidos.PedidoModels.Mesas


<div class="container">
    @if (Model.MesasAbertas != null && Model.MesasAbertas.Count() > 0)
    {
        <b class="mesasTitulo">Pedidos em andamento (@(Model.MesasAbertas.Count()))</b>
        <div class="listaDeMesas mesasOcupadas">
            @foreach (var item in Model.MesasAbertas)
            {
                <a class="abrirMesa" href="@Url.Action("Details", "Home", new { Numero = item.Numero })">
                    <div class="mesas @(item.ContaSolicitada ? "emFechamento" : null)">
                        <span class="hidden duracao">@(item.DuracaoInfo)</span>
                        @if (item.ContaSolicitada)
                        {
                            <span class="iconeMesaBloqueada"><i class="fa fa-lock fa"></i></span>
                        }

                        <div class="numeroMesa">
                            <b>@item.Numero.ToString("00")</b>
                        </div>

                        <span id="nomeCliente">@item.Nome</span>

                        <span class="hidden valor">@Html.Raw(!item.ProcessandoNovosItens ? item.ValorFormatado : "<i class='fa fa-hourglass fa-fw'></i> Processando")</span>
                    </div>
                </a>

            }
        </div>
    }
    <br />
    @if (Model.MesasDisponiveis != null && Model.MesasDisponiveis.Count() > 0)
    {
        <b class="mesasTitulo">Mesas/Comandas livres (@(Model.MesasDisponiveis.Count()))</b>
        <div class="listaDeMesas mesasLivres">
            @foreach (var item in Model.MesasDisponiveis)
            {
                <a class="abrirMesa" href="@Url.Action("Details", "Home", new { Numero = item.Numero })">
                    <div class="mesas">
                        <div class="numeroNome">
                            @item.Numero.ToString("00")
                        </div>
                    </div>
                </a>

            }
        </div>
    }
</div>