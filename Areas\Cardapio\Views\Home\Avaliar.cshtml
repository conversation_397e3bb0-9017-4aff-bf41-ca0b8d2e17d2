﻿@using Consumer.Resources.Mobile.Cardapio.Views.Home
@model RAL.Consumer.Data.Entities.PEDIDOCARDAPIO
@{
    ViewBag.Title = @AvaliarRes.Avalie;
}

@using (Html.BeginForm("Avaliar", "Home", FormMethod.Post, new { id = "avaliarForm" }))
{

    <div class="form-group text-center">
        <select id="NOTA" name="NOTA" class="hide">
            <option value=""></option>
            <option value="1">1</option>
            <option value="2">2</option>
            <option value="3">3</option>
            <option value="4">4</option>
            <option value="5">5</option>
        </select>
        <div class="rateit" data-rateit-backingfld="#NOTA" data-rateit-resetable="false" data-rateit-mode="font" data-rateit-starwidth="32px"></div>
        <br />
        @Html.ValidationMessageFor(model => model.NOTA, "", new { @class = "text-danger" })
    </div>

    <div class="form-group">
        @Html.EditorFor(model => model.COMENTARIO, new { htmlAttributes = new { @class = "form-control input-lg", placeholder = HttpUtility.HtmlDecode(Html.DisplayNameFor(n => n.COMENTARIO).ToHtmlString()) } })
        @Html.ValidationMessageFor(model => model.COMENTARIO, "", new { @class = "text-danger" })
    </div>
    @*<div class="form-group">
            <button class="btn btn-lg btn-success btn-block">Enviar</button>
        </div>*@
}

@section BodyArea {
    <nav id="actions" class="navbar navbar-default navbar-fixed-bottom">
        <div class="col-xs-12">
            <a href="javascript:document.getElementById('avaliarForm').submit()" class="btn btn-lg btn-block btn-square btn-success navbar-btn" title="@AvaliarRes.EnviarAvaliacao"><i class="fa fa-check"></i><br />@AvaliarRes.Enviar</a>
        </div>
    </nav>
}



@section Styles{
    @Styles.Render("~/bundles/rateitCss")
}
@section Scripts{
    @Scripts.Render("~/bundles/rateit")
}
