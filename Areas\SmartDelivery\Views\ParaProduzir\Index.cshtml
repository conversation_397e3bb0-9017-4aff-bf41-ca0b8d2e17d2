﻿@using Consumer.Resources.Mobile.SmartDelivery.Views.ParaProduzir;
@{
    Layout = null;
}

<div class="row h-100">
    <div class="col-xl-9 col-lg-8 col-md-8 col-sm-7 col-12 p-0">
        <div id="mapProduce" class="h-100"></div>
        <!-- Select Orders Here!  -->
        <div id="divSelect" class="sd-div-select text-center text-light">
            <span>
                <span data-order-qty-selected>3</span>@IndexRes.Selecionados
                <a class="small text-light" href="javascript:void(0)" onclick="mapProduce.unselectAll()">@IndexRes.Cancelar</a>
            </span>
            <div class="btn-group d-inline-block">
                <button type="button" class="btn btn-link text-white" onclick="mapProduce.doGroup()"><i class="material-icons text-success align-text-bottom">done</i>@IndexRes.AgruparProduzir</button>
                <button type="button" class="btn btn-link text-white dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span class="sr-only">Toggle Dropdown</span>
                </button>
                <div class="dropdown-menu overflow-auto" style="max-height: 80vh">
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-4 col-sm-5 d-none d-sm-block bg-dark h-100 overflow-auto">
        <div class="dropdown float-lg-right">
            <a href="#" id="dropdownMenuSortOrder" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <span class="material-icons float-right text-white">
                    sort
                </span>
            </a>
            <div class="dropdown-menu" aria-labelledby="dropdownMenuSrotOrder">
                <button class="dropdown-item" type="button" data-sortBy="orderCreatedDateTime" data-sortAsc="true">@IndexRes.Antigos</button>
                <button class="dropdown-item" type="button" data-sortBy="orderCreatedDateTime" data-sortAsc="false">@IndexRes.Recentes</button>
                @*<button class="dropdown-item" type="button" data-sortBy="orderid" data-sortAsc="true">Pedidos crescentes</button>
                    <button class="dropdown-item" type="button" data-sortBy="orderid" data-sortAsc="false">Pedidos decrescentes</button>*@
                <button class="dropdown-item" type="button" data-sortBy="groupid" data-sortAsc="true">@IndexRes.Grupo</button>
            </div>
        </div>

        <h5 class="text-white">
            @IndexRes.PedidosProduzir
        </h5>
        <div class="text-white" id="table-orders">
            <p class="text-center small">@IndexRes.NenhumPedido</p>
            @* JS *@
        </div>
    </div>
</div>

<div id="mySidepanel" class="sidepanel">
    <a href="javascript:void(0)" class="closebtn" onclick="return togglePanelRight(false);">&times;</a>
    <div class="container p-3">
        <h5>@IndexRes.Sugestoes</h5>
        <div class="groups"></div>
    </div>
    <p class="text-center small">
        <a href="javascript:void(0)" onclick="return mapProduce.getGroupsSuggestionsRoboV2()">@IndexRes.Atualizar</a> |
        <a href="javascript:void(0)" onclick="return mapProduce.clearSuggestions()">@IndexRes.Limpar</a>
    </p>
</div>

<script id="template-grid-item-sd-order-to-produce" type="text/template">
    <div class="row sd-order" data-select-to-produce data-orderid="{{orderId}}" data-statusdatetime="{{orderCreatedDateTime}}">
        <div class="col">
            <p class="font-weight-bold m-0">
                #{{orderId}} {{orderCustomerName}}
            </p>
            <p class="m-0">
                {{orderAddress}}
            </p>
        </div>
        <div class="col-auto sd-info-order text-right border-right">
            <span class="text-time font-weight-bold" data-relativedatetime>@IndexRes.Ha</span><br>
            <a href="#" data-open-order>
                <i class="material-icons md-24 gray align-text-bottom">info</i>
            </a>
        </div>
    </div>
</script>