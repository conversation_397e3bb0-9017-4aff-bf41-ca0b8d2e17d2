﻿@using Consumer.Resources.Mobile.Cardapio.Views.Pedido
@model RAL.Consumer.Data.Entities.PEDIDOS
@using RAL.Consumer.Data.Context
@{
    ViewBag.Title = @ReviewRes.RevisePedido;
}

<div class="panel b serrilhado">
    <div class="panel-heading pt">
        <h4 class="panel-title text-center">
            @ReviewRes.MesaComanda @Model.NUMERO
        </h4>
    </div>
    <div class="panel-body">
        @using (Html.BeginForm("review", "pedido", FormMethod.Post, new { id = "formEnviarPedido" }))
        {
            @Html.AntiForgeryToken()
            <input type="hidden" id="SenhaValidacao" name="SenhaValidacao" value="" />
            if (ViewBag.CardapioDigitalPermitirObservacaoPedido == true)
            {
                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <b>@ReviewRes.ObsMesaComanda</b>
                            @Html.EditorFor(model => model.NOME, new { htmlAttributes = new { @class = "form-control input-lg", placeholder = "Observações da Mesa/Comanda" } })
                            @Html.ValidationMessageFor(model => model.NOME, "", new { @class = "text-danger" })
                        </div>
                    </div>
                </div>
            }
        }

        @if (Model.ITENSPEDIDO.Listar().Count() > 0)
        {
            <div class="table-responsive">
                <table class="table table-condensed">
                    <colgroup>
                        <col class="order-item-name">
                        <col class="order-qty">
                        <col class="order-price">
                        <col class="order-total">
                    </colgroup>
                    <thead class="bg-gray-lighter">
                        <tr>
                            <th colspan="2">@ReviewRes.Item</th>
                            <th class="order-price hidden-xs">@ReviewRes.ValorUn</th>
                            <th class="order-total">@ReviewRes.Total</th>
                        </tr>
                    </thead>
                    <tfoot>
                        <tr class="order-subtotal text-right">
                            <td colspan="2">@ReviewRes.Subtotal</td>
                            <td colspan="2" class="order-subtotal-amount">@Model.ITENSPEDIDO.Listar().Sum(i => i.ValorTotalIncluindoFilhos).ToString("C")</td>
                        </tr>
                        @*<tr class="order-subtotal">
                                <td colspan="3">(+) Entrega</td>
                                <td colspan="2">$0.00</td>
                            </tr>*@
                        @if (Model.ITENSPEDIDO.Listar().Sum(i => i.QUANTIDADE * i.ValorUnitarioServico).GetValueOrDefault() > 0)
                        {
                            <tr class="order-subtotal text-right">
                                <td colspan="2">@ReviewRes.Servico</td>
                                <td colspan="2" class="order-service-amount">@Model.ITENSPEDIDO.Listar().Sum(i => i.QUANTIDADE * i.ValorUnitarioServico).GetValueOrDefault().ToString("C")</td>
                            </tr>
                        }
                        @*<tr class="order-subtotal">
                                <td colspan="3">(-) Desconto</td>
                                <td colspan="2">$1540.00</td>
                            </tr>*@
                        <tr class="order-subtotal text-right">
                            <td colspan="2">@ReviewRes.TotalFinal</td>
                            <td colspan="2" class="order-total-amount">@((Model.ITENSPEDIDO.Listar().Sum(i => i.ValorTotalIncluindoFilhos) + Model.ITENSPEDIDO.Listar().Sum(i => i.QUANTIDADE * i.ValorUnitarioServico).GetValueOrDefault()).ToString("C"))</td>
                        </tr>
                    </tfoot>
                    <tbody>
                        @foreach (var item in Model.ITENSPEDIDO.Listar().OrderBy(c => c.PRODUTODETALHE.PRODUTOS.CODIGOPRODUTOTIPO).ThenBy(c => c.CODIGOITEMPEDIDOTIPO))
                        {
                            bool isWizard = (item.ITENSPEDIDOFILHOS != null && item.ITENSPEDIDOFILHOS.Any(i => i.OpcaoWizardSelecionada > 0));

                            @*<tr id="<EMAIL>" class="details">
                                    <td class="order-qty">@item.QUANTIDADE.GetValueOrDefault().ToString("#x")</td>
                                    <td class="order-item-name">@Html.Raw(item.NomeComDetalheQuebraLinha)</td>
                                    <td class="order-price hidden-xs">@item.VALORUNITARIO.GetValueOrDefault().ToString("C")</td>
                                    <td class="order-total">@item.ValorTotal.ToString("C")</td>
                                </tr>*@
                            @Html.Partial("_ItemPedidoRow", item)
                            <tr id="<EMAIL>" class="actions">
                                <td colspan="4">
                                    @using (Ajax.BeginForm("EditItem", new AjaxOptions { HttpMethod = "POST", OnSuccess = "OnSuccessEdit" }))
                                    {
                                        @Html.AntiForgeryToken()
                                        @Html.Hidden("Codigo", item.CODIGO)
                                        <input type="hidden" id="<EMAIL>" name="qtd" />

                                        <div class="col-xs-4">
                                            @if (!isWizard)
                                            {
                                                <button type="submit" class="btn btn-info btn-block" data-id="@item.CODIGO" data-qtd="1"><i class="fa fa-plus"></i></button>
                                            }
                                        </div>
                                        <div class="col-xs-4">
                                            @if (!isWizard)
                                            {
                                                <button type="submit" class="btn btn-info btn-block" data-id="@item.CODIGO" data-qtd="-1"><i class="fa fa-minus"></i></button>
                                            }
                                        </div>

                                        <div class="col-xs-4">
                                            <button type="submit" class="btn btn-warning btn-block" data-id="@item.CODIGO" data-qtd="0"><i class="fa fa-trash"></i></button>
                                        </div>
                                    }
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
    <p>@ReviewRes.FazerPedido</p>

    <a href="@Url.Action("Categoria", "Produto")" class=" btn btn-lg btn-primary btn-block btn-square" title="@ReviewRes.AdicionarItens"><i class="fa fa-plus"></i><br />@ReviewRes.Pedir</a>
        }
    </div>
</div>

@section BodyArea {
    @if (Model.ITENSPEDIDO != null && Model.ITENSPEDIDO.Count > 0)
    {
        <nav id="actions" class="navbar navbar-default navbar-fixed-bottom">
            <div class="col-xs-4">
                <a href="@Url.Action("Cancel")" class="btn btn-lg btn-block btn-square btn-danger navbar-btn" title="@ReviewRes.ExcluirPedido"><i class="fa fa-trash"></i><br />@ReviewRes.Excluir</a>
            </div>
            <div class="col-xs-4">
                <button id="btnChamarGarcom" class="btn btn-lg btn-block btn-square btn-warning navbar-btn" title="@ReviewRes.ChamarGarcom"><i class="fa fa-bell"></i><br /><span class="hidden-xs">@ReviewRes.Chamar</span>@ReviewRes.GarcomChamar</button>
            </div>
            <div class="col-xs-4">
                <button id="btnEnviarPedido" class="btn btn-lg btn-block btn-square btn-success navbar-btn" title="@ReviewRes.EnviarPedido">
                    <i class="fa fa-check"></i><br />@ReviewRes.Confirmar
                </button>
            </div>
        </nav>
    }
}

@section Scripts {
    <script>
        $(document).ready(function () {
            $("button").click(function () {
                $("#qtd-" + $(this).data("id")).val($(this).data("qtd"));
            });

            var senhaInvalida = @((ViewBag.SenhaInvalida ?? false).ToString().ToLower()) ;
            if(senhaInvalida) {
                window.sessionStorage.removeItem('SenhaValidacao');
            }
            else
            {
                var senhaValidacao = window.sessionStorage.getItem('SenhaValidacao');
                if (senhaValidacao != null) {
                    $('#SenhaValidacao').val(senhaValidacao);
                }
            }
            @if (ViewBag.CardapioDigitalSolicitarSenha == true)
            {
                <text>
            $('#btnEnviarPedido').click(function (e) {
                    if ($('#SenhaValidacao').val() == null || $('#SenhaValidacao').val().trim().length == 0) {
                        e.preventDefault();
                        swal({
                            title: "@ReviewRes.SenhaPedido",
                            text: "@Html.Raw(ReviewRes.Garcom)",
                            type: "input",
                            inputType: "tel",
                            showCancelButton: true,
                            closeOnConfirm: false,
                            confirmButtonClass: "btn-success",
                            confirmButtonText: "@ReviewRes.Confirmar",
                            cancelButtonClass: "btn-default",
                            cancelButtonText: "@ReviewRes.Cancelar",
                        }, function (typedPassword) {
                            if (typedPassword != null && typedPassword && typedPassword.trim().length > 0) {
                                $('#SenhaValidacao').val(typedPassword);
                                window.sessionStorage.setItem('SenhaValidacao', typedPassword);
                                $('#formEnviarPedido').submit();
                            }
                            else {
                                swal.showInputError("@ReviewRes.InformeSenha");
                            }
                        });
                    }
                    else {
                        $('#formEnviarPedido').submit();
                    }
                });
                </text>
            }
            else
            {
                <text>
                $('#btnEnviarPedido').click(function (e) {
                    $('#formEnviarPedido').submit();
                });
                </text>
            }
            $(document).on("click", "#btnChamarGarcom", function (e) {
    chamarGarcom(e);
});
            });

        function OnSuccessEdit(response) {
            if (response && response.data) // verifica se response e se response.data existe
            {
                var data = response.data;
                if (data.DelPedido || data.Refresh) {
                    location.reload();
                }
                else {
                    if (data.DelItem) {
                        $("#detail-" + data.Codigo).remove();
                        $("#action-" + data.Codigo).remove();
                        $("#detail-subtotal-" + data.Codigo).remove();
                        $("[id*=detail-" + data.Codigo + "-]").remove();
                    }
                    else {
                        $("#detail-" + data.Codigo + " td.order-qty").text(data.Quantidade);
                        $("#detail-" + data.Codigo + " td.order-price").text(data.ValorUnitario);
                        $("#detail-" + data.Codigo + " td.order-total").text(data.ValorTotal);

                        data.ItensFilhos.forEach(function (filho, index, ar) {
                            $("#detail-" + data.Codigo + "-" + filho.Codigo + " td.order-qty").text(filho.Quantidade);
                            $("#detail-" + data.Codigo + "-" + filho.Codigo + " td.order-price").text(filho.ValorUnitario);
                            $("#detail-" + data.Codigo + "-" + filho.Codigo + " td.order-total").text(filho.ValorTotal);
                            filho.ItensNetos.forEach(function (neto, indexNeto, arNeto) {
                                $("#detail-" + data.Codigo + "-" + filho.Codigo + "-" + neto.Codigo + " td.order-qty").text(neto.Quantidade);
                                $("#detail-" + data.Codigo + "-" + filho.Codigo + "-" + neto.Codigo + " td.order-price").text(neto.ValorUnitario);
                                $("#detail-" + data.Codigo + "-" + filho.Codigo + "-" + neto.Codigo + " td.order-total").text(neto.ValorTotal);
                            });
                        });

                        $("#detail-subtotal-" + data.Codigo + " td").text(data.ItemSubtotal);
                    }
                    $(".order-subtotal-amount").text(data.PedidoSubtotal);
                    $(".order-service-amount").text(data.PedidoServico);
                    $(".order-total-amount").text(data.PedidoTotal);
                }
            }

        }
    </script>
}

@section Styles {
    <style>
        tr.details + tr.actions td {
            border-top: 0;
            padding-bottom: 10px;
        }
    </style>
}
