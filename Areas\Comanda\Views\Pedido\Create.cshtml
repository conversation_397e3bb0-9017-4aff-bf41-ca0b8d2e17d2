﻿@using Consumer.Resources.Mobile.Comanda.Views.Pedido
@model RAL.Consumer.Mobile.Areas.Comanda.Models.PedidoCriacaoViewModel

@{
    ViewBag.Title = @CreateRes.NovoPedido + Model.Pedido.Numero;
}

<div class="col-xs-12">
    <div class="form-group has-feedback">
        <label class="control-label sr-only" for="q">@CreateRes.BuscarProduto</label>
        <div class="input-group">
            <input type="text" class="form-control" id="q" placeholder="@CreateRes.BuscarTodasCat">
            <span class="input-group-btn">
                <button id="btn-pesquisar" class="btn" type="button" style="box-shadow:none">
                    <i class="glyphicon glyphicon-search"></i>
                </button>
            </span>
        </div>
    </div>
</div>


<div id="list-categoria">
    @Html.Partial("_CategoriaPartial",  Model.Categorias)
</div>
<div style="display:none">@*REMOVER ESSE DIV QUANDO ESTIVER OTIMIZADA PARA CONSULTA DE MAIS VENDIDOS*@
    <div id="sort-options" class="col-xs-12" style="padding-bottom: 15px;display:none">
        <a href="#" class="btn btn-default" id="btn-orderbyname" style="display:none">@CreateRes.OrdenarNome</a>
        <a href="#" class="btn btn-default" id="btn-orderbysells">@CreateRes.OrdenarMaisVendidos</a>
    </div>
</div>
<div id="list-produto">
</div>

<nav id="actions" class="navbar navbar-default navbar-fixed-bottom">
    <div id="item-produto-selected" class="item-produto container-fluid alert alert-success hide">
        <div class="item-produto-data col-xs-12">
            <h4 style="margin:0"><span class="item-produto-qtde label label-primary pull-right"></span></h4>
            <i class="glyphicon glyphicon-ok"></i> <span class="item-produto-nome"></span>
        </div>
        <div class="item-produto-actions">
            <div class="col-xs-3">
                <a class="btn-add btn btn-lg btn-default btn-block navbar-btn"><i class="glyphicon glyphicon-plus text-success"></i> 1</a>
            </div>
            <div class="col-xs-3">
                <a class="btn-del btn btn-lg btn-default btn-block navbar-btn"><i class="glyphicon glyphicon-minus text-danger"></i> 1</a>
            </div>
            <div class="col-xs-3">
                <a class="btn-del-all btn btn-lg btn-default btn-block navbar-btn"><i class="glyphicon glyphicon-trash text-danger"></i></a>
            </div>
            <div class="col-xs-3">
                <a class="btn-obs btn btn-lg btn-default btn-block navbar-btn" title="@CreateRes.Observacoes"><i class="glyphicon glyphicon-list text-primary"></i></a>
            </div>
        </div>
    </div>
    <div id="pedido-actions" class="container-fluid">
        <div class="col-xs-6">
            <a id="btn-voltar" class="btn btn-lg btn-default btn-block navbar-btn" title="@CreateRes.Voltar"><i class="glyphicon glyphicon-circle-arrow-left"></i> <span class="hidden-xs">@CreateRes.Voltar1</span><small class="visible-xs">@CreateRes.Voltar2</small></a>
        </div>
        <div class="col-xs-6">
            <a id="btn-pedido-review" href="Review" class="btn btn-lg btn-success btn-block navbar-btn" title="@CreateRes.RevisarPedido"><i class="glyphicon glyphicon-check"></i> <span class="hidden-xs">@CreateRes.Revisar</span><small class="visible-xs">@CreateRes.Revisar1</small></a>
        </div>
    </div>
</nav>

