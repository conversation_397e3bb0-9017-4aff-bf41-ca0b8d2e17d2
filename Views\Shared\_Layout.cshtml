﻿@using Consumer.Resources.Mobile.Comanda.App.Shared;

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="description" content="@_LayoutRes.Title">
    <meta name="keywords" content="consumer mobile, cardapio digital, cardapio qr-code, comanda mobile">
    <title>Consumer Mobile</title>
    <!-- =============== VENDOR STYLES ===============-->
    @Styles.Render("~/bundles/fontawesome")
    @Styles.Render("~/bundles/simpleLineIcons")
    @Styles.Render("~/bundles/animatecss")
    @Styles.Render("~/bundles/whirl")
    <!-- =============== PAGE VENDOR STYLES =============== -->
    @if (IsSectionDefined("Styles"))
    {
        @RenderSection("Styles", required: false)
    }
    <!-- =============== BOOTSTRAP STYLES ===============-->
    <link href="@Styles.Url("~/Content/app/css/bootstrap.css")" rel="stylesheet" type="text/css" id="bscss" />
    <!-- =============== APP STYLES ===============-->
    <link href="@Styles.Url("~/Content/app/css/app.css")" rel="stylesheet" type="text/css" id="maincss" />
    <link href="@Styles.Url("~/Content/mvc-override.css")" rel="stylesheet" type="text/css" />
    <link href="@Styles.Url("~/Content/app/css/theme-d.css")" rel="stylesheet" type="text/css" />
</head>
<body>
    <div class="wrapper">
        <!-- top navbar-->
        <header class="topnavbar-wrapper">
            @Html.Partial("_TopNavbar")
        </header>
        <!-- sidebar-->
        <aside class="aside">
            @Html.Action("_Sidebar", "Home")
        </aside>
        <!-- offsidebar-->
        <aside class="offsidebar hide">
            @*@Html.Partial("_Offsidebar")*@
        </aside>
        <!-- Main section-->
        <section>
            <!-- Page content-->
            <div class="content-wrapper">
                @RenderBody()
            </div>
        </section>
        <!-- Page footer-->
        <footer>
            @Html.Partial("_Footer")
        </footer>
    </div>
    @if (IsSectionDefined("BodyArea"))
    {@RenderSection("BodyArea", required: false)}
    <!-- =============== VENDOR SCRIPTS ===============-->
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/bundles/matchMedia")
    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
    @Scripts.Render("~/bundles/storage")
    @Scripts.Render("~/bundles/jqueryEasing")
    @Scripts.Render("~/bundles/animo")
    @Scripts.Render("~/bundles/slimscroll")
    @Scripts.Render("~/bundles/screenfull")
    @Scripts.Render("~/bundles/localize")
    <!-- =============== PAGE VENDOR SCRIPTS ===============-->
    @RenderSection("scripts", required: false)
    <!-- =============== APP SCRIPTS ===============-->
    @Scripts.Render("~/bundles/Angle")
</body>
</html>