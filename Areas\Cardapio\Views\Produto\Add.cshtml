﻿@using Consumer.Resources.Mobile.Cardapio.Views.Produto
@model RAL.Consumer.Mobile.Models.ItemPedidoTamanhoViewModels

@{
    ViewBag.Title = @AddRes.Adicionar;
    Layout = null;
    var selected = Model.TodosTamanhos != null ? Model.TodosTamanhos.FirstOrDefault() : null;
    bool naoPodeSelecionar = false;
}

<style>
    .modalBody {
        margin-top: 80px;
    }

        .modalBody h4 {
            border: none;
        }
</style>

@using (Ajax.BeginForm(new AjaxOptions { HttpMethod = "POST", OnBegin = "OnBeginComboAdd", OnSuccess = "OnSuccessAdd" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true)

    @Html.HiddenFor(m => m.CodigoTipoProduto)
    @Html.HiddenFor(m => m.NomeProdutoPersonalizado)
    if (Model.CobrarPeloMaiorValor)
    {
        @Html.Hidden("CobrarPeloMaiorValor", true)
    }

    <div class="modal-header">
        <div>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <label class="label label-warning">@Model.NomeCategoria</label>
        </div>
        <span id="PrecoTotal" class="text-success pull-right product-price">@Html.DisplayFor(model => model.PrecoVenda)@(Model.PorKgOuLt ? "/" + Model.UnidadeComercializacao : string.Empty)</span>
        <h4 class="modal-title product-title" id="produtoModalNome">
            @Model.NomeProduto
        </h4>
        <p class="small mb0 hide" style="line-height: 1.2" id="resumo"></p>
    </div>
    <div class="modal-body">
        @if (ViewBag.ExibirImagemDetalhe == true)
        {
            <div class="row" style="margin-top: 79px;">
                @if (System.IO.File.Exists(Server.MapPath(string.Format("~/Content/images/cardapio/{0}.jpg", Model.CodigoProduto))))
                {
                    <img id="produtoModalImagePath" src="@Url.Content(string.Format("~/Content/images/cardapio/{0}.jpg?q={1}", Model.CodigoProduto, DateTime.Now.Ticks.ToString()))" alt="Foto" class="img-responsive center-block" />
                }
                else
                {
                    <img id="produtoModalImagePath" src="@Url.Content(string.Format("~/Content/images/cardapio/{0}", ViewBag.ImagemPadrao))" alt="Foto" class="img-responsive center-block" />
                }
            </div>
        }
        @if (ViewBag.ExibirDescricaoDetalhe == true)
        {
            <div class="row">
                <div class="col-xs-12">
                    <div class="panel-body text-justify">
                        <p id="produtoModalDescricao" class="product-description">
                            @Model.DescricaoProduto
                        </p>
                    </div>
                </div>
            </div>
        }

        @if (Model.ComboItens != null && Model.ComboItens.Count() > 0)
        {
            <h4 class="bb">@AddRes.ItensCombo</h4>
            <div>
                @foreach (var comboItem in Model.ComboItens.Select((value, i) => new { i, value }))
                {
                    <h4>@(comboItem.i + 1). @AddRes.Escolha @comboItem.value.Nome</h4>

                    @Html.HiddenFor(m => m.ComboItens[comboItem.i].Codigo)
                    @Html.ValidationMessageFor(m => m.ComboItens[comboItem.i].Codigo)
                    @*@Html.ValidationMessageFor(m => m.ComboItens[comboItem.i].OpcaoSelecionada.Produto.Codigo)*@

                    <div class="comboOpcoesList">

                        @if (comboItem.value.TodasOpcoes.Count() == 0)
                        {
                            naoPodeSelecionar = true;
                            <div class="alert alert-warning">
                                <i class="fa fa-warning"></i> @AddRes.Indisp
                            </div>
                        }
                        else
                        {
                            foreach (var opcaoItem in comboItem.value.TodasOpcoes.Select((value, j) => new { j, value }))
                            {
                                @*@Html.RadioButtonFor(m => m.ComboItens[comboItem.i].OpcaoSelecionada.Codigo, opcaoItem.Codigo, new { id = "ci-" + comboItem.i + "-" + opcaoItem.Codigo, @class = "pull-right" })
                                    @Html.LabelFor(m => m.ComboItens[comboItem.i].OpcaoSelecionada.Codigo, opcaoItem.Descricao, new { style = "width: 100%", @for = "ci-" + comboItem.i + "-" + opcaoItem.Codigo })*@
                                <div class="checkbox c-checkbox clearfix mb-lg mt-lg">
                                    <label for="ci-@<EMAIL>">

                                        @Html.RadioButtonFor(m => m.ComboItens[comboItem.i].OpcaoSelecionada.Produto.Codigo, opcaoItem.value.Produto.Codigo, new { id = "ci-" + comboItem.i + "-" + opcaoItem.value.Produto.Codigo, @data_indexComboItem = comboItem.i, @data_idComboItem = comboItem.value.Codigo, @data_load = Url.Action("ComplementosObservacoesCombo") })

                                        @*else
                                            {
                                                @Html.RadioButtonFor(m => m.ComboItens[comboItem.i].OpcaoSelecionada.Produto.Codigo, opcaoItem.Produto.Codigo, new { id = "ci-" + comboItem.i + "-" + opcaoItem.Produto.Codigo, @checked = "checked" })
                                            }*@
                                        <span class="fa fa-check"></span>
                                    </label>
                                    @if (opcaoItem.value.Produto.Qtd > 1)
                                    {
                                        <i>@(opcaoItem.value.Produto.Qtd)x @opcaoItem.value.Produto.Descricao</i>
                                    }
                                    else
                                    {
                                        <i>@opcaoItem.value.Produto.Descricao</i>
                                    }
                                </div>
                            }
                        }
                    </div>

                    <div id="accordionComboItem@(comboItem.value.Codigo)" role="tablist" aria-multiselectable="true" class="panel-group">
                    </div>
                }
            </div>
        }
        @Html.HiddenFor(model => model.CodigoProdutoDetalhe)
        @Html.HiddenFor(model => model.CodigoProduto)
        @Html.HiddenFor(model => model.PrecoVenda)
        @Html.Hidden("NomeProduto", Model.NomeProduto)

        @if (Model.TodosTamanhos != null && Model.TodosTamanhos.Count() > 0)
        {
            <h4 class="bb">
                @AddRes.SelecioneTam
            </h4>
            for (int i = 0; i < Model.TodosTamanhos.Count; i++)
            {
                var g = Model.TodosTamanhos;
                <div class="radio c-radio clearfix mb-lg mt-lg">
                    <label for="tam-@g[i].Codigo" style="width: 100%">
                        <input data-msg="@AddRes.SelecioneTam"
                               required
                               type="radio"
                               name="TodosTamanhos[0].Codigo"
                               value="@g[i].Codigo"
                               id="tam-@g[i].Codigo" data-nome="@g[i].Descricao"
                               data-codigotamanho="@g[i].CodigoTamanho" data-sigla="@g[i].Sigla" data-qtdmaximapartes="@g[i].QtdMaximaPartes"
                               data-precovenda="@g[i].Valor.ToString("N", System.Globalization.CultureInfo.InvariantCulture)" data-wizard="@(Json.Encode(g[i].HasWizard || g[i].QtdMaximaPartes > 2 ))">
                        <span class="fa fa-check"></span>@g[i].Descricao <label class="pull-right">@g[i].Valor.ToString("C")</label>
                    </label>
                </div>
            }
            @Html.ValidationMessage("TodosTamanhos[0].Codigo", new { @class = "text-danger" })
        }
        <div id="accordion" role="tablist" aria-multiselectable="true" class="panel-group" @(Model.TodosTamanhos != null && Model.TodosTamanhos.Count() > 0 ? " style=display:none;" : "")>
            @if (Model.TodasPartes != null && Model.TodasPartes.Count() > 0)
            {
                <div class="panel panel-danger@(selected.QtdMaximaPartes - 1 == 0 ? " hide" : string.Empty)" id="todasPartes">
                    <div id="headingOne" role="tab" class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion" href="#collapseSelectedPartes" aria-expanded="false" aria-controls="collapseSelectedPartes" class="collapsed btn-block">Meio a Meio <span class="collapse-icon pull-right"><i class="fa fa-chevron-up"></i><i class="fa fa-chevron-down"></i></span></a>
                        </h4>
                    </div>
                    <div id="collapseSelectedPartes" role="tabpanel" aria-labelledby="headingOne" class="panel-collapse collapse" aria-expanded="false" style="height: 0px;">
                        <div class="panel-body">
                            @* Inteira *@
                            <div class="radio c-radio clearfix mb-lg mt-lg">
                                <label for="parte-inteira" style="width: 100%">
                                    <input type="radio" name="TodasPartes[0].Codigo" id="parte-inteira" value="0" data-precovenda="0" checked="checked">
                                    <span class="fa fa-check"></span>Inteira
                                </label>
                            </div>
                            @for (int i = 1; i < Model.TodasPartes.Count; i++)
                            {
                                var g = Model.TodasPartes;
                                <div class="radio c-radio clearfix mb-lg mt-lg parte parte-@g[i].CodigoTamanho @(g[i].CodigoTamanho != selected.CodigoTamanho ? "hide" : "")">
                                    <label for="parte-@g[i].Codigo" style="width: 100%">
                                        <input type="radio" name="TodasPartes[0].Codigo" value="@g[i].Codigo" id="parte-@g[i].Codigo" data-nome="@g[i].Descricao" data-precovenda="@g[i].Valor.ToString("N", System.Globalization.CultureInfo.InvariantCulture)" @(i == 0 ? " checked=checked" : "")>
                                        <span class="fa fa-check"></span>@g[i].Descricao <label class="pull-right">@g[i].Valor.ToString("C")</label>
                                    </label>
                                </div>
                            }
                            @if (Model.CobrarPeloMaiorValor)
                            {
                                <p class="small"><i>@AddRes.MaiorValor</i></p>
                            }
                        </div>
                    </div>
                </div>
            }
            @if (Model.CodigoTipoProduto != 4 && Model.TodosComplementos != null && Model.TodosComplementos.Count() > 0)
            {
                <div class="panel panel-danger">
                    <div id="headingTwo" role="tab" class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion" href="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo" class="collapsed btn-block">@AddRes.Complementos<span class="collapse-icon pull-right"><i class="fa fa-chevron-up"></i><i class="fa fa-chevron-down"></i></span></a>
                        </h4>
                    </div>
                    <div id="collapseTwo" role="tabpanel" aria-labelledby="headingTwo" class="panel-collapse collapse" aria-expanded="false" style="height: 0px;">
                        <div class="panel-body">
                            <div class="mb-lg meioameio hide">
                                <span title="@AddRes.MetadePrincipal" class="fa fa-adjust fa-rotate-180" style="font-size: 20px; display: inline-block; vertical-align: top; width: 20px; height: 20px; margin-right: 5px; text-align: center;"></span>
                                <span title="@AddRes.PizzaInteira" class="fa fa-circle" style="font-size: 20px; display: inline-block; vertical-align: top; width: 20px; height: 20px; margin-right: 5px; text-align: center;"></span>
                                <span title="@AddRes.OutraMetade" class="fa fa-adjust" style="font-size: 20px; display: inline-block; vertical-align: top; width: 20px; height: 20px; margin-right: 5px; text-align: center;"></span>
                            </div>

                            <div id="complementosProdutoTamanho">
                                @for (int i = 0; i < Model.TodosComplementos.Count(); i++)
                                {
                                    var g = Model.TodosComplementos;
                                    <div class="checkbox c-checkbox clearfix mb-lg">
                                        <label for="com1-@g[i].Codigo" class="meioameio hide">
                                            <input type="checkbox" name="SelectedComplementosParte[0][@i].Codigo" value="@g[i].Codigo" id="com1-@g[i].Codigo" data-precovenda="@g[i].Valor.ToString("N", System.Globalization.CultureInfo.InvariantCulture)">
                                            <span class="fa fa-check"></span>
                                        </label>
                                        <label for="com-@g[i].Codigo">
                                            <input type="checkbox" name="SelectedComplementos[@i].Codigo" value="@g[i].Codigo" id="com-@g[i].Codigo" data-precovenda="@g[i].Valor.ToString("N", System.Globalization.CultureInfo.InvariantCulture)">
                                            <span class="fa fa-check"></span>
                                        </label>
                                        <label for="com2-@g[i].Codigo" class="meioameio hide">
                                            <input type="checkbox" name="SelectedComplementosParte[1][@i].Codigo" value="@g[i].Codigo" id="com2-@g[i].Codigo" data-precovenda="@g[i].Valor.ToString("N", System.Globalization.CultureInfo.InvariantCulture)">
                                            <span class="fa fa-check"></span>
                                        </label>
                                        <i>@g[i].Descricao</i><i class="pull-right">+@g[i].Valor.ToString("C")</i>
                                    </div>
                                    <input type="hidden" name="SelectedComplementos[@i].Qtd" value="1" />
                                    <input type="hidden" name="SelectedComplementosParte[0][@i].Qtd" value="1" />
                                    <input type="hidden" name="SelectedComplementosParte[1][@i].Qtd" value="1" />
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }
            @if (Model.CodigoTipoProduto != 4 && Model.TodasObservacoes.Count() > 0)
            {
                <div class="panel panel-danger">
                    <div id="headingThree" role="tab" class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#accordion" href="#collapseThree" aria-expanded="false" aria-controls="collapseThree" class="collapsed btn-block">@AddRes.Observacoes<span class="collapse-icon pull-right"><i class="fa fa-chevron-up"></i><i class="fa fa-chevron-down"></i></span></a>
                        </h4>
                    </div>
                    <div id="collapseThree" role="tabpanel" aria-labelledby="headingThree" class="panel-collapse collapse" aria-expanded="false">
                        <div class="panel-body">
                            <div class="mb-lg meioameio hide">
                                <span title="@AddRes.MetadePrincipal" class="fa fa-adjust fa-rotate-180" style="font-size: 20px; display: inline-block; vertical-align: top; width: 20px; height: 20px; margin-right: 5px; text-align: center;"></span>
                                <span title="@AddRes.PizzaInteira" class="fa fa-circle" style="font-size: 20px; display: inline-block; vertical-align: top; width: 20px; height: 20px; margin-right: 5px; text-align: center;"></span>
                                <span title="@AddRes.OutraMetade" class="fa fa-adjust" style="font-size: 20px; display: inline-block; vertical-align: top; width: 20px; height: 20px; margin-right: 5px; text-align: center;"></span>
                            </div>
                            @foreach (var item in Model.TodasObservacoes)
                            {
                                <div class="checkbox c-checkbox clearfix mb-lg mt-lg">
                                    <label for="<EMAIL>" class="meioameio hide">
                                        <input type="checkbox" name="SelectedObservacoesParte[0]" value="@item.Chave" id="<EMAIL>">
                                        <span class="fa fa-check"></span>
                                    </label>
                                    <label for="<EMAIL>">
                                        <input type="checkbox" name="SelectedObservacoes" value="@item.Chave" id="<EMAIL>">
                                        <span class="fa fa-check"></span>
                                    </label>
                                    <label for="<EMAIL>" class="meioameio hide">
                                        <input type="checkbox" name="SelectedObservacoesParte[1]" value="@item.Chave" id="<EMAIL>">
                                        <span class="fa fa-check"></span>
                                    </label>
                                    <i>@item.Descricao</i>
                                </div>
                            }
                            <input type="hidden" name="SelectedObservacoesParte[0]" value="0" />
                            <input type="hidden" name="SelectedObservacoesParte[1]" value="0" />
                        </div>
                    </div>
                </div>
            }

            @if (Model.CodigoTipoProduto != (int)RAL.Common.ProdutoTipos.Combo)
            {
                <h4 class="bb">
                    @AddRes.Quantidade
                </h4>
                <div class="form-group">
                    <div class="input-group input-group-lg">
                        <input class="form-control text-left" data-val="true" data-val-number="@AddRes.Numero" id="Quantidade" name="Quantidade" type="number" value="1" aria-describedby="Quantidade-error" aria-invalid="false" data-val-range-min="1" data-val-range-max="9">
                        <span class="input-group-btn">
                            <button type="button" class="btn btn-default btn-minus" style="border-radius: 0">
                                <span class="fa fa-minus"></span>
                            </button>
                        </span>
                        <span class="input-group-btn">
                            <button type="button" class="btn btn-default btn-plus">
                                <span class="fa fa-plus"></span>
                            </button>
                        </span>
                    </div>
                    @Html.ValidationMessageFor(model => model.Quantidade, "", new { @class = "text-danger" })
                </div>
            }

            @*@if (Model.Produto.NomeTipoProduto != "Combo")*@
            @if (Model.CodigoTipoProduto != (int)RAL.Common.ProdutoTipos.ProdutoTamanho)
            {
                <div class="panel panel-danger">
                    <div id="headingObs" role="tab" class="panel-heading">
                        <h4 class="panel-title">
                            @AddRes.AlgumaObservacao
                        </h4>
                    </div>
                    <div class="panel-body">
                        <div>
                            <span class="pull-right text-muted count_message">0 / 140</span>
                            @Html.TextAreaFor(m => m.ObservacoesAdicionais, new { @class = "form-control mb contar-texto", maxlength = "140", placeholder = AddRes.AddObs })
                            @Html.ValidationMessageFor(model => model.ObservacoesAdicionais, "", new { @class = "text-danger" })
                        </div>
                        <div>
                            <span class="text-muted small">@AddRes.AlterarValor</span><br />
                        </div>
                    </div>
                </div>
            }

            @if (ViewBag.HabilitarPedidos == false)
            {
                <small class="font-italic">@AddRes.ChameGarcom</small>
            }
        </div>
    </div>
    <div class="modal-footer">
        <div class="row">
            @if (ViewBag.HabilitarPedidos == true && Request.IsAuthenticated && CurrentUserCardapio.Role.Equals("Cliente"))
            {
                <div class="col-xs-6">
                    <button type="button" class="btn btn-lg btn-block btn-square btn-default" data-dismiss="modal"><i class="fa fa-arrow-left"></i><br />@AddRes.Voltar</button>
                </div>
                <div class="col-xs-6">
                    @if ((Model.Indisponivel && ViewBag.PermiteEstoqueNegativoProdutos == false) || naoPodeSelecionar)
                    {
                        <a href="#" class="btn btn-lg btn-block btn-square btn-warning" disabled=""><i class="fa fa-times"></i><br />@AddRes.Indisp</a>
                    }
                    else
                    {
                        <button type="submit" class="btn btn-lg btn-block btn-square btn-success"><i class="fa fa-plus"></i><br />@AddRes.Adicionar</button>
                    }
                </div>
            }
            else
            {
                <div class="col-xs-4">
                    <button type="button" class="btn btn-lg btn-block btn-square btn-default btn-irpara" data-irpara="-1"><i class="fa fa-chevron-left" style="line-height: 46px"></i></button>
                </div>
                <div class="col-xs-4">
                    <button type="button" class="btn btn-lg btn-block btn-square btn-default" data-dismiss="modal" style="line-height: 46px">@AddRes.Voltar</button>
                </div>
                <div class="col-xs-4">
                    <button type="button" class="btn btn-lg btn-block btn-square btn-default btn-irpara" data-irpara="+1"><i class="fa fa-chevron-right" style="line-height: 46px"></i></button>
                </div>
            }
        </div>
    </div>
}