﻿@using Consumer.Resources.Mobile.Comanda.Views.Pedido
@model RAL.Consumer.Mobile.Models.AddComboViewModel
@using RAL.Consumer.Data.Context
@{
    ViewBag.Title = Model.NomeProdutoExtendido;
}

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true)

    @Html.HiddenFor(model => model.CodigoProduto)
    @Html.HiddenFor(model => model.CodigoProdutoDetalhe)
    @Html.HiddenFor(model => model.CodigoEtiqueta)


    <div>
        @foreach (var comboItem in Model.ComboItens.Select((value, i) => new { i, value }))
        {
            <h4 class="text-nowrap" style="overflow: hidden; text-overflow: ellipsis">@(comboItem.i + 1). @<EMAIL></h4>
            @Html.ValidationMessageFor(m => m.ComboItens[comboItem.i].OpcaoSelecionada.Produto.Codigo)
            @Html.HiddenFor(m => m.ComboItens[comboItem.i].Codigo)

            if (comboItem.value.TodasOpcoes.Count == 0)
            {
                Model.NaoPodeSelecionar = true;

                <div class="alert alert-warning">
                    <i class="glyphicon glyphicon-warning-sign"></i>@AddComboRes.SemOpcoes
                </div>
            }
            else
            {
                <ul class="list-group">
                    @foreach (var opcaoItem in comboItem.value.TodasOpcoes)
                    {
                        <li class="list-group-item list-group-item-info">
                            @Html.RadioButtonFor(m => m.ComboItens[comboItem.i].OpcaoSelecionada.Produto.Codigo, opcaoItem.Produto.Codigo, new { id = "ci-" + comboItem.i + "-" + opcaoItem.Produto.Codigo, @class = "pull-right" })
                            @Html.LabelFor(m => m.ComboItens[comboItem.i].OpcaoSelecionada.Produto.Codigo, opcaoItem.Produto.Descricao, new { style = "width: 100%", @for = "ci-" + comboItem.i + "-" + opcaoItem.Produto.Codigo })
                        </li>
                    }
                </ul>
            }
        }
    </div>

    <nav id="actions" class="navbar navbar-default navbar-fixed-bottom">
        <div id="item-pedido-actions" class="container-fluid">
            <div class="col-xs-6">
                <a id="btn-voltar" class="btn btn-lg btn-default btn-block navbar-btn" title="@AddComboRes.Voltar"><i class="glyphicon glyphicon-circle-arrow-left"></i> <span class="hidden-xs">@AddComboRes.Voltar</span><small class="visible-xs">@AddComboRes.Voltar</small></a>
            </div>
            @if (Model.NaoPodeSelecionar == false)
            {
                <div class="col-xs-6">
                    <button class="btn btn-lg btn-success btn-block navbar-btn" title="@AddComboRes.Ok"><i class="glyphicon glyphicon-check"></i> <span class="hidden-xs">@AddComboRes.Ok</span><small class="visible-xs">@AddComboRes.Ok</small></button>
                </div>
            }
        </div>
    </nav>
}