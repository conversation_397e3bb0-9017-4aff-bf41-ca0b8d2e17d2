﻿@using Consumer.Resources.Mobile.Comanda.App.Home;

@{
    ViewBag.Title = @IndexRes.CardapioQr;
    Layout = "~/Views/Shared/_LayoutHorizontal.cshtml";
}

@if (ViewBag.HabilitarPedidos)
{
    <h4>@IndexRes.Display</h4>

    <img src="~/Images/display-cardapio.jpg" class="img-responsive center-block" style="height: 300px;">
}
else
{
    <p class="text-center">
        <a href="@Url.Action("Categoria", "Produto", new { area = "Cardapio" })" class="btn btn-lg btn-success">@IndexRes.VerCardapio</a>
    </p>
}

@if (Request.Url.Host.ToLower().Equals("localhost") || Request.QueryString["a"] != null)
{
    <p class="text-center">
        <a href="@Url.Action("QRcode", "Home")" class="btn btn-lg btn-danger">@IndexRes.VerQr</a>
        <a href="@Url.Action("Index", "Home", new { area = "Comanda" })" class="btn btn-lg btn-primary">@IndexRes.ComandaMobile</a>
    </p>
}
