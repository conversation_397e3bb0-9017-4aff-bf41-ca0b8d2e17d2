<!-- START Off Sidebar (right)-->
<nav>
   <div role="tabpanel">
      <!-- Nav tabs-->
      <ul role="tablist" class="nav nav-tabs nav-justified">
         <li role="presentation" class="active">
            <a href="#app-settings" aria-controls="app-settings" role="tab" data-toggle="tab">
               <em class="icon-equalizer fa-lg"></em>
            </a>
         </li>
         <li role="presentation">
            <a href="#app-chat" aria-controls="app-chat" role="tab" data-toggle="tab">
               <em class="icon-user fa-lg"></em>
            </a>
         </li>
      </ul>
      <!-- Tab panes-->
      <div class="tab-content">
         <div id="app-settings" role="tabpanel" class="tab-pane fade in active">
            <h3 class="text-center text-thin">Settings</h3>
            <div class="p">
               <h4 class="text-muted text-thin">Themes</h4>
               <div class="table-grid mb">
                  <div class="col mb">
                     <div class="setting-color">
                         <label data-load-css="/Content/app/css/theme-a.css">
                             <input type="radio" name="setting-theme" checked="checked" />
                             <span class="icon-check"></span>
                             <span class="split">
                                 <span class="color bg-info"></span>
                                 <span class="color bg-info-light"></span>
                             </span>
                             <span class="color bg-white"></span>
                         </label>
                     </div>
                  </div>
                  <div class="col mb">
                     <div class="setting-color">
                         <label data-load-css="/Content/app/css/theme-b.css">
                             <input type="radio" name="setting-theme" />
                             <span class="icon-check"></span>
                             <span class="split">
                                 <span class="color bg-green"></span>
                                 <span class="color bg-green-light"></span>
                             </span>
                             <span class="color bg-white"></span>
                         </label>
                     </div>
                  </div>
                  <div class="col mb">
                     <div class="setting-color">
                         <label data-load-css="/Content/app/css/theme-c.css">
                             <input type="radio" name="setting-theme" />
                             <span class="icon-check"></span>
                             <span class="split">
                                 <span class="color bg-purple"></span>
                                 <span class="color bg-purple-light"></span>
                             </span>
                             <span class="color bg-white"></span>
                         </label>
                     </div>
                  </div>
                  <div class="col mb">
                     <div class="setting-color">
                         <label data-load-css="/Content/app/css/theme-d.css">
                             <input type="radio" name="setting-theme" />
                             <span class="icon-check"></span>
                             <span class="split">
                                 <span class="color bg-danger"></span>
                                 <span class="color bg-danger-light"></span>
                             </span>
                             <span class="color bg-white"></span>
                         </label>
                     </div>
                  </div>
               </div>
               <div class="table-grid mb">
                  <div class="col mb">
                     <div class="setting-color">
                         <label data-load-css="/Content/app/css/theme-e.css">
                             <input type="radio" name="setting-theme" />
                             <span class="icon-check"></span>
                             <span class="split">
                                 <span class="color bg-info-dark"></span>
                                 <span class="color bg-info"></span>
                             </span>
                             <span class="color bg-gray-dark"></span>
                         </label>
                     </div>
                  </div>
                  <div class="col mb">
                     <div class="setting-color">
                         <label data-load-css="/Content/app/css/theme-f.css">
                             <input type="radio" name="setting-theme" />
                             <span class="icon-check"></span>
                             <span class="split">
                                 <span class="color bg-green-dark"></span>
                                 <span class="color bg-green"></span>
                             </span>
                             <span class="color bg-gray-dark"></span>
                         </label>
                     </div>
                  </div>
                  <div class="col mb">
                     <div class="setting-color">
                         <label data-load-css="/Content/app/css/theme-g.css">
                             <input type="radio" name="setting-theme" />
                             <span class="icon-check"></span>
                             <span class="split">
                                 <span class="color bg-purple-dark"></span>
                                 <span class="color bg-purple"></span>
                             </span>
                             <span class="color bg-gray-dark"></span>
                         </label>
                     </div>
                  </div>
                  <div class="col mb">
                     <div class="setting-color">
                         <label data-load-css="/Content/app/css/theme-h.css">
                             <input type="radio" name="setting-theme" />
                             <span class="icon-check"></span>
                             <span class="split">
                                 <span class="color bg-danger-dark"></span>
                                 <span class="color bg-danger"></span>
                             </span>
                             <span class="color bg-gray-dark"></span>
                         </label>
                     </div>
                  </div>
               </div>
            </div>
             <div class="p">
                 <h4 class="text-muted text-thin">Layout</h4>
                 <div class="clearfix">
                     <p class="pull-left">Fixed</p>
                     <div class="pull-right">
                         <label class="switch">
                             <input id="chk-fixed" type="checkbox" data-toggle-state="layout-fixed">
                             <span></span>
                         </label>
                     </div>
                 </div>
                 <div class="clearfix">
                     <p class="pull-left">Boxed</p>
                     <div class="pull-right">
                         <label class="switch">
                             <input id="chk-boxed" type="checkbox" data-toggle-state="layout-boxed">
                             <span></span>
                         </label>
                     </div>
                 </div>
                 <div class="clearfix">
                     <p class="pull-left">RTL</p>
                     <div class="pull-right">
                         <label class="switch">
                             <input id="chk-rtl" type="checkbox">
                             <span></span>
                         </label>
                     </div>
                 </div>
             </div>
             <div class="p">
                 <h4 class="text-muted text-thin">Aside</h4>
                 <div class="clearfix">
                     <p class="pull-left">Collapsed</p>
                     <div class="pull-right">
                         <label class="switch">
                             <input id="chk-collapsed" type="checkbox" data-toggle-state="aside-collapsed">
                             <span></span>
                         </label>
                     </div>
                 </div>
                 <div class="clearfix">
                     <p class="pull-left">Collapsed Text</p>
                     <div class="pull-right">
                         <label class="switch">
                             <input id="chk-collapsed-text" type="checkbox" data-toggle-state="aside-collapsed-text">
                             <span></span>
                         </label>
                     </div>
                 </div>
                 <div class="clearfix">
                     <p class="pull-left">Float</p>
                     <div class="pull-right">
                         <label class="switch">
                             <input id="chk-float" type="checkbox" data-toggle-state="aside-float">
                             <span></span>
                         </label>
                     </div>
                 </div>
                 <div class="clearfix">
                     <p class="pull-left">Hover</p>
                     <div class="pull-right">
                         <label class="switch">
                             <input id="chk-hover" type="checkbox" data-toggle-state="aside-hover">
                             <span></span>
                         </label>
                     </div>
                 </div>
                 <div class="clearfix">
                     <p class="pull-left">Show Scrollbar</p>
                     <div class="pull-right">
                         <label class="switch">
                             <input id="chk-hover" type="checkbox" data-toggle-state="show-scrollbar" data-target=".sidebar">
                             <span></span>
                         </label>
                     </div>
                 </div>
             </div>
         </div>
         <div id="app-chat" role="tabpanel" class="tab-pane fade">
            <h3 class="text-center text-thin">Connections</h3>
            <ul class="nav">
               <!-- START list title-->
               <li class="p">
                  <small class="text-muted">ONLINE</small>
               </li>
               <!-- END list title-->
               <li>
                  <!-- START User status-->
                  <a href="#" class="media-box p mt0">
                     <span class="pull-right">
                        <span class="circle circle-success circle-lg"></span>
                     </span>
                     <span class="pull-left">
                        <!-- Contact avatar-->
                        <img src="~/Content/Images/user/05.jpg" alt="Image" class="media-box-object img-circle thumb48" />
                     </span>
                     <!-- Contact info-->
                     <span class="media-box-body">
                        <span class="media-box-heading">
                           <strong>Juan Sims</strong>
                           <br/>
                           <small class="text-muted">Designeer</small>
                        </span>
                     </span>
                  </a>
                  <!-- END User status-->
                  <!-- START User status-->
                  <a href="#" class="media-box p mt0">
                     <span class="pull-right">
                        <span class="circle circle-success circle-lg"></span>
                     </span>
                     <span class="pull-left">
                        <!-- Contact avatar-->
                        <img src="~/Content/Images/user/06.jpg" alt="Image" class="media-box-object img-circle thumb48" />
                     </span>
                     <!-- Contact info-->
                     <span class="media-box-body">
                        <span class="media-box-heading">
                           <strong>Maureen Jenkins</strong>
                           <br/>
                           <small class="text-muted">Designeer</small>
                        </span>
                     </span>
                  </a>
                  <!-- END User status-->
                  <!-- START User status-->
                  <a href="#" class="media-box p mt0">
                     <span class="pull-right">
                        <span class="circle circle-danger circle-lg"></span>
                     </span>
                     <span class="pull-left">
                        <!-- Contact avatar-->
                        <img src="~/Content/Images/user/07.jpg" alt="Image" class="media-box-object img-circle thumb48" />
                     </span>
                     <!-- Contact info-->
                     <span class="media-box-body">
                        <span class="media-box-heading">
                           <strong>Billie Dunn</strong>
                           <br/>
                           <small class="text-muted">Designeer</small>
                        </span>
                     </span>
                  </a>
                  <!-- END User status-->
                  <!-- START User status-->
                  <a href="#" class="media-box p mt0">
                     <span class="pull-right">
                        <span class="circle circle-warning circle-lg"></span>
                     </span>
                     <span class="pull-left">
                        <!-- Contact avatar-->
                        <img src="~/Content/Images/user/08.jpg" alt="Image" class="media-box-object img-circle thumb48" />
                     </span>
                     <!-- Contact info-->
                     <span class="media-box-body">
                        <span class="media-box-heading">
                           <strong>Tomothy Roberts</strong>
                           <br/>
                           <small class="text-muted">Designer</small>
                        </span>
                     </span>
                  </a>
                  <!-- END User status-->
               </li>
               <!-- START list title-->
               <li class="p">
                  <small class="text-muted">OFFLINE</small>
               </li>
               <!-- END list title-->
               <li>
                  <!-- START User status-->
                  <a href="#" class="media-box p mt0">
                     <span class="pull-right">
                        <span class="circle circle-lg"></span>
                     </span>
                     <span class="pull-left">
                        <!-- Contact avatar-->
                        <img src="~/Content/Images/user/09.jpg" alt="Image" class="media-box-object img-circle thumb48" />
                     </span>
                     <!-- Contact info-->
                     <span class="media-box-body">
                        <span class="media-box-heading">
                           <strong>Lawrence Robinson</strong>
                           <br/>
                           <small class="text-muted">Developer</small>
                        </span>
                     </span>
                  </a>
                  <!-- END User status-->
                  <!-- START User status-->
                  <a href="#" class="media-box p mt0">
                     <span class="pull-right">
                        <span class="circle circle-lg"></span>
                     </span>
                     <span class="pull-left">
                        <!-- Contact avatar-->
                        <img src="~/Content/Images/user/10.jpg" alt="Image" class="media-box-object img-circle thumb48" />
                     </span>
                     <!-- Contact info-->
                     <span class="media-box-body">
                        <span class="media-box-heading">
                           <strong>Tyrone Owens</strong>
                           <br/>
                           <small class="text-muted">Designer</small>
                        </span>
                     </span>
                  </a>
                  <!-- END User status-->
               </li>
               <li>
                  <div class="p-lg text-center">
                     <!-- Optional link to list more users-->
                     <a href="#" title="See more contacts" class="btn btn-purple btn-sm">
                        <strong>Load more..</strong>
                     </a>
                  </div>
               </li>
            </ul>
            <!-- Extra items-->
            <div class="p">
               <p>
                  <small class="text-muted">Tasks completion</small>
               </p>
               <div class="progress progress-xs m0">
                  <div role="progressbar" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100" class="progress-bar progress-bar-success progress-80">
                     <span class="sr-only">80% Complete</span>
                  </div>
               </div>
            </div>
            <div class="p">
               <p>
                  <small class="text-muted">Upload quota</small>
               </p>
               <div class="progress progress-xs m0">
                  <div role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" class="progress-bar progress-bar-warning progress-40">
                     <span class="sr-only">40% Complete</span>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</nav>
<!-- END Off Sidebar (right)-->