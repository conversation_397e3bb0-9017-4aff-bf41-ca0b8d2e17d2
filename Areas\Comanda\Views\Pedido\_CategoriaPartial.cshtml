﻿@using Consumer.Resources.Mobile.Comanda.Views.Pedido
@model IEnumerable<RAL.Common.ItemExibicaoInt32>


@foreach (var item in Model)
{
    <div class="col-xs-6" style="padding-bottom: 15px">
        <a class="item-categoria btn btn-warning btn-block" data-codigo="@item.Chave">
            <span class="item-categoria-nome"><strong>@item.Descricao</strong></span>
        </a>
    </div>
}

<div class="col-xs-6">
    <a class="btn btn-default btn-block" id="item-todos-produtos" data-codigo="0">
        <span class="item-categoria-nome">@CategoriaPartialRes.TodosProdutos</span>
    </a>
</div>