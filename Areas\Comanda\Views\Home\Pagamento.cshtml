﻿@using Consumer.Resources.Mobile.Comanda.Views.Home
@model RAL.Consumer.Mobile.Areas.Comanda.Models.PagamentosViewModel

@using RAL.Common;
@{
    ViewBag.Title = @PagamentoRes.PagarPedido + Model.NumeroMesa;
}

<div class="row">

    <div class="col-xs-12 bg-warning mt-sm">

        <p class="valores-financeiros">
            @PagamentoRes.TotalItens<span class="pull-right">@Model.ValorTotalItens.ToString("C")</span>
        </p>
        <p class="valores-financeiros">
            @PagamentoRes.TotalServico<span class="pull-right">@Model.ValorTotalServico.ToString("C")</span>
        </p>
        
        @if (Model.ValorTotalIva > 0)
        {
            <p class="valores-financeiros">
                @PagamentoRes.TotalIva<span class="pull-right">@Model.ValorTotalIva.ToString("C")</span>
            </p>
        }
        
        <p class="valores-financeiros">
            @PagamentoRes.Desconto<span class="pull-right">@Model.ValorDesconto.ToString("C")</span>
        </p>
        <p class="valores-financeiros">
            @PagamentoRes.TotalPagar<span class="pull-right"><strong>@Model.ValorTotalPagar.ToString("C")</strong></span>
        </p>

    </div>
</div>

<div class="row">
    <div class="col-xs-12 bg-info mt-sm">
        <p class="valores-financeiros">
            @PagamentoRes.TotalPago<span class="pull-right"><strong>@Model.ValorTotalPago.ToString("C")</strong></span>
        </p>
        @if (Model.ValorRestantePagar > 0)
        {
            <p class="valores-financeiros">
                @PagamentoRes.FaltaPagar<span class="pull-right"><strong>@Model.ValorRestantePagar.ToString("C")</strong></span>
            </p>
        }
    </div>
</div>


<hr />

@if (Model.Pagamentos.Count() == 0)
{
    <div class="alert alert-warning">
        <i class="glyphicon glyphicon-warning-sign"></i> @PagamentoRes.NaoHaPag
    </div>
}
else
{
    <h4>Valores Pagos</h4>

    <table id="pagamentos" class="table table-condensed table-responsive tabela-pagamentos">
        @foreach (var item in Model.Pagamentos)
        {
            <tr id="<EMAIL>" class="linha-pai" data-codigo="@item.Codigo">
                <td>
                    @item.FormaPagamentoOperadoraCartao
                    @if (!string.IsNullOrEmpty(item.Observacao))
                    {
                        <small class="text-muted">(@item.Observacao)</small>
                    }
                </td>
                <td>@item.Valor.ToString("C")</td>
                <td>
                    @*<a class="btn btn-danger btn-block btn-md" onclick="return confirmDelete(@item.CODIGO);" title="Estornar"><i class="fa fa-trash"></i></a>*@

                    <a href="#"
                       class="btn btn-danger btn-block btn-md"
                       data-recordid="@item.Codigo"
                       data-recordtitle="@PagamentoRes.EstornarExcluirPagamento"
                       data-path="@Url.Action("EstornarPagamento", "Pagamento", new { CodigoPedido = Model.CodigoPedido, CodigoPagamento = item.Codigo })"
                       data-toggle="modal"
                       data-target="#confirma-estorno">
                        <i class="fa fa-trash"></i>
                    </a>
                </td>
            </tr>
        }
        @if (Model.ValorTotalPago > 0)
        {
            <tr class="linha-subtotal">
                <td></td>
                <td colspan="2"></td>
            </tr>

            <tr class="linha-total">
                <td>Total</td>
                <td colspan="2">
                    <strong>@Model.ValorTotalPago.ToString("C")</strong>
                </td>
            </tr>
        }
    </table>
}

<div id="confirma-estorno" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="Estorno" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h4 class="modal-title" id="myModalLabel">Confirmação</h4>
            </div>
            <div class="modal-body">
                <p>@PagamentoRes.ConfirmaEstornoPagamento</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">@PagamentoRes.Cancelar</button>
                <button type="button" class="btn btn-danger btn-ok">@PagamentoRes.Sim</button>
            </div>
        </div>
    </div>
</div>



<div id="modalFinalizar" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="@PagamentoRes.Finalizar" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            @using (Html.BeginForm("Finalizar", "Home", FormMethod.Post))
            {
                @Html.AntiForgeryToken()

                @Html.HiddenFor(m => m.FiscalAtivo)
                @Html.HiddenFor(m => m.CodigoPedido)
                @Html.HiddenFor(m => m.NumeroMesa)
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title" id="myModalLabel">@PagamentoRes.FinalizarMesa @Model.NumeroMesa</h4>
                </div>
                if (Model.FiscalAtivo)
                {
                    <div class="modal-body">
                        <h5>@PagamentoRes.DadosEmissaoFiscal</h5>
                        <div class="form-group">
                            <div class="btn-group" data-toggle="buttons">
                                <label class="btn btn-primary active"><input type="radio" name="TipoDocumentoEmissaoFiscal" value="1" checked="checked">@PagamentoRes.CPF</label>
                                <label class="btn btn-primary"><input type="radio" name="TipoDocumentoEmissaoFiscal" value="2">@PagamentoRes.CNPJ</label>
                                <label class="btn btn-primary"><input type="radio" name="TipoDocumentoEmissaoFiscal" value="3">@PagamentoRes.Estrangeiro</label>
                            </div>
                        </div>
                        <div class="form-group">

                            @Html.EditorFor(model => model.DocumentoEmissaoFiscal, new { htmlAttributes = new { @class = "form-control", type = "tel" } })
                            @Html.ValidationMessageFor(model => model.DocumentoEmissaoFiscal, "", new { @class = "text-danger" })
                        </div>
                    </div>
                }
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">@PagamentoRes.Cancelar</button>
                    <button type="submit" class="btn btn-success btn-ok">@PagamentoRes.Confirmar</button>
                </div>
            }
        </div>
    </div>
</div>

<nav id="actions" class="navbar navbar-default navbar-fixed-bottom">
    <div class="slider-formas ocultar">
        <div class="btn-group btn-group-justified" role="group" aria-label="...">
            <a href="@Url.Action("AddPagamento", "Pagamento", new { formaPagamento = (short)FormasPagamento.Dinheiro, codigoPedido = Model.CodigoPedido })" class="btn btn-lg btn-default" title="@PagamentoRes.Dinheiro"><i class="fa fa-money"></i> <span class="hidden-xs">@PagamentoRes.Dinheiro</span><small class="visible-xs">@PagamentoRes.Dinheiro</small></a>
            <a href="@Url.Action("AddPagamento", "Pagamento", new { formaPagamento = (short)FormasPagamento.CartaoDebito, codigoPedido = Model.CodigoPedido })" class="btn btn-lg btn-default" title="@PagamentoRes.CartaoDebito"><i class="fa fa-credit-card"></i> <span class="hidden-xs">@PagamentoRes.Debito</span><small class="visible-xs">@PagamentoRes.Debito</small></a>
            <a href="@Url.Action("AddPagamento", "Pagamento", new { formaPagamento = (short)FormasPagamento.CartaoCredito, codigoPedido = Model.CodigoPedido })" class="btn btn-lg btn-default" title="@PagamentoRes.CartaoCredito"><i class="fa fa-cc-mastercard"></i> <span class="hidden-xs">@PagamentoRes.Credito</span><small class="visible-xs">@PagamentoRes.Credito</small></a>
        </div>
        <div class="btn-group btn-group-justified" role="group" aria-label="...">
            <a href="@Url.Action("AddPagamento", "Pagamento", new { formaPagamento = (short)FormasPagamento.PixPagtoInstantaneo, codigoPedido = Model.CodigoPedido })" class="btn btn-lg btn-default" title="@PagamentoRes.Pix"><i class="fa fa-money"></i> <span class="hidden-xs">@PagamentoRes.Pix</span><small class="visible-xs">@PagamentoRes.Pix</small></a>
            <a href="@Url.Action("AddPagamento", "Pagamento", new { formaPagamento = (short)FormasPagamento.ValeAlimentacao, codigoPedido = Model.CodigoPedido })" class="btn btn-lg btn-default" title="@PagamentoRes.ValeAlimentacao"><i class="fa fa-cart-arrow-down"></i> <span class="hidden-xs">@PagamentoRes.ValeAlim</span><small class="visible-xs">@PagamentoRes.ValeAlim</small></a>
            <a href="@Url.Action("AddPagamento", "Pagamento", new { formaPagamento = (short)FormasPagamento.ValeRefeicao, codigoPedido = Model.CodigoPedido })" class="btn btn-lg btn-default" title="@PagamentoRes.ValeAlimentacao"><i class="fa fa-cutlery"></i> <span class="hidden-xs">@PagamentoRes.ValeRef</span><small class="visible-xs">@PagamentoRes.ValeRef</small></a>
        </div>
    </div>
    <div id="pedido-actions" class="container-fluid">
        <div class="col-xs-4">
            <a id="btn-voltar" class="btn btn-lg btn-default btn-block navbar-btn" title="@PagamentoRes.Voltar"><i class="glyphicon glyphicon-circle-arrow-left"></i> <span class="hidden-xs">@PagamentoRes.Voltar</span><small class="visible-xs">@PagamentoRes.Voltar</small></a>
        </div>

        @if (Model.ValorRestantePagar > 0)
        {
            <div class="col-xs-8">
                @*<a id="btn-add-pagto" class="btn btn-lg btn-success btn-block navbar-btn" title="Adicionar Pagamento" data-toggle="modal" data-target="#opcoes-pagamento-modal"><i class="glyphicon glyphicon-plus"></i> <span class="hidden-xs">Adicionar Pagamento</span><small class="visible-xs">Adicionar Pagamento</small></a>*@
                <a id="btn-add-pagto" class="btn btn-lg btn-success btn-block navbar-btn" title="@PagamentoRes.AdicionarPagamento" data-toggle="modal" data-target="#opcoes-pagamento-modal"><i class="glyphicon glyphicon-plus"></i> <span class="hidden-xs">@PagamentoRes.AdicionarPagamento</span><small class="visible-xs">@PagamentoRes.AdicionarPagamento</small></a>
            </div>
        }
        else
        {
            if (Model.HabilitarFechamentoPedido)
            {
                <div class="col-xs-8">
                    <a class="btn btn-lg btn-info btn-block navbar-btn" title="@PagamentoRes.Finalizar" data-toggle="modal" data-target="#modalFinalizar"><i class="glyphicon glyphicon-check"></i><span class="hidden-xs">@PagamentoRes.Finalizar</span><small class="visible-xs">@PagamentoRes.Finalizar</small></a>
                </div>
            }
        }

    </div>
</nav>


@section Scripts{
    <script>
        $('#confirma-estorno').on('click', '.btn-ok', function (e) {
            var $modalDiv = $(e.delegateTarget);
            var id = $(this).data('recordid');
            var path = $(this).data('path');

            $.ajax({
                url: path,
                type: 'POST',
                cache: false,
                async: true,
                success: function (result) {
                    if (result !== null) {
                        if (result.sucesso) {
                            window.location = '@Html.Raw(@Url.Action("Pagamento", "Home", new {CodigoPedido = Model.CodigoPedido }))';
                        }
                    }
                },
                error: function (result) {
                    $.notify("<i class='" + result.icone + "'></i> " + result.mensagem);
                }
            });

            $modalDiv.addClass('loading');
            setTimeout(function () {
                $modalDiv.modal('hide').removeClass('loading');
            }, 1000);
        });
        $('#confirma-estorno').on('show.bs.modal', function (e) {
            var data = $(e.relatedTarget).data();

            $('.btn-ok', this).data('path', data.path);
            $('.btn-ok', this).data('recordid', data.recordid);
        });

        $(document).ready(function () {
            $('#DocumentoEmissaoFiscal').mask("999.999.999-99");
            $('[name=TipoDocumentoEmissaoFiscal]').change(function () {
                if ($(this).val() == 1)
                    $('#DocumentoEmissaoFiscal').mask("999.999.999-99");
                else if ($(this).val() == 2)
                    $('#DocumentoEmissaoFiscal').mask("99.999.999/9999-99");
                else
                    $('#DocumentoEmissaoFiscal').unmask();
                $('#DocumentoEmissaoFiscal').focus();
            });

            $("#modalFinalizar").on('shown.bs.modal', function () {
                $('#DocumentoEmissaoFiscal').focus();
            });

            $("#btn-add-pagto").click(function () {
                $('.slider-formas').toggleClass('ocultar');
            });

            @if (Model.Pagamentos.Count() == 0)
            {
                <text>
                    $("#btn-add-pagto").trigger("click");
                </text>
            }
        });

    </script>
}
