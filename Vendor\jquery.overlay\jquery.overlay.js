/*
 * DWTFYW License
 * Author: <PERSON>, http://tonybrix.info
 * 
 * Simple overlay with text/html in the center and a delayed cancel button
 * Requires jQuery
 * 
 * if cancelTimeDelay is < 0 then there is no cancel button
 * 
 */

$.overlaycancelTimeout = null;
$.overlaydelayTimeout = null;
$.displayOverlay = function (text, zIndex, cancelTimeDelay, opacity, delay, cancelCallback) {
	var options = {
	    text: '<i class="fa fa-spinner fa-spin fa-fw" aria-hidden="true"></i> Processando...',
		zIndex: 10000,
		delay: 100,
		opacity: .8,
		cancelTimeDelay: -101,
		cancelCallback: function () {
			return true;
		}
	};
	if ($.isPlainObject(text)) {
		$.extend(options, text);
	} else {
		if (text) {
			options.text = text;
		}
		if (zIndex) {
			options.zIndex = zIndex;
		}
		if (delay) {
			options.delay = delay;
		}
		if (opacity) {
			options.opacity = opacity;
		}
		if (cancelTimeDelay) {
			options.cancelTimeDelay = cancelTimeDelay;
		}
		if (typeof cancelCallback === "function") {
			options.cancelCallback = cancelCallback;
		}
	}
	options.cancelTimeDelay += options.delay;
	if (options.opacity > 1) {
		options.opacity /= 100;
	}
	var $overlay = $("<table id='overlay' style='display: none'><tbody><tr><td>" + options.text + "</td></tr></tbody></table>").css({
		position: "fixed",
		top: "10px",
		left: "50%",
        "margin-left": "-75px",
		width: "150px",
		//height: "15px",
		"background-color": "rgba(0,0,0," + options.opacity + ")",
		"z-index": options.zIndex,
		"vertical-align": "middle",
		"text-align": "center",
		color: "#fff",
		"font-size": "12px",
		"font-weight": "bold",
		"font-family": "sans-serif",
		cursor: "wait"
	});
	if (options.delay > 0) {
        $.overlaydelayTimeout = setTimeout(function () {
            $overlay.prependTo("body").fadeIn();
		}, options.delay);
	} else {
        $overlay.prependTo("body").fadeIn();
	}
	if (options.cancelTimeDelay >= 0) {
		$.overlaycancelTimeout = setTimeout(function () {
			$("<div id='overlaycancel' title='Cancel'>X</div>").css({
				position: "fixed",
				top: 0,
				right: 0,
				"z-index": options.zIndex + 1,
				"font-size": "20px",
				"font-weight": "bold",
				"font-family": "sans-serif",
				cursor: "pointer",
				color: "#fff",
				"padding-top": "5px",
				"padding-right": "8px",
				opacity: 0
			}).hover(function () {
				$(this).css({
					"font-size": "24px",
					"padding-top": "2px",
					"padding-right": "6px"
				});
			}, function () {
				$(this).css({
					"font-size": "20px",
					"padding-top": "5px",
					"padding-right": "8px"
				});
			}).animate({opacity: 1}).click(function () {
				if (options.cancelCallback()) {
					$.removeOverlay();
				}
			}).appendTo("body");
		}, options.cancelTimeDelay);
	}
};

$.removeOverlay = function () {
	clearTimeout($.overlaycancelTimeout);
	clearTimeout($.overlaydelayTimeout);
	$("#overlay, #overlaycancel").fadeOut(function () { $(this).remove(); });
};
