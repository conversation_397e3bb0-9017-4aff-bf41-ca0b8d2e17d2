﻿//function mostrarDetalhes() {
//    if ($('#mostrarDetalhes').prop('checked')) {
//        $('p.product-description').removeClass("hide");
//    }
//    else {
//        $('p.product-description').addClass("hide");
//    }
//}

//function mostrarFotos() {
//    if ($('#mostrarFotos').prop('checked')) {
//        $('.col-sm-3').removeClass("hide");
//        $('.col-sm-12').addClass('col-sm-9')
//        $('.col-sm-12').removeClass('col-sm-12')
//    }
//    else {
//        $('.col-sm-3').addClass("hide");
//        $('.col-sm-9').addClass('col-sm-12');
//        $('.col-sm-9').removeClass('col-sm-9');
//    }
//}

var currentIndexProduto = 0;
function irParaProduto(irPara) {
    var currentProduto = $('.row .col-md-6').eq(currentIndexProduto);
    var classPrev = "right";
    var classNext = "left";
    if (irPara < 0) {
        classPrev = "left";
        classNext = "right";
        currentProduto = $(currentProduto).prev();
    }
    else
        currentProduto = $(currentProduto).next();
    if ($(currentProduto).length > 0) {
        currentIndexProduto = $('.row .col-md-6').index($(currentProduto));

        //$('.modal-content').animo({
        //    animation: "fadeOut"
        //});
        //$('.modal-content').load($(currentProduto).children('.modal-link').data('load'), function (response, status, xhr) {
        //    $('.modal-content').show().animo({
        //        animation: "fadeIn"
        //    }, alternarIrPara);
        //    $.validator.unobtrusive.parse(this);
        //    ajustarScroll();
        //});

        $('.modal.fade').removeClass(classPrev);
        $('.modal.fade').addClass(classNext);
        $('.modal.fade.in').removeClass("in");

        setTimeout(function () {
            $('.modal.fade').removeClass(classNext);
            $('.modal.fade').addClass(classPrev);
            setTimeout(function () {
                $('.modal-content').load($(currentProduto).children('.modal-link').data('load'), function (response, status, xhr) {
                    $('.modal.fade').addClass("in");
                    $.validator.unobtrusive.parse(this);
                    ajustarScroll();
                    setTimeout(alternarIrPara, 500);
                });
            }, 100);
        }, 100);
    }
}

function alternarIrPara() {
    if (currentIndexProduto === 0)
        $('.btn-irpara[data-irpara="-1"]').attr("disabled", "");
    if (currentIndexProduto === $('.row .col-md-6').index($('.row .col-md-6').last()))
        $('.btn-irpara[data-irpara="+1"]').attr("disabled", "");
}

function ajustarScroll() {
    if (Modernizr.mq('(max-width: 767px)')) {
        var height = $('.modal-header').outerHeight();
        $('.modal-body').children().first().css("margin-top", height);
        if ($('.modal-body').children(':visible').last().position()) {
            var marginbot = $(window).height() - $('.modal-body').children(':visible').last().position().top - $('.modal-body').children(':visible').last().outerHeight() - 90 + height;
            if (marginbot < 15) {
                $('.modal-body').children(':visible').last().css("margin-bottom", '');
            }
            else {
                $('.modal-body').children().last().css("margin-bottom", marginbot);
            }
        }
        $('.modal-body').scroll(function () {
            if ($('.modal-body').children().eq(1).position().top < $('.modal-header').outerHeight())
                $('.modal-header').css('background', '#fff');
            else
                $('.modal-header').css('background', '');
        });
    }
}

$(document).ready(function () {

    initCultureApp();

    $('body').on('click', '.modal-link', function (e) {
        e.preventDefault();

        var obj = $(this);

        if (obj.data('haswizard') === true) { // Produto wizard
            location.href = "/Cardapio/Wizard/Add/" + obj.data('codigoproduto');
        }
        else {
            obj.attr('data-target', '#produtoModal');
            obj.attr('data-toggle', 'modal');

            var url = obj.data('load');
            $('.modal-content').load(url, function (response, status, xhr) {
                $('.modal-content').show().animo({
                    animation: "fadeIn"
                }, alternarIrPara);
                $.validator.unobtrusive.parse($('.modal-dialog'));                

                ajustarModalBody()                
            });
            currentIndexProduto = $('.row .col-md-6').index($(obj).parent());
        }
    });

    $('#produtoModal').on('hidden.bs.modal', function () {
        $(this).removeData('bs.modal');
    });

    var touchStartX = 0;
    var touchEndX = 0;
    var touchStartY = 0;
    var touchEndY = 0;
    $(document).on("touchstart", ".modal-body", function (event) {
        touchStartX = event.originalEvent.changedTouches[0].screenX;
        touchStartY = event.originalEvent.changedTouches[0].screenY;
    });

    $(document).on("touchend", ".modal-body", function (event) {
        touchEndX = event.originalEvent.changedTouches[0].screenX;
        touchEndY = event.originalEvent.changedTouches[0].screenY;
        if (Math.abs(touchEndX - touchStartX) > Math.abs(touchEndY - touchStartY) && Math.abs(touchEndY - touchStartY) < 50 && Math.abs(touchEndX - touchStartX) > 50) {
            if (touchEndX > touchStartX) {
                irParaProduto(-1);
            } else if (touchEndX < touchStartX) {
                irParaProduto(+1);
            }
        }
    });

    $(document).on("click", ".btn-irpara", function (event) {
        irParaProduto($(this).data('irpara'));
    });

    $("#formWizard").ready(function () {
        atualizarContadorObservacoes();
    })

    $('#produtoModal').on('shown.bs.modal', function () {
        ajustarScroll();

        atualizarContadorObservacoes();        
    });

    function atualizarContadorObservacoes() {
        $("#ObservacoesAdicionais").off("keyup");

        $("#ObservacoesAdicionais").on("keyup", function (e) {
            $(".count_message").text(e.currentTarget.value.length + " / 140");
        });
    }

    function ajustarModalBody() {
        var width = (window.innerWidth > 0) ? window.innerWidth : screen.width;

        if (width <= 768) {
            var image = document.getElementById("produtoModalImagePath");

            if (image == undefined || image == null) {
                document.querySelector('.modal-body').classList.add("modalBody");
            }
        }        
    }

    //$('#mostrarFotos').change(mostrarFotos);
    //$('#mostrarDetalhes').change(mostrarDetalhes);

    $(document).on('change', 'input[name*=TodasPartes]', function () {
        var fn = function () {
            if ($('input[name*=TodasPartes][value=0]:checked').length === 1) {  //Por Tamanho Inteiro
                $('.meioameio').toggleClass("hide", true);
                $('#Quantidade').data('val-range-max', $('#Quantidade').data('val-range-max-old'));
                $('#Quantidade').removeData('val-range-max-old');
            }
            else {
                $('.meioameio').removeClass("hide");
                $('#Quantidade').val(1);
                $('#Quantidade').data('val-range-max-old', $('#Quantidade').data('val-range-max'));
                $('#Quantidade').data('val-range-max', 1);
            }
        }

        CarregaComplementosParaAgrupadorMeioMeio(fn);
    });

    $(document).on('change', 'select[name=SelectedPartes]', function () {

        if ($('select[name=SelectedPartes]').val() === '') {  //Por Tamanho Inteiro
            $('#Quantidade').data('val-range-max', $('#Quantidade').data('val-range-max-old'));
            $('#Quantidade').removeData('val-range-max-old');
        }
        else {
            $('#Quantidade').val(1);
            $('#Quantidade').data('val-range-max-old', $('#Quantidade').data('val-range-max'));
            $('#Quantidade').data('val-range-max', 1);
        }
    });

    $(document).on('change', 'input[name*=SelectedComplementos]', function () {
        if ($(this).is(":checked")) {
            var value = $(this).val();
            if ($('input[name*=SelectedComplementos][value=' + value + ']:checked').length > 1) {
                $('input[name*=SelectedComplementos][value=' + value + ']').prop('checked', false);
                $(this).prop('checked', true);
            }
        }
    });

    $(document).on('change', 'input[name*=SelectedObservacoes]', function () {
        if ($(this).is(":checked")) {
            var value = $(this).val();
            if ($('input[name*=SelectedObservacoes][value=' + value + ']:checked').length > 1) {
                $('input[name*=SelectedObservacoes][value=' + value + ']').prop('checked', false);
                $(this).prop('checked', true);
            }
        }
    });

    //View AddProdutoTamanho
    $(document).on('change', 'input[name*=TodosTamanhos]', function () {
        const botaoAdicionar = $('.btn-success[type=submit]');

        if ($(this).data('wizard') === true) {
            botaoAdicionar.prop('disabled', true)
                .addClass('disabled')
                .html('<i class="fa fa-spinner fa-spin"></i><br>Carregando...');
            $.displayOverlay();
            location.href = '/cardapio/wizard/add/' + $('#CodigoProduto').val() + '?codigoTamanho=' + $(this).data('codigotamanho');
        }
        else {
            var fn = function () {
                var max = $('input[name*="TodosTamanho"]:checked').data("qtdmaximapartes");
                $('#todasPartes').toggleClass("hide", max < 2);
                $('input[name*=TodasPartes]').removeAttr('checked');
                $('input[name*=TodasPartes]#parte-inteira').prop('checked', true).change();
                $('#collapseSelectedPartes div.parte').addClass("hide");
                $('#collapseSelectedPartes div.parte-' + $('input[name*=TodosTamanhos]:checked').data("codigotamanho")).removeClass("hide");
            } 
            CarregaComplementosParaAgrupadorMeioMeio(fn);
            $('#accordion').slideDown();
        }
    });

    function CarregaComplementosParaAgrupadorMeioMeio(callback) {
        var codigosProdutoDetalhe = [];

        codigosProdutoDetalhe.push(parseInt($('input[name*="TodosTamanho"]:checked').val())); //parte principal

        var codigoSegundoSabor = parseInt($('input[name*="TodasPartes"]:checked').val());
        if (codigoSegundoSabor > 0)
            codigosProdutoDetalhe.push(codigoSegundoSabor); //segundo sabor

        $.ajax({
            data: { codigosProdutoDetalhe: codigosProdutoDetalhe }, // << Without JSON.stringify 
            traditional: true,  // << Important line
            url: '/Cardapio/Produto/ComplementosProdutoTamanhoFracionadoPrincipal',
            type: 'post',
            //dataType: 'json',
            success: function (resp) {
                $('#complementosProdutoTamanho').html(resp);
                callback();
            }
        });
    }

    $(document).on('change', '#Quantidade,input[name*=SelectedComplementos],input[name*=SelectedObservacoes],input[name*=TodosTamanhos],input[name*=TodasPartes]', function () {
        var precoVenda = 0;

        var qtd = parseFloat($('#Quantidade').val());

        precoVenda = parseFloat($('#PrecoVenda').val().replace(",", "."));
        var cobrarPeloMaiorValor = $('#CobrarPeloMaiorValor').val();

        $('input[name*=TodosTamanhos]:checked').each(function () {
            precoVenda = parseFloat($(this).data("precovenda"));
        });

        var valorParte = 0;
        $('input[name*=TodasPartes]:checked').each(function () {
            valorParte = parseFloat($(this).data("precovenda"));
        });

        if (valorParte > 0) {
            if (cobrarPeloMaiorValor) {
                precoVenda = Math.max(precoVenda, valorParte);
            }
            else
                precoVenda = (precoVenda / 2) + (valorParte / 2);
        }

        var complementos = 0;
        $('input[name*=SelectedComplementos]:checked').each(function () {
            complementos += parseFloat($(this).data("precovenda"));
        });
        $('#PrecoTotal').text("R$ " + Number(qtd * (precoVenda + complementos)).toFixed(2).replace(".", ",")).animo({
            animation: "tada"
        });

        $('#produtoModalNome').text(function () {
            var nome = $('#NomeProduto').val();
            if (valorParte > 0) {
                nome = $('#NomeProdutoPersonalizado').val() || 'Personalizado';
            }
            if ($('input[name*=TodosTamanhos]:checked').length) {
                nome += " " + $('input[name*=TodosTamanhos]:checked').data('nome');
            }
            return nome;
        });

        $('#resumo').html(function () {
            var resumo = "";
            $('input[name*="SelectedComplementos["]:checked').each(function () {
                if (resumo !== "")
                    resumo += ", ";
                resumo += $(this).parent().siblings('i:first').text();
            });
            $('input[name*="SelectedObservacoes"]:checked').each(function () {
                if (resumo !== "")
                    resumo += ", ";
                resumo += $(this).parent().siblings('i:first').text();
            });
            if (valorParte > 0) {
                if (resumo !== "")
                    resumo = "<i class='fa fa-circle'></i> <i>" + resumo + "</i><br />";
                var resumoParte1 = "";
                $('input[name*="SelectedComplementosParte[0]"]:checked').each(function () {
                    if (resumoParte1 !== "")
                        resumoParte1 += ", ";
                    resumoParte1 += $(this).parent().siblings('i:first').text();
                });
                $('input[name*="SelectedObservacoesParte[0]"]:checked').each(function () {
                    if (resumoParte1 !== "")
                        resumoParte1 += ", ";
                    resumoParte1 += $(this).parent().siblings('i:first').text();
                });
                resumo += "<i class='fa fa-adjust fa-rotate-180'></i> " + $('#NomeProduto').val();
                if (resumoParte1 !== "")
                    resumo += ": <i>" + resumoParte1 + "</i>";
                var resumoParte2 = "";
                $('input[name*="SelectedComplementosParte[1]"]:checked').each(function () {
                    if (resumoParte2 !== "")
                        resumoParte2 += ", ";
                    resumoParte2 += $(this).parent().siblings('i:first').text();
                });
                $('input[name*="SelectedObservacoesParte[1]"]:checked').each(function () {
                    if (resumoParte2 !== "")
                        resumoParte2 += ", ";
                    resumoParte2 += $(this).parent().siblings('i:first').text();
                });
                if (resumoParte2 === "")
                    if (resumoParte1 === "")
                        resumo += " / " + "<i class='fa fa-adjust'></i> " + $('input[name*=TodasPartes]:checked').data('nome');
                    else
                        resumo += "<br />" + "<i class='fa fa-adjust'></i> " + $('input[name*=TodasPartes]:checked').data('nome');
                else
                    resumo += "<br />" + "<i class='fa fa-adjust'></i> " + $('input[name*=TodasPartes]:checked').data('nome') + ": <i>" + resumoParte2 + "</i>";
            }
            $('#resumo').toggleClass("hide", resumo === "")
            $('.modal-body div:first').css('margin-top', $('.modal-header').outerHeight(true) + "px");
            return resumo;
        });
    });

    $(document).on('click', '.btn-minus', function () {
        var input = $(this).parent().siblings('input');
        var minValue = parseInt($(input).data('val-range-min'));
        var newValue = parseInt($(input).val()) - 1;
        if (newValue < minValue) newValue = minValue;
        $(input).val(newValue).change();
    });

    $(document).on('click', '.btn-plus', function () {
        var input = $(this).parent().siblings('input');
        var maxValue = parseInt($(input).data('val-range-max'));
        var newValue = parseInt($(input).val()) + 1;
        if (newValue > maxValue) newValue = maxValue;
        $(input).val(newValue).change();
    });

    $(document).on('change', 'input[type=radio][name*="ComboItens"]', function () {
        var currentRadio = $(this);

        var idComboItem = $(currentRadio).data('idcomboitem');
        var indexComboItem = $(currentRadio).data('indexcomboitem');
        var url = $(currentRadio).data('load');
        var codigoProdutoDetalhe = $(currentRadio).val();

        $('#accordionComboItem' + idComboItem).load(url + '?indexComboItem=' + indexComboItem + '&idComboItem=' + idComboItem + '&codigoProdutoDetalhe=' + codigoProdutoDetalhe, function (data) {
            // load success code
        });
    });
});

function OnSuccessAdd(response) {
    $('#produtoModal').modal('hide');
    if (response && response.data) // verifica se response e se response.data existe
    {
        swal({
            title: `${getLocalizedContent("ALGO_MAIS")}`,
            type: "success",
            showCancelButton: true,
            confirmButtonClass: "btn-success",
            confirmButtonText: `${getLocalizedContent("SIM")}`,
            cancelButtonClass: "btn-default",
            cancelButtonText: `${getLocalizedContent("NAO")}`,
            closeOnConfirm: true
        },
            function (isConfirm) {
                if (!isConfirm) {
                    localStorage.setItem('enviarPedidoAutomatico', 'true');
                    $.displayOverlay();
                    window.location.href = "/Cardapio/Pedido/Review";
                }
                else {
                    if (!$('#actionBar').length) {
                        window.location.href = "/Cardapio/Produto/Categoria";
                    }
                }
            });
    }

    if ($('#actionBar').length) {
        $('#actionBar').load($('#actionBar').data('load'), function () {
            $.validator.unobtrusive.parse(this);
        });
    }
}


function OnBeginComboAdd() {
    $.validator.unobtrusive.parse(this);

    if (parseInt($('#CodigoTipoProduto').val()) === 4) {
        var isValid = true;
        var comboItemId;
        $('.comboOpcoesList').each(function (index, value) {

            var qtdRadioChecked = $(value).find('input[type="radio"]:checked').length;
            comboItemId = $(value).find('input[type="radio"]').first().data('idcomboitem');
            isValid = qtdRadioChecked > 0;

            if (!isValid) {

                //$.removeOverlay();
                //ExibirErroValidacaoComboItem(comboItemId, 'Selecione um item');
                return false;
            }
        });

        if (!isValid) {
            $.removeOverlay();
            ExibirErroValidacaoComboItem(comboItemId, 'Selecione um item');
            return false;
        }
    }

    return true;
}

function ExibirErroValidacaoComboItem(comboItemId, mensagem) {
    var $el = $('input[type="hidden"][value=' + comboItemId + ']').parents('.modal-content');
    $(document).scrollTop($el.offset().top - 70);
    $el.find('input:visible:first').focus();
    $el.animo({
        animation: "shake"
    });
    var customVal = $el.children('form').validate();
    var input = $('input[type="hidden"][value="' + comboItemId + '"]');
    customVal.showErrors(JSON.parse('{ "' + $(input).attr('name') + '": "' + mensagem + '"}'));
}

function OnBeginAddWizard() {
    var formValid = true;
    $('[data-pergunta]').each(function () {
        var isValid = validarPergunta($(this));

        if (!isValid) {
            //e.preventDefault();
            formValid = false;
            //$.hideOverlay();
            $.removeOverlay();
            return false;
        }
    });

    if (!formValid) {
        $('button,input[type="submit"]').removeAttr('disabled');
        //e.preventDefault();
        //$.hideOverlay();
        $.removeOverlay();
        return false;
    }

    return true;
}

function chamarGarcom(e) {
    e.preventDefault();
    swal({
        title: "Chamar Garçom?",
        type: "warning",
        showCancelButton: true,
        confirmButtonClass: "btn-success",
        confirmButtonText: "Sim!",
        cancelButtonText: "Não",
        closeOnConfirm: true
    },
        function () {
            $.post("/Cardapio/Home/ChamarGarcom", function (data) {
                $.notify(data.message);
            });
        }
    );
}

//Wizard
function validarPergunta($pergunta) {
    var errorMessage = null;
    var perguntaId = $pergunta.data('id');

    var $listOpcoes = $('[data-parent=' + perguntaId + ']');

    if ($listOpcoes.filter('input[type="radio"]').length > 0) { // Validar radio
        var radioChecked = $listOpcoes.filter('input[type="radio"]:checked').length;

        if (radioChecked < $pergunta.data('opc-min'))
            errorMessage = 'Selecione no mínimo ' + $pergunta.data('opc-min');

        if (radioChecked < $pergunta.data('item-min'))
            errorMessage = 'Selecione no mínimo ' + $pergunta.data('item-min');

        if ($pergunta.data('opc-max') > 0 && radioChecked > $pergunta.data('opc-max'))
            errorMessage = 'Selecione no máximo ' + $pergunta.data('opc-max');

    }
    else if ($listOpcoes.filter('input[type="checkbox"]').length > 0) { // Validar checkbox
        var checkboxChecked = $listOpcoes.filter('input[type="checkbox"]:checked').length;

        if (checkboxChecked < $pergunta.data('opc-min'))
            errorMessage = 'Selecione no mínimo ' + $pergunta.data('opc-min');

        if (checkboxChecked < $pergunta.data('item-min'))
            errorMessage = 'Selecione no mínimo ' + $pergunta.data('item-min');

        if ($pergunta.data('opc-max') > 0 && checkboxChecked > $pergunta.data('opc-max'))
            errorMessage = 'Selecione no máximo ' + $pergunta.data('opc-max');
    }
    else {  // Validar numbers
        var numberChecked = 0;
        var numberTotal = 0;
        $listOpcoes.filter('input[type="number"]').each(function () {
            var valorInformado = parseInt($(this).val()) || 0;

            numberTotal += valorInformado;

            if (valorInformado > 0)
                numberChecked++;
        });

        if (numberChecked < $pergunta.data('opc-min'))
            errorMessage = 'Selecione no mínimo ' + $pergunta.data('opc-min');

        if ($pergunta.data('opc-max') > 0 && numberChecked > $pergunta.data('opc-max'))
            errorMessage = 'Selecione no máximo ' + $pergunta.data('opc-max');

        if (numberTotal < $pergunta.data('item-min'))
            errorMessage = 'Escolha no mínimo ' + $pergunta.data('item-min');

        if ($pergunta.data('item-max') > 0 && numberTotal > $pergunta.data('item-max'))
            errorMessage = 'Escolha no máximo ' + $pergunta.data('item-max');
    }

    if (errorMessage) {
        ExibirErroPergunta(perguntaId, errorMessage);
        return false;
    }
    else {
        return true;
    }
}

function ExibirErroPergunta(perguntaId, mensagem) {
    var $el = $('[data-pergunta][value=' + perguntaId + ']').parents('.panel');
    $(document).scrollTop($el.offset().top - 70);
    $el.find('input:visible:first').focus();
    $el.animo({
        animation: "shake"
    });
    var customVal = $el.closest('form').validate();
    var input = $('input[data-pergunta][value="' + perguntaId + '"]');
    customVal.showErrors(JSON.parse('{ "' + $(input).attr('name') + '": "' + mensagem + '"}'));
}

//$(document).ready(function () {
//    $(document).on('submit', '#formWizard', function (e) {
//        //var formValid = true;
//        //$('[data-pergunta]').each(function () {
//        //    var isValid = validarPergunta($(this));

//        //    if (!isValid) {
//        //        e.preventDefault();
//        //        formValid = false;
//        //        return false;
//        //    }
//        //});

//        //if (!formValid) {
//        //    $('button,input[type="submit"]').removeAttr('disabled');
//        //    e.preventDefault();
//        //    $.hideOverlay();
//        //    return false;
//        //}
//    });
//});

$(document).ready(function () {
    $(document).on('change', 'input[type=checkbox][data-parent],input[type=radio][data-parent],input[type=number][data-parent]', function () {
        var parent = $(this).data('parent');

        var $pergunta = $('[data-id="' + parent + '"]');

        var qtd1 = $('input[type="checkbox"][data-parent="' + parent + '"]:checked').length;
        var qtd2 = $('input[type="radio"][data-parent="' + parent + '"]:checked').length;

        var qtd3 = 0;
        $('input[type="number"][data-parent="' + parent + '"]').each(function (index, element) {
            if ($(this).val() > 0)
                qtd3++;
        });

        var qtdChecked = qtd1 + qtd2 + qtd3;

        if ($pergunta.data('opc-max') > 0 && qtdChecked > $pergunta.data('opc-max')) {

            if ($(this).is('input[type="number"]'))
                $(this).prop('value', 0);
            else
                $(this).prop('checked', false);

            $(this).parent().animo({ animation: "shake" });
            $(this).parents('.panel').children('.panel-heading').animo({ animation: "shake" });
        }
    });
});