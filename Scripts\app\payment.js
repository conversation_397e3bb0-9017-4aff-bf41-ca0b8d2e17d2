﻿var Payment = (function () {

    var getRequest = function (url, successCallback, errorCallback) {

        $.ajax({
            url: url,
            type: "GET",
            dataType: "json",
            success: function (result) {

                if (successCallback)
                    successCallback(result);
            },
            error: function (jqxhr, textStatus, error) {

                if (errorCallback)
                    errorCallback(error);
            }
        });
    }

    var checkPayment = function () {

        let orderId = $("#orderId").val();
        let pagamentoIdPix = $("#pagamentoIdPix").val();
        let valorRecebido = $("#valorRecebido").val();
        let url = `/Comanda/Pagamento/VerificarPagamentoPix?codigoPedido=${orderId}&pagamentoIdPix=${pagamentoIdPix}&valorRecebido=${valorRecebido}`;
        let urlRetorno = `/Comanda/Home/Pagamento?codigoPedido=${orderId}`;

        getRequest(url, function (result) {
            if (result) {
                if (result.Success) {
                    window.location = urlRetorno;
                } else {
                    checkRetry();
                }
            }
        }, function (error) {
            checkRetry();
        });
    }

    var checkRetry = function () {
        setTimeout(function () {
            checkPayment();
        }, 5000);
    }

    var copyPix = function () {

        $("#copyPix").on("click", function (e) {
            e.preventDefault();
            let pixCode = document.getElementById("pixTransactionId");
            pixCode.select();
            document.execCommand("copy");
            $("#copyAlert").show();

            setTimeout(function () {
                $("#copyAlert").hide();
            }, 5000);
        });
    }

    var pixPaymentTimer = function () {

        var expiresAt = $("#expiresAt").val();
        var countDownToExpires = new Date(expiresAt).getTime();

        var interval = setInterval(function () {
            var now = new Date().getTime();

            var difference = countDownToExpires - now;

            var minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)).toString().padStart(2, '0');
            var seconds = Math.floor((difference % (1000 * 60)) / 1000).toString().padStart(2, '0');

            if (difference < 0) {
                clearInterval(interval);
                alert('O pagamento Pix ainda não foi realizado! Cancele essa transação e gere um novo QR Code ou altere a forma de pagamento.');
            } else {
                let expiresAtTimerValue = `Expira em ${minutes}:${seconds}`;
                $("#timerExpiresAt").text(expiresAtTimerValue);
            }

        }, 1000);
    }

    return {
        init: function () {
            checkRetry();
            copyPix();
            pixPaymentTimer();
        }
    };

})();

$(function () {
    Payment.init();
});