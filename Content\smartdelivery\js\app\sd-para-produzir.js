﻿'use strict';

let mapProduce = {
    map: null,
    initMap: function (mapElement) {
        mapProduce.map = new google.maps.Map(mapElement, {
            zoom: 13,
            center: appSettings.PontoCentral,
            mapTypeControlOptions: {
                mapTypeIds: [
                    "roadmap",
                    "styled_map_compacto",
                    "hybrid"
                ]
            }
        });

        mapProduce.map.mapTypes.set("styled_map_compacto", styledMapTypeCompacto);

        const marker = new google.maps.Marker({
            position: appSettings.PontoCentral,
            map: mapProduce.map,
            icon: '/content/smartdelivery/assets/sd-home.png'
        });
        marker.addListener('click', function () {
            infowindow.setContent('Meu Local');
            infowindow.open(mapProduce.map, marker);
        });

        mapProduce.map.addListener('drag', function () {
            if (mapProduce.autoFitBounds) {
                mapProduce.autoFitBounds = false;
                $('#mapProduceCenter').show();
            }
        });

        var centerControlDiv = document.createElement('div');
        centerControlDiv.id = 'mapProduceCenter';
        centerControlDiv.style = 'display: none;';
        new CustomControl(centerControlDiv, mapProduce.fitBounds, null, 'Centralizar', 'Clique para Centralizar o Mapa')
        mapProduce.map.controls[google.maps.ControlPosition.BOTTOM_LEFT].push(centerControlDiv);

        //Sugestões
        var sugestionControlDiv = document.createElement('div');
        sugestionControlDiv.id = 'mapProduceSugestions';
        new CustomControl(sugestionControlDiv, togglePanelRight, true, 'Ver Sugestões', 'Clique para ver as sugestões de grupos');
        mapProduce.map.controls[google.maps.ControlPosition.BOTTOM_CENTER].push(sugestionControlDiv);
    },
    autoFitBounds: true,
    fitBounds: function () {
        let bounds = new google.maps.LatLngBounds();
        bounds.extend(appSettings.PontoCentral);
        mapProduce.markers.forEach(m => {
            bounds.extend(m.getPosition())
        });
        mapProduce.map.fitBounds(bounds);
        mapProduce.autoFitBounds = true;
        $('#mapProduceCenter').hide();
    },
    orders: [],
    markers: [],
    selecteds: [],
    addMarker: function (orderId, location, icon) {
        const marker = new google.maps.Marker({
            position: location,
            map: this.map,
            icon: icon,
            zIndex: 999999 // ao selecionar é subtraído para enviar para trás em colisões
        });

        marker.orderId = orderId;
        marker.defaultIcon = icon;

        marker.addListener('click', function () {
            mapProduce.toggleSelect(orderId);
        });

        this.markers.push(marker);
    },
    selectOrder: function (orderId) {
        const marker = this.markers.find(element => element.orderId === orderId);
        const isSelected = this.selecteds.find(element => element === orderId);

        $(`[data-orderid=${orderId}]`).toggleClass('selected', true);

        if (!isSelected) {
            marker.setIcon(appSettings.SelectedPin);
            marker.setAnimation(3);
            this.selecteds.push(orderId);
        }

        ToggleGroupAction();

        // Auto Agrupamento
        if (appSettings.AutoAgrupamentoAtivo === true && this.selecteds.length === appSettings.AutoAgrupamentoAposSelecionar)
            this.doGroup();
    },
    unselectOrder: function (orderId) {
        const marker = this.markers.find(element => element.orderId === orderId);
        const isSelected = this.selecteds.find(element => element === orderId);

        $(`[data-orderid=${orderId}]`).toggleClass('selected', false);

        if (isSelected) {
            marker.setIcon(marker.defaultIcon);
            marker.setAnimation(4);
            this.selecteds = mapProduce.selecteds.filter(el => el !== orderId);
        }

        ToggleGroupAction();
    },
    toggleSelect: function (orderId) {
        const marker = mapProduce.markers.find(element => element.orderId === orderId);
        const isSelected = mapProduce.selecteds.find(element => element === orderId);
        if (isSelected) {
            mapProduce.unselectOrder(orderId)
        }
        else {
            mapProduce.selectOrder(orderId)
        }
        marker.setZIndex(marker.getZIndex() - 1); // envia para trás após selecionar
    },
    unselectAll: function (remove = false) {
        for (let order in this.selecteds) {

            $(`[data-orderid=${this.selecteds[order]}]`).toggleClass('selected', false);

            const marker = this.markers.find(element => element.orderId === this.selecteds[order]);
            if (remove === true)
                marker.setMap(null);
            else {
                marker.setIcon(marker.defaultIcon);
                marker.setAnimation(4);
            }
        }
        this.selecteds = [];
        ToggleGroupAction();
    },
    doGroup: function (e) {
        if (this.selecteds.length) {
            let groupId = null;
            if (e) {
                let sender = e.srcElement || e.target;
                groupId = $(sender).data('id');
            }
            $.post('/smartdelivery/paraproduzir/dogroup', { selecteds: this.selecteds, existingGroupId: groupId }, function (response) {
                if (response.Success) {
                    mapProduce.unselectAll(true);
                    mapProduce.getOrders();
                }
                else {
                    $.notify(`<i class="material-icons align-bottom">cancel</i> ${response.ErrorMessage}`, { timeout: 0, status: 'danger' });
                }
            });
        }
    },
    getOrders: function () {
        $.post('/smartdelivery/paraproduzir/getorders', function (response) {
            if (response.Success) {

                let orders = response.Model;

                $('#orderToProduceQty').html(`(${orders.length})`);
                $('#table-orders > p').toggleClass('d-none', orders.length !== 0);

                for (let i = 0; i < orders.length; i++) {
                    let currentOrder = orders[i];

                    currentOrder.position = { lat: currentOrder.lat, lng: currentOrder.lng };
                    currentOrder.distance = mapProduce.getDistanceFromPontoCentral(currentOrder);
                    currentOrder.direction = mapProduce.getHeadingFromPontoCentral(currentOrder);

                    //Add List
                    let $listItem = $(`#table-orders [data-orderid=${currentOrder.orderId}]`);
                    if ($listItem.length === 0) {
                        $listItem = iterateAndReplace('template-grid-item-sd-order-to-produce', currentOrder);
                        $('#table-orders').append($listItem);
                    }
                    else {
                        //Upd List
                        let old = mapProduce.orders.find(e => e.orderId === currentOrder.orderId);
                        if (old && old.hash != currentOrder.hash) {
                            let $newListItem = $(iterateAndReplace('template-grid-item-sd-order-to-produce', currentOrder));
                            if (mapProduce.selecteds.includes(currentOrder.orderId))
                                $newListItem.addClass('selected');
                            $listItem.replaceWith($newListItem);
                        }
                    }

                    // Armazena o elemnto JQuery no array de pedidos
                    currentOrder.elementHtml = $(`#table-orders [data-orderid=${currentOrder.orderId}]`);

                    // Markers
                    let orderStatusExpiration = getStatusExpiration(currentOrder.orderCreatedDateTime, appSettings.TempoLimiteEmMinutos);
                    let position = new google.maps.LatLng(currentOrder.lat, currentOrder.lng);
                    const marker = mapProduce.markers.find(m => m.orderId === currentOrder.orderId);
                    if (marker) {
                        if (!mapProduce.selecteds.includes(currentOrder.orderId))
                            marker.setIcon(appSettings.markerIcons[orderStatusExpiration].icon);
                        marker.defaultIcon = appSettings.markerIcons[orderStatusExpiration].icon;
                        marker.setPosition(position);
                    }
                    else
                        mapProduce.addMarker(currentOrder.orderId, position, appSettings.markerIcons[orderStatusExpiration].icon);
                }

                // Del List
                $('#table-orders [data-orderid]').each(function () {
                    const ordersExist = orders.find(m => m.orderId === $(this).data('orderid'));
                    if (!ordersExist) {
                        $(this).slideUp(() => $(this).remove());
                        mapProduce.unselectOrder($(this).data('orderid'))
                    }
                });

                // Del Markers
                mapProduce.markers = mapProduce.markers.filter(function (marker) {
                    const ordersExist = orders.find(m => m.orderId === marker.orderId);
                    if (!ordersExist) {
                        marker.setMap(null);
                    }
                    return ordersExist;
                });

                if (mapProduce.autoFitBounds)
                    mapProduce.fitBounds();

                calcTimeLapse();
                mapProduce.orders = orders;
            }
            else {
                if (response.ErrorMessage)
                    $.notify(`<i class="material-icons align-bottom">cancel</i> ${response.ErrorMessage}`, { timeout: 0, status: 'danger' });
                else // Erro desconhecido
                    $.notify(`<i class="material-icons align-bottom">cancel</i> Não foi possível obter os dados, por favor, recarregue a página.`, { timeout: 15, status: 'danger' });
            }

            //após obter pedidos, agenda uma nova chamada
            clearTimeout(mapProduce.nextUpdateTimeout);
            mapProduce.nextUpdateTimeout = setTimeout(mapProduce.getOrders, appSettings.TempoAtualizacaoEmMS);
        });
    },
    getDistanceBetweenOrders: function (fromOrder, toOrder) {
        return getDistanceMeters(fromOrder.position, toOrder.position);
    },
    getDistanceFromPontoCentral: function (toOrder) {
        return getDistanceMeters(appSettings.PontoCentral, toOrder.position);
    },
    getHeadingFromPontoCentral: function (toOrder) {
        return getHeadingNorth(appSettings.PontoCentral, toOrder.position);
    },
    sortOrdersElementsHtmlBy: function (sortBy, asc = true) {
        $('#table-orders').append($('#table-orders .sd-order').detach().sort(function (a, b) {
            if (!asc) {
                let aux = a;
                a = b;
                b = aux;
            }

            if (sortBy === 'orderCreatedDateTime') {
                return moment($(a).data('statusdatetime')) - new moment($(b).data('statusdatetime'))
            }
            if (sortBy === 'groupid') {
                if ($(a).data(sortBy) === $(b).data(sortBy)) {
                    return $(a).data('groupsort') - $(b).data('groupsort');
                }
                return $(a).data(sortBy) - $(b).data(sortBy);
            }
            else {
                return $(a).data(sortBy) - $(b).data(sortBy);
            }
        }));
        $('.suggest-group.suggest-group-child').toggle(sortBy !== 'groupid');
    },
    getGroupsSuggestionsRoboV2: function () {

        let sugerirPor = appSettings.GrupoSugerirPor;
        let qtdMax = appSettings.GrupoQtdPedidosPadrao;
        let dMax = appSettings.GrupoDistanciaMaximaEmMetros;
        let dUnificar = appSettings.GrupoDistanciaUnificarEmMetros;

        //ordena pela data do pedido
        let ordersByDate = mapProduce.orders.sort((a, b) => moment(a.orderCreatedDateTime) - new moment(b.orderCreatedDateTime));

        //Criar propriedade baseado na distância de unificação dos pedidos
        ordersByDate.forEach(o => {
            // Cria um array com a distancia para todos os outros pedidos
            o.ordersToUnify = ordersByDate
                .filter(d => d.orderId !== o.orderId) // tirando current
                .map(i => {
                    const newObj = {};
                    newObj.order = i;
                    newObj.m = mapProduce.getDistanceBetweenOrders(o, i);
                    newObj.k = mapProduce.markers.find(mk => mk.orderId === i.orderId);
                    newObj.e = i.elementHtml;
                    return newObj
                })
                .sort((a, b) => a.m - b.m) // ordena pelos mais próximos ou data
                .filter(d => d.m <= dUnificar); // filtra os que estão acima do limite para Unificar
        });

        // reseta o agrupamento anterior, se houve uma simulação antes
        mapProduce.clearSuggestions();
        let groups = [];

        // Esse array vai controlar os pedidos que já foram agrupados para serem ignorados no forEach abaixo
        let pedidosAgrupados = [];

        // Vai iterar sobre os pedidos, começmando pelos mais antigos e criar grupos com os pedidos mais próximos a ele
        ordersByDate.forEach(o => {

            if (!pedidosAgrupados.includes(o.orderId)) {

                // cria a grupo para o pedido atual
                let currentGroup = { id: groups.length + 1, items: [] };
                // encontra o pin do mapa do pedido atual, adiciona no grupo e seta o label com id do grupo
                let markerCurrent = mapProduce.markers.find(mk => mk.orderId === o.orderId);
                currentGroup.items.push({ k: markerCurrent });
                markerCurrent.setLabel(currentGroup.id + '');
                groups.push(currentGroup);

                // atribui os dados do grupo ao elemento do pedido na grid
                o.elementHtml.data('groupid', currentGroup.id);
                o.elementHtml.data('groupsort', currentGroup.items.length);
                o.elementHtml.find('.col:first').prepend(`<span data-select-group-suggest class="suggest-group badge-group badge badge-primary badge-pill">Grupo ${currentGroup.id}</span>`);

                pedidosAgrupados.push(o.orderId);

                // Se atingiu o limite, vaza...
                if (currentGroup.items.length === qtdMax)
                    return;

                // Cria um array com todos pedidos não agrupados e a distância para o pedido atual
                let ordersDistanceFromCurrent = ordersByDate
                    .filter(d => d.orderId !== o.orderId && !pedidosAgrupados.includes(d.orderId)) // tirando current
                    .map(i => {
                        const newObj = {};
                        newObj.order = i;
                        newObj.m = mapProduce.getDistanceBetweenOrders(o, i);
                        newObj.k = mapProduce.markers.find(mk => mk.orderId === i.orderId);
                        newObj.e = i.elementHtml;
                        return newObj
                    })
                    .sort((a, b) => sugerirPor === 'DistanciaEntrePedidos' ? a.m - b.m : moment(a.order.orderCreatedDateTime) - new moment(b.order.orderCreatedDateTime)) // ordena pelos mais próximos ou data
                    .filter(d => d.m <= dMax) // filtra os que estão acima do limite
                    .slice(0, qtdMax - 1); // pega N baseado na quantidade máxima definida

                // adiciona cada pedido no grupo e seta os atributos como acima
                ordersDistanceFromCurrent.forEach(d => {
                    if (!pedidosAgrupados.includes(d.order.orderId)) {
                        currentGroup.items.push({ k: d.k });
                        d.k.setLabel(currentGroup.id + '');
                        d.e.data('groupid', currentGroup.id);
                        d.e.data('groupsort', currentGroup.items.length);
                        d.e.find('.col:first').prepend(`<span data-select-group-suggest class="suggest-group suggest-group-child badge-group badge badge-primary badge-pill">Grupo ${currentGroup.id}</span>`)
                        pedidosAgrupados.push(d.order.orderId);

                        // Se o pedido possui outros "muito próximos" (distância para unificar), vai junto mesmo que passe o limite...

                        // adiciona cada pedido no grupo e seta os atributos como acima
                        d.order.ordersToUnify.forEach(u => {
                            if (!pedidosAgrupados.includes(u.order.orderId)) {
                                currentGroup.items.push({ k: u.k });
                                u.k.setLabel(currentGroup.id + '');
                                u.e.data('groupid', currentGroup.id);
                                u.e.data('groupsort', currentGroup.items.length);
                                u.e.find('.col:first').prepend(`<span data-select-group-suggest class="suggest-group suggest-group-child badge-group badge badge-primary badge-pill">Grupo ${currentGroup.id}</span>`)
                                pedidosAgrupados.push(u.order.orderId);
                            }
                        });

                        // Se atingiu o limite, vaza...
                        if (currentGroup.items.length >= qtdMax)
                            return;
                    }
                });
            }
        });

        // ordena grid pelo grupo
        mapProduce.sortOrdersElementsHtmlBy('groupid');

        // Protótipo de exibição dos grupos
        $('#mySidepanel .groups').html(groups.map(g => `<div class="row sd-order mb" data-select-group-suggest data-groupid="${g.id}"><div class='col'><span data-select-group-suggest class="badge-group badge badge-primary badge-pill">Grupo ${g.id}</span><br /><span>${g.items.length} pedidos</span><div><div>`));

        // atribui evento de click para selecionar todos os pedidos do grupo
        $('[data-select-group-suggest]').click(function (e) {
            e.stopPropagation();
            e.preventDefault();
            mapProduce.unselectAll();
            let id = $(this).closest('.sd-order').data('groupid');
            let markersFromGroup = mapProduce.markers.filter(m => m.getLabel() === id.toString());
            markersFromGroup.forEach(m => mapProduce.selectOrder(m.orderId));
        });
    },
    clearSuggestions: () => $('[data-select-group-suggest]').remove()
};

function ToggleGroupAction() {
    $('[data-order-qty-selected]').text(mapProduce.selecteds.length);
    if (mapProduce.selecteds.length) {
        // preparar grupos existentes para seleção a partir dos grupos que estão em produção
        $('#divSelect .dropdown-menu').empty();
        if (inProduction && inProduction.groups.length) {
            $('#divSelect .dropdown-menu').append('<h6 class="dropdown-header">Selecionar existente</h6>');
            inProduction.groups.forEach(g => {
                let el = `<button type="button" class="dropdown-item" onclick="mapProduce.doGroup(event)" data-id="${g.groupId}">Grupo #${g.groupId}</button>`;
                $('#divSelect .dropdown-menu').append(el);
            });
        }
        else {
            $('#divSelect .dropdown-menu').append('<h6 class="dropdown-header">Não há grupos em produção</h6>');
        }
        $('#divSelect').show();
    }
    else
        $('#divSelect').hide();
}

// essa função é chamada no sd.init.js, de acordo com a aba selecionada
function initTabToProduce() {
    mapProduce.getOrders();
}

$(function () {
    // Evento para selecionar pela grid
    $(document).on('click', '[data-select-to-produce]', function (e) {
        mapProduce.toggleSelect($(this).data('orderid'));
    });

    // Evento para classificar pela grid
    $(document).on('click', '[data-sortby]', function (e) {
        mapProduce.sortOrdersElementsHtmlBy($(this).data('sortby'), $(this).data('sortasc'));
    });
});

// Show or Hide Side Panel Right
function togglePanelRight(show) {
    if (show || document.getElementById("mySidepanel").style.width !== "220px") {
        mapProduce.getGroupsSuggestionsRoboV2();
        document.getElementById("mySidepanel").style.width = "220px";
    }
    else {
        document.getElementById("mySidepanel").style.width = "0";
    }
}