﻿@using Consumer.Resources.Mobile.Comanda.Views.Home
@using RAL.Common;
@using Consumer.Services.V20.Pedidos.Extensoes;

@model RAL.Consumer.Mobile.Areas.Comanda.Models.PedidoDetalheViewModel
@{
    ViewBag.Title = @DetailsRes.Mesa + Model.Pedido.Numero;
    int colsButtons = ViewBag.HabilitarPagamento == true ? 3 : 4;
}

<div>
    @Html.LabelFor(model => model.Pedido.NomeTelefoneCliente, @DetailsRes.Cliente, htmlAttributes: new { @class = "control-label" }):
    @Html.DisplayFor(model => model.Pedido.NomeTelefoneCliente, new { htmlAttributes = new { @class = "form-control" } })
</div>
@if (!string.IsNullOrWhiteSpace(Model.Pedido.Observacao))
{
    <div>
        @Html.LabelFor(model => model.Pedido.Observacao, @DetailsRes.Observacoes, htmlAttributes: new { @class = "control-label" }):
        @Html.DisplayFor(model => model.Pedido.Observacao, new { htmlAttributes = new { @class = "form-control" } })
    </div>
}
<div>
    @Html.LabelFor(model => model.Pedido.QuantidadePessoas, @DetailsRes.QtdPessoas, htmlAttributes: new { @class = "control-label" }):
    @Html.DisplayFor(model => model.Pedido.QuantidadePessoas, new { htmlAttributes = new { @class = "form-control" } })
</div>
@if (Model.Pedido.CardapioDigitalSenha != null)
{
    <div>
        @Html.LabelFor(model => model.Pedido.CardapioDigitalSenha, @DetailsRes.SenhaCardapioQr, htmlAttributes: new { @class = "control-label" }):
        @Html.DisplayFor(model => model.Pedido.CardapioDigitalSenha, new { htmlAttributes = new { @class = "form-control" } })
    </div>
}

<div>
    <h4>Itens</h4>
    @if (Model.Itens.Count() > 0)
    {
        <table class="table table-condensed table-responsive tabela-pedido-itens">
            @foreach (var item in Model.Itens)
            {
                var subtotalDescendentes = 0M;
                <tr class="linha-pai">
                    <td @(item.Content.Principal.Item.Quantidade == 0 ? "colspan=4" : "")>
                        <i class="glyphicon glyphicon-chevron-right"></i>
                        @Html.Raw(item.Content.Principal.Item.NomeProduto)
                        @{
                            string obs = item.Content.Principal.Item.FormatarObservacao();
                            if (!string.IsNullOrWhiteSpace(obs))
                            {
                                <i class="small">*@obs</i>
                            }
                        }
                    </td>
                    @if (item.Content.Principal.Item.Quantidade > 0)
                    {
                        <td class="item-produto-valor">@item.Content.Principal.Item.ValorUnitario.GetValueOrDefault().ToString("C")</td>
                        <td class="item-produto-qtde">@item.Content.Principal.Item.Quantidade</td>
                        <td>@item.Content.Principal.Item.ValorItem.GetValueOrDefault().ToString("C")</td>
                    }
                </tr>
                //Complementos
                foreach (var complemento in item.Content.Principal.Complementos)
                {
                    subtotalDescendentes += complemento.Content.ValorTotal.GetValueOrDefault();
                    <tr class="linha-filho">
                        <td>
                            <i class="glyphicon glyphicon-plus-sign"></i>
                            @Html.Raw(complemento.Content.NomeProduto)
                            @{ string obsc = complemento.Content.FormatarObservacao();
                                if (!string.IsNullOrWhiteSpace(obsc))
                                {
                                    <i class="small">*@obsc</i>
                                }
                            }
                        </td>
                        <td>@complemento.Content.ValorUnitario.GetValueOrDefault().ToString("C")</td>
                        <td>@complemento.Content.Quantidade</td>
                        <td>@complemento.Content.ValorItem.GetValueOrDefault().ToString("C")</td>
                    </tr>
                }
                //Filho
                //foreach (var itemFilho in item.Filhos.OrderBy(c => Array.IndexOf(new int[] { 1, 3, 4, 5, 6, 7, 8 }, c.CodigoItemPedidoTipo)))
                foreach (var itemFilho in item.Content.Filhos)
                {
                    subtotalDescendentes += itemFilho.Content.Item.ValorTotal.GetValueOrDefault();
                    <tr class="linha-filho">
                        <td>
                            <i class="glyphicon glyphicon-@((itemFilho.Content.Item.TipoItem == PedidoItemTipos.ProdutoFracionado) ? "adjust" : "inbox")"></i>

                            @Html.Raw(itemFilho.Content.Item.NomeProduto)
                            @{ string obsf = itemFilho.Content.Item.FormatarObservacao();
                                if (!string.IsNullOrWhiteSpace(obsf))
                                {
                                    <i class="small">*@obsf</i>
                                }
                            }
                        </td>
                        <td>@itemFilho.Content.Item.ValorUnitario.GetValueOrDefault().ToString("C")</td>
                        <td>@itemFilho.Content.Item.Quantidade</td>
                        <td>@itemFilho.Content.Item.ValorItem.GetValueOrDefault().ToString("C")</td>
                    </tr>
                    //Neto
                    foreach (var itemNeto in itemFilho.Content.Complementos)
                    {
                        subtotalDescendentes += itemNeto.Content.ValorTotal.GetValueOrDefault();
                        <tr class="linha-neto">
                            <td>
                                <i class="glyphicon glyphicon-plus-sign"></i>
                                @Html.Raw(itemNeto.Content.NomeProduto)
                                @{ string obsn = itemNeto.Content.FormatarObservacao();
                                    if (!string.IsNullOrWhiteSpace(obsn))
                                    {
                                        <i class="small">*@obsn</i>
                                    }
                                }
                            </td>
                            <td>@itemNeto.Content.ValorUnitario.GetValueOrDefault().ToString("C")</td>
                            <td>@itemNeto.Content.Quantidade</td>
                            <td>@itemNeto.Content.ValorItem.GetValueOrDefault().ToString("C")</td>
                        </tr>
                    }
                }
                <tr class="linha-subtotal">
                    <td>
                    </td>
                    <td colspan="3">
                        <strong>@(item.Content.Principal.Item.ValorTotal.GetValueOrDefault().ToString("C"))</strong>
                        <a class="btn btn-xs btn-default addonemore" style="border-radius: 50%"
                           href="/comanda/pedido/addonemore/@item.Content.Principal.Item.Codigo?codigopedido=@Model.Pedido.Codigo"
                           data-item="@item.Content.Principal.Item.Codigo"
                           data-pedido="@Model.Pedido.Codigo">
                            <i class="glyphicon glyphicon-plus text-success"></i>
                        </a>
                    </td>
                </tr>
            }
            @if (ViewBag.ProcessandoNovosItens == true || Model.ResumoValores == null)
            {
                <tr class="linha-processando">
                    <td colspan="4">
                        <div class="loader-content">
                            <div class="loader"></div>
                            <div class="loader-text">Processando, aguarde os totais...</div>
                        </div>
                    </td>
                </tr>
            }
            else
            {
                <tr class="linha-total">
                    <td>
                        @DetailsRes.Subtotal
                    </td>
                    <td colspan="3">
                        @Model.ResumoValores.Totais.ValorItens.ToString("C")
                    </td>
                </tr>
                if (Model.ResumoValores.Totais.ValorServico > 0)
                {
                    <tr class="linha-total">
                        <td>
                            @DetailsRes.TotalServico
                        </td>
                        <td colspan="3">
                            @Model.ResumoValores.Totais.ValorServico.Value.ToString("C")
                        </td>
                    </tr>
                }
                if (Model.ResumoValores.Totais.ValorIva > 0)
                {
                    <tr class="linha-total">
                        <td>
                            @DetailsRes.TotalIva
                        </td>
                        <td colspan="3">
                            @Model.ResumoValores.Totais.ValorIva.Value.ToString("C")
                        </td>
                    </tr>
                }
                if (Model.ResumoValores.Totais.ValorDesconto > 0)
                {
                    <tr class="linha-total">
                        <td>
                            @DetailsRes.Desconto
                        </td>
                        <td colspan="3">
                            @Model.ResumoValores.Totais.ValorDesconto.Value.ToString("C")
                        </td>
                    </tr>
                }

                <tr class="linha-total">
                    <td>
                        @DetailsRes.Total
                    </td>
                    <td colspan="3">
                        @Model.ResumoValores.Totais.ValorTotal.ToString("C")
                    </td>
                </tr>
            }

        </table>

        if (ViewBag.ProcessandoNovosItens == false &&
            Model.ResumoValores != null &&
            Model.ResumoValores.ValorTotalPago > 0
        )
        {
            <table class="table table-condensed table-responsive tabela-pedido-itens">
                <tr class="linha-total" style="background-color: rgb(225, 190, 231);">
                    <td>
                        @DetailsRes.ValorPago
                    </td>
                    <td colspan="3">
                        @Model.ResumoValores.ValorTotalPago.ToString("C")
                    </td>
                </tr>

                @if (Model.ResumoValores.ValorRestantePagar > 0)
                {
                    <tr class="linha-total" style="background-color: rgb(225, 190, 231);">
                        <td>
                            @DetailsRes.FaltaPagar
                        </td>
                        <td colspan="3">
                            @Model.ResumoValores.ValorRestantePagar.ToString("C")
                        </td>
                    </tr>
                }
            </table>
        }
        if (Model.IsCieloLio)
        {
            <div class="pull-right">
                <button class="btn btn-default" id="btn-imprimir-lio">Imprimir</button>
            </div>
        }
    }
    else
    {
        <p>@DetailsRes.NenhumItemEnviado</p>
    }
</div>

<nav id="actions" class="navbar navbar-default navbar-fixed-bottom">
    <div id="pedido-actions" class="container-fluid">
        <div class="col-xs-@colsButtons">
            <a id="btn-voltar" class="btn btn-lg btn-default btn-block navbar-btn" title="@DetailsRes.Voltar"><i class="glyphicon glyphicon-circle-arrow-left"></i> <span class="hidden-xs">@DetailsRes.Voltar</span><small class="visible-xs">@DetailsRes.Voltar</small></a>
        </div>
        @if (Model.Pedido.EmFechamento == false)
        {
            <div class="col-xs-@colsButtons">
                <a id="btn-conta-fechar" href="@Url.Action("PedirConta", "Home", new { CodigoPedido = Model.Pedido.Codigo })" class="btn btn-lg btn-success btn-block navbar-btn" title="@DetailsRes.PedirConta"><i class="glyphicon glyphicon-flag"></i> <span class="hidden-xs">@DetailsRes.Fechar</span><small class="visible-xs">@DetailsRes.Fechar</small></a>
            </div>
            if (ViewBag.HabilitarPagamento == true)
            {
                <div class="col-xs-@colsButtons">
                    <a id="btn-conta-pagar" href="@Url.Action("PagarConta", "Home", new { CodigoPedido = Model.Pedido.Codigo, Numero = Model.Pedido.Numero })" class="btn btn-lg btn-pagar btn-block navbar-btn" title="@DetailsRes.PagarConta"><i class="glyphicon glyphicon-usd"></i> <span class="hidden-xs">@DetailsRes.Pagar</span><small class="visible-xs">@DetailsRes.Pagar</small></a>
                </div>
            }
            <div class="col-xs-@colsButtons">
                <a id="btn-pedido-add" href="@Url.Action("Create", "Pedido", new { Numero = Model.Pedido.Numero })" class=" btn btn-lg btn-primary btn-block navbar-btn" title="@DetailsRes.AdicionarItens"><i class="glyphicon glyphicon-plus"></i> <span class="hidden-xs">@DetailsRes.Novo</span><small class="visible-xs">@DetailsRes.Novo</small></a>
            </div>
        }
        else
        {
            <div class="col-xs-@(12 - colsButtons)">
                <a id="btn-conta-reabrir" href="@Url.Action("PedirConta", "Home", new { CodigoPedido = Model.Pedido.Codigo, Pedir = "N", Numero = Model.Pedido.Numero })" class="btn btn-lg btn-warning btn-block navbar-btn" title="@DetailsRes.ReabrirConta"><i class="glyphicon glyphicon-refresh"></i> <span class="hidden-xs">@DetailsRes.Reabrir</span><small class="visible-xs">@DetailsRes.Reabrir</small></a>
            </div>
        }        
    </div>
</nav>

@section Scripts
{
    <script>
        $(function () {
            $('.addonemore').click(function (e) {
                e.preventDefault();
                $(this).addClass('disabled');
                location.href = '/comanda/pedido/addonemore/' + $(this).data('item') + '?codigopedido=' + $(this).data('pedido');
            });
        });

        $(document).ready(function () {
            setTimeout(function () {
                window.location.reload();
            }, 10000);
        })

        $("#btn-imprimir-lio").on("click", function () {
            window.open(`/cielolio/print?codigoPedido=${@Model.Pedido.Codigo}`, '_self')
        })
    </script>
}