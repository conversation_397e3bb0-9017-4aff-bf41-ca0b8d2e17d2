﻿@using Consumer.Resources.Mobile.Comanda.Views.Pedido
@model IEnumerable<RAL.Consumer.Mobile.Areas.Comanda.Models.ProdutoPartialViewModel>

@if (Model.Count() == 0)
{
    <div class="col-xs-12">
        <div class="alert alert-warning">
            <i class="glyphicon glyphicon-warning-sign"></i> @ProdutoPartialRes.NenhumProdutoEncontrado
        </div>
    </div>
}
else
{
    bool exibeCategoria = Model.Select(p => p.CodigoCategoria).Distinct().Count() > 1;


    if (!exibeCategoria)
    {
        <h6>@Model.FirstOrDefault().NomeCategoria</h6>
    }

    foreach (var item in Model)
    {
        <div class="col-xs-6 item-produto-container" style="padding-bottom: 10px" 
             data-nomeproduto="@item.NomeProduto" data-qtdvendida="0">

            <a id="<EMAIL>" 
               class="item-produto @(item.Indisponivel ? " disabled" : "")" 
               data-codigoproduto="@item.CodigoProduto" 
               data-personalizavel="@Json.Encode(item.Personalizavel)" 
               data-combo="@Json.Encode(item.IsCombo)" 
               data-wizard="@Json.Encode(item.HasWizard)"
               data-nomeproduto="@item.NomeProduto" 
               data-precovenda="@item.PrecoVenda" 
               data-precocusto="@item.PrecoCusto" 
               data-itemporkg="@Json.Encode(item.PorKgOuLt)" 
               data-estoquecontrolado="@Json.Encode(item.EstoqueControlado)" 
               data-estoqueatual="@item.QtdEstoque" 
               title="@item.NomeProduto">

                <div class="alert-info" style="height:80px; color:white; @(item.Indisponivel ? " opacity:0.7" : "")">
                    @if (exibeCategoria)
                    {
                        <div style="font-size: 8px;"><span style="background-color:#808080; padding:2px 4px">@(item.NomeCategoria)</span></div>
                    }
                    <div class="" style="padding:4px 4px 4px 10px; line-height: normal;">
                        @if (item.PrecoVenda.HasValue && item.Indisponivel)
                        {
                            <span class="label label-danger small"><i class="fa fa-remove" title="@Consumer.Resources.Mobile.Comanda.Views.Pedido.ProdutoPartialRes.Indisponivel"></i><span class="hidden-xs"> @ProdutoPartialRes.Indisponivel</span></span>
                        }
                        @item.NomeProduto
                    </div>
                    <div class="item-produto-precovenda text-right" style="font-size: 10px; position:absolute; bottom:10px; right:5px"><span style="background-color:#4caf50; padding:3px">@item.PrecoVendaFormatado</span></div>
                </div>
            </a>
        </div>
    }
}

<!-- Modal -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-backdrop="static">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="myModalLabel">@ProdutoPartialRes.QtdGramas</h4>
            </div>
            <div class="modal-body">
                <form id="myForm">
                    <div class="form-group">
                        <input type="tel" id="item-produto-qtde" name="itemprodutoqtde" class="form-control">
                        <small>@ProdutoPartialRes.ExemploKg</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button id="btnCancelarPorKg" type="button" class="btn btn-lg btn-default" data-dismiss="modal" tabindex="-1">@ProdutoPartialRes.Cancelar</button>
                <button id="btnAdicionarPorKg" type="button" class="btn btn-lg btn-success">@ProdutoPartialRes.Adicionar</button>
            </div>
        </div>
    </div>
</div>