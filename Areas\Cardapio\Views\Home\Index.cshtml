﻿@using Consumer.Resources.Mobile.Cardapio.Views.Home
@model RAL.Consumer.Data.Entities.PEDIDOS
@using RAL.Consumer.Data.Context
@{
    ViewBag.Title = @IndexRes.MinhaConta;
}

<div class="panel b serrilhado">
    <div class="panel-heading pt">
        <h4 class="panel-title text-center">
            @IndexRes.MesaComanda @Model.NUMERO
        </h4>
    </div>
    <div class="panel-body">
        @if (Model.CONTASOLICITADA == "S")
        {
        <div role="alert" class="alert alert-warning bg-warning-light">
            <i class="fa fa-spinner fa-spin"></i> @IndexRes.ContaFechamento
        </div>
        }

        @if (Model.ITENSPEDIDO.Listar().Count() > 0)
        {
            <div class="table-responsive">
                <table class="table table-condensed">
                    <colgroup>
                        <col span="1" class="order-item-name">
                        <col span="1" class="order-qty">
                        <col span="1" class="order-price">
                        <col span="1" class="order-total">
                    </colgroup>
                    <thead class="bg-gray-lighter">
                        <tr>
                            <th colspan="2">Item</th>
                            <th class="order-price hidden-xs">V.Unit</th>
                            <th class="order-total">@IndexRes.Total</th>
                        </tr>
                    </thead>
                    <tfoot>
                        <tr class="order-subtotal text-right">
                            <td colspan="2">@IndexRes.Subtotal</td>
                            <td colspan="2">@Model.ITENSPEDIDO.Listar().Sum(i => i.QUANTIDADE * i.VALORUNITARIO).GetValueOrDefault().ToString("C")</td>
                        </tr>
                        @*<tr class="order-subtotal">
                                <td colspan="3">(+) Entrega</td>
                                <td colspan="2">$0.00</td>
                            </tr>*@
                        @if (Model.ITENSPEDIDO.Listar().Sum(i => i.QUANTIDADE * i.ValorUnitarioServico).GetValueOrDefault() > 0)
                        {
                            <tr class="order-subtotal text-right">
                                <td colspan="2">@IndexRes.Servico</td>
                                <td colspan="2">@Model.ITENSPEDIDO.Listar().Sum(i => i.QUANTIDADE * i.ValorUnitarioServico).GetValueOrDefault().ToString("C")</td>
                            </tr>
                        }
                        @*<tr class="order-subtotal">
                                <td colspan="3">(-) Desconto</td>
                                <td colspan="2">$1540.00</td>
                            </tr>*@
                        <tr class="order-subtotal text-right">
                            <td colspan="2">@IndexRes.TotalFinal</td>
                            <td colspan="2">@((Model.ITENSPEDIDO.Listar().Sum(i => i.QUANTIDADE * i.VALORUNITARIO).GetValueOrDefault() + Model.ITENSPEDIDO.Listar().Sum(i => i.QUANTIDADE * i.ValorUnitarioServico).GetValueOrDefault()).ToString("C"))</td>
                        </tr>
                    </tfoot>
                    <tbody>
                        @foreach (var item in Model.ITENSPEDIDO.Listar().Where(i => !i.CODIGOPAI.HasValue))
                        {
                            @*<tr>
                                    <td class="order-qty">@item.QUANTIDADE.GetValueOrDefault().ToString("#x")</td>
                                    <td class="order-item-name">@Html.Raw(item.NomeComDetalheQuebraLinha)</td>
                                    <td class="order-price hidden-xs">@item.VALORUNITARIO.GetValueOrDefault().ToString("C")</td>
                                    <td class="order-total">@((item.VALORUNITARIO * item.QUANTIDADE).GetValueOrDefault().ToString("C"))</td>
                                </tr>*@
                            @Html.Partial("_ItemPedidoRow", item)
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
               <p>@IndexRes.FacaPedido</p>

               <a href="@Url.Action("Categoria", "Produto")" class=" btn btn-lg btn-primary btn-block btn-square" title="@IndexRes.AdicionarItens"><i class="fa fa-plus"></i><br />@IndexRes.Pedir</a>
        }
    </div>
</div>

@if (Model.ITENSPEDIDO.Listar().Count() > 0)
{
    @section BodyArea {
        <nav id="actions" class="navbar navbar-default navbar-fixed-bottom">
            <div class="col-xs-4">
                    <button id="btnChamarGarcom" class="btn btn-lg btn-block btn-square btn-warning navbar-btn" title="@IndexRes.ChamarGarcom"><i class="fa fa-bell"></i><br /><span class="hidden-xs">@IndexRes.Chamar</span>@IndexRes.Garcom</button>
            </div>
            @if (Model.CONTASOLICITADA != "S" && !Model.DATAFECHAMENTO.HasValue)
            {
                <div class="col-xs-4">
                    @using (Html.BeginForm("PedirConta", "Home", FormMethod.Post, new { id = "formPedirConta" }))
                    {
                    <button id="btnPedirConta" class="btn btn-lg btn-block btn-square btn-danger navbar-btn" title="@IndexRes.PedirConta"><i class="fa fa-flag"></i><br />@IndexRes.Fechar<span class="hidden-xs">@IndexRes.ContaFinal</span></button>
                    }
                </div>
                <div class="col-xs-4">
                    <a href="@Url.Action("Categoria", "Produto")" class="btn btn-lg btn-block btn-square btn-success navbar-btn" title="@IndexRes.AdicionarItens"><i class="fa fa-plus"></i><br />@IndexRes.Pedir</a>
                </div>
            }
            else
            {
                <div class="col-xs-8">
                    <a href="@Url.Action("Avaliar", "Home")" class="btn btn-lg btn-block btn-square btn-success navbar-btn" title="@IndexRes.AvaliarAtendimento"><i class="fa fa-star"></i><br />@IndexRes.AvaliarAtendimento</a>
                </div>
            }
        </nav>
    }
}

@section Scripts
{
    <script>
        $(document).ready(function () {
            $(document).on("click", "#btnPedirConta", function (e) {
                e.preventDefault();
                swal({
                    title: "@IndexRes.FecharConta",
                    text: '@Html.Raw(IndexRes.Confirmacao)',
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonClass: "btn-success",
                    confirmButtonText: "@IndexRes.Conta",
                    cancelButtonText: "@IndexRes.Voltar",
                    closeOnConfirm: false
                },
                    function () {
                        $.displayOverlay();;
                        $("#formPedirConta").submit();
                    }
                );
            });

            $(document).on("click", "#btnChamarGarcom", function (e) {
                chamarGarcom(e);
            });
        });
    </script>
}

