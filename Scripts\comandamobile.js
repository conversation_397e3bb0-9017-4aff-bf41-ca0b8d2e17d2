﻿function initValidations() {
    // por padrão nossas aplicações já validam em "pt-BR", então se não for "pt-BR", isto é, separador de decimal ".", vamos voltar as validações originais
    if (appCurrent.DecimalSeparator !== ",") {
        jQuery.extend(jQuery.validator.methods, {
            date: function (value, element) {
                return this.optional(element) || !/Invalid|NaN/.test(new Date(value).toString());
            },
            number: function (value, element) {
                return this.optional(element) || /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(value);
            }
        });
        $('[data-money="money"]').maskMoney({ selectAllOnFocus: true });
    }
    else {
        $('[data-money="money"]').maskMoney({ thousands: '.', decimal: appCurrent.DecimalSeparator, selectAllOnFocus: true });
    }
}

function obterProdutos(codigoCategoria, query) {

    $.get("/Comanda/Pedido/ObterProdutos", "codigoCategoria=" + codigoCategoria + "&query=" + query, function (data) {
        $('#list-categoria').hide();
        $('#list-produto').show();
        $('#list-produto').off();
        $('#list-produto').children().off();
        $('#list-produto').empty();
        $('#list-produto').html(data);
        $('#sort-options').show();

        $("#list-produto").on("click", "a.item-produto", function (e) {
            e.preventDefault();

            var src = $(this);
            src.attr('disabled', 'disabled');

            if (!$(this).hasClass("disabled")) {
                $('#item-produto-selected').addClass("hide");
                $("#item-produto-selected .item-produto-data").removeData();
                $("#item-produto-selected .item-produto-data").data($(this).data());
                var dados = $("#item-produto-selected .item-produto-data").data();
                if (dados.personalizavel) { // Produto Por Tamanho
                    location.href = "AddProdutoTamanho/" + dados.codigoproduto;
                }
                else if (dados.combo) { // Produto combo
                    location.href = "AddCombo/" + dados.codigoproduto;
                }
                else if (dados.wizard && !dados.itemporkg) { // Produto wizard
                    location.href = "/Comanda/Wizard/Add/" + dados.codigoproduto;
                }
                else // Produto Normal
                {
                    $("#item-produto-selected .item-produto-data .item-produto-nome").text($(this).children().first().text());
                    $("#item-produto-selected .item-produto-data .item-produto-qtde").text("");
                    //$("#item-produto-selected").removeClass("hide");

                    dados.qtde = 1;
                    if (!dados.itemporkg)
                        addProduto(dados, src);
                    else {
                        $('#myModal').modal();
                    }
                }
            }
            else {
                alert("Estoque insuficiente!");
            }
        });

        $('#myModal').on('shown.bs.modal', function () {
            $('#item-produto-qtde').focus();
            $('#item-produto-qtde').on('keyup keypress', function (e) {
                var keyCode = e.keyCode || e.which;
                if (keyCode === 13) {
                    e.preventDefault();
                    $("#btnAdicionarPorKg").focus();
                    return false;
                }
                else if ((e.keyCode < 48 || e.keyCode > 57) && (e.keyCode < 96 || e.keyCode > 105)) {
                    e.preventDefault();
                    return false;
                }
            });
        });

        $("#btnCancelarPorKg").on("click", "", function () {
            $("#item-produto-selected").addClass("hide");
        });

        $("#btnAdicionarPorKg").on("click", "", function (e) {
            e.preventDefault();

            var src = $(this);
            src.attr('disabled', 'disabled');

            var qtdInformada = Number($("#item-produto-qtde").val()) / 1000;
            if (qtdInformada > 0) {
                $("#item-produto-selected .item-produto-data").data($(this).data());
                var dados = $("#item-produto-selected .item-produto-data").data();
                dados.qtde = qtdInformada.toString().replace(".", ",");
                addProduto(dados, src);
                $('#myModal').modal('hide');
            }
        });
    }).success(function (data) {
    }).done(function () {
    });
}

function addProduto(dados, src) {
    $.post("/Comanda/Pedido/AddOrDel", { qtde: dados.qtde, codigoProduto: dados.codigoproduto }, function (data) {
        updateItem(data, $('#item-produto-selected .item-produto-data'));
        $("#item-produto-selected").removeClass("hide");
    }).done(function () {
        src.removeAttr('disabled');
    });
}

function ajustarQtd(elto, item) {
    var total = item.ValorTotal.toLocaleString("pt-BR", { minimumFractionDigits: 2 });

    $(elto).find(".item-produto-qtde").text(item.Quantidade);
    $(elto).find(".item-produto-valor-total").text("R$" + total);
}

function updateItem(data, el) {
    var element = null;
    if (el) {
        element = el;
        $('.btn-add, .btn-del, .btn-del-all, .btn-obs').data("codigo", data.Sequencia.Sequence);
        if (element.data("combo")) {
            $('.btn-obs').hide();
        }
        else {
            $('.btn-obs').show();
        }
    }
    else {
        element = $('#item-pedido-' + data.Sequencia.Sequence);
    }

    var principal = data.Sequencia.Content.Principal;
    if (principal.Item.Quantidade > 0) {
        $(element).attr("id", "item-pedido-" + data.Sequencia.Sequence);
        $(element).data("codigo", data.Sequencia.Sequence);
        ajustarQtd(element, principal.Item);

        data.Sequencia.Content.Filhos.forEach(function (filho, index, ar) {
            var elementFilho = $('#item-pedido-' + data.Codigo + '-' + filho.Codigo);
            if (elementFilho.length > 0) {
                $(elementFilho).find(".item-produto-qtde").text(filho.Value.Quantidade);
                $(elementFilho).find(".item-produto-valor-total").text("R$" + (filho.Quantidade * $(elementFilho).data().precovenda.toString().replace(',', '.')).toLocaleString("pt-BR", { minimumFractionDigits: 2 }));
                filho.ItensNetos.forEach(function (neto, indexNeto, arNeto) {
                    var elementNeto = $('#item-pedido-' + data.Codigo + '-' + filho.Codigo + '-' + neto.Codigo);
                    if (elementNeto.length > 0) {
                        $(elementNeto).find(".item-produto-qtde").text(neto.Quantidade);
                        $(elementNeto).find(".item-produto-valor-total").text("R$" + (neto.Quantidade * $(elementNeto).data().precovenda.toString().replace(',', '.')).toLocaleString("pt-BR", { minimumFractionDigits: 2 }));
                    }
                });
            }
        });

        //var subtotal = 0;
        //$('.item-pedido-principal-' + data.Sequencia.Sequence + ' .item-produto-valor-total').each(function (index, elem) {
        //    subtotal += Number($(elem).text().split('.').join('').replace(',', '.').replace('R$', ''));
        //});
        /*$('.item-pedido-principal-' + data.Codigo + ' .item-produto-subtotal strong').text("R$" + subtotal.toLocaleString("pt-BR", { minimumFractionDigits: 2 }));*/
        $('.item-pedido-principal-' + data.Sequencia.Sequence + ' .item-produto-subtotal strong').text("R$" + principal.Item.ValorTotal.toLocaleString("pt-BR", { minimumFractionDigits: 2 }));
    }
    else {
        if (el) { //View Create
            $(element).data(null);
            $(element).find(".item-produto-nome").text("");
            $(element).find(".item-produto-qtde").text("0");
            $(element).parent().addClass("hide");
        } else {
            $('#item-produto-selected').addClass("hide");
            $('.item-pedido-principal-' + data.Sequencia.Sequence).remove();
            if ($('.tabela-pedido-itens tr').length === 0)
                $('.tabela-pedido-itens').append("<tr><td>Não há itens para revisar.</td></tr>"); //Sem itens
        }
    }
    $("#item-produto-qtde").val(null); //Por KG
}

var showLoading = true;
$(document).ready(function () {

    initCultureApp();
    initValidations();

    $('[data-money="money"]').maskMoney('mask');

    $(document).ajaxStart(function () {
        if (showLoading)
            $.displayOverlay();
        showLoading = true;
    });

    $(document).ajaxStop(function () {
        $.removeOverlay();
        $('button,input[type="submit"]').removeAttr('disabled');
    });

    $(document).ajaxError(function (e, xhr, settings, thrownError) {
        var url = settings.url;
        //location.href = location.href;
        console.log('Erro na requisição para:', url);
        console.log('Erro:', thrownError);
    });

    //Desabilita button após submit
    $(document).on('submit', 'form', function () {
        if ($(this).valid()) {
            $('button,input[type="submit"]').attr('disabled', 'disabled');
        }
    });

    $("a.item-categoria").on("click", "", function () {
        var codigo = this.dataset.codigo;
        obterProdutos(codigo);
    });

    $("#btn-pedido-cancel").on("click", function (e, s) {
        if (!confirm("Cancelar pedido atual?"))
            e.preventDefault();
    });

    $("#btn-voltar").on("click", "", function () {
        if (location.href.indexOf('Pedido/Create') > -1 && $('#list-categoria:visible').length === 0) {
            $('#list-produto').hide();
            $('#item-produto-selected').addClass("hide");
            $('#list-categoria').show();
        }
        else if (location.href.indexOf('Comanda/Pedido/Review') > -1) {
            var numero = $('#Pedido_Numero').val();
            window.location.href = "../Pedido/Create?Numero=" + numero;
        }
        else
            history.back();
    });

    $(".item-produto-actions .btn-add").on("click", "", function (e) {        
        e.preventDefault();
        
        var src = $(this);
        src.attr('disabled', 'disabled');

        var element = $('#item-pedido-' + $(this).data("codigo"));
        var dados = $(element).data();
        dados.qtde = 1;
        $.post("/Comanda/Pedido/AddOrDel", { qtde: dados.qtde, codigo: dados.codigo }, function (data) {
            if (element.data('combo') === true) {
                location.reload();
            }
            else if (data.Sequencia.Sequence != dados.codigo) {
                location.reload();
            }
            else {
                updateItem(data);
            }
        }).done(function() {
            src.removeAttr('disabled');
        });
    });

    $(".item-produto-actions .btn-del").on("click", "", function (e) {
        e.preventDefault();

        var src = $(this);
        src.attr('disabled', 'disabled');

        var element = $('#item-pedido-' + $(this).data("codigo"));
        var dados = $(element).data();
        dados.qtde = -1;
        $.post("/Comanda/Pedido/AddOrDel", { qtde: dados.qtde, codigo: dados.codigo }, function (data) {
            updateItem(data);
        }).done(function () {
            src.removeAttr('disabled');
        });;
    });

    $(".item-produto-actions .btn-del-all").on("click", "", function (e) {
        e.preventDefault();

        var src = $(this);
        src.attr('disabled', 'disabled');

        var element = $('#item-pedido-' + $(this).data("codigo"));
        var dados = $(element).data();
        $.post("/Comanda/Pedido/DelAll", dados, function (data) {
            //updateItem({ Quantidade: 0, Codigo: dados.codigo });
            //$('#item-produto-selected').addClass("hide");
            $('.item-pedido-principal-' + dados.codigo).remove();
        }).done(function () {
            src.removeAttr('disabled');
        });;
    });

    //$(".item-produto-actions .btn-obs").on("click", "", function () {
    $(".btn-obs").on("click", "", function () {
        //var element = $('#item-pedido-' + $(this).data("codigo"));
        var dados = $(this).data();

        var origem = "";
        if (dados.origem)
            origem = "&origem=" + dados.origem;

        var link = "/Comanda/Pedido/ItemPedido/?codigo=" + dados.codigo;
        if (dados.codigofilho)
            link += "&codigoFilho=" + dados.codigofilho;

        location.href = link + origem;
    });

    //View AddProdutoTamanho
    $('select[name="SelectedPartes"]').change(function () {
        var max = $('input[name="Tamanho"]:checked').data("qtdmaximapartes");

        var $li = $(this).closest('li');
        var parteIndex = $li.data('parte');

        if (parteIndex < max - 1) {
            $li.next().toggleClass('hide', false);
        }

        var codigosProdutoDetalhe = selecionarCodigosProdutoDetalheMeioMeio();
        CarregaComplementosParaAgrupadorMeioMeio(codigosProdutoDetalhe);
    });

    function selecionarCodigosProdutoDetalheMeioMeio() {
        var resultado = [];

        if (parseInt($('#CodigoProdutoDetalhe').val()) > 0)
            resultado.push(parseInt($('#CodigoProdutoDetalhe').val()));

        if (parseInt($("input[name='Tamanho']:checked").data("codigoprodutodetalhe")) > 0)
            resultado.push(parseInt($("input[name='Tamanho']:checked").data("codigoprodutodetalhe")));

        $("select[name='SelectedPartes']").filter(function () {
            return parseInt(this.value) > 0;
        }).each(function () { resultado.push(parseInt($(this).val())) });

        return resultado;
    }

    function CarregaComplementosParaAgrupadorMeioMeio(codigosProdutoDetalhe) {
        //var codigosProdutoDetalhe = [];

        //if (parseInt($('#CodigoProdutoDetalhe').val()) > 0)
        //    codigosProdutoDetalhe.push(parseInt($('#CodigoProdutoDetalhe').val()));
        //if (parseInt($("input[name='Tamanho']:checked").data("codigoprodutodetalhe")) > 0)
        //    codigosProdutoDetalhe.push(parseInt($("input[name='Tamanho']:checked").data("codigoprodutodetalhe")));
        //$("select[name='SelectedPartes']").filter(function () {
        //    return parseInt(this.value) > 0;
        //}).each(function () { codigosProdutoDetalhe.push(parseInt($(this).val())) });

        $.ajax({
            data: { codigosProdutoDetalhe: codigosProdutoDetalhe }, // << Without JSON.stringify 
            traditional: true,  // << Important line
            url: '/comanda/pedido/ComplementosProdutoTamanhoFracionadoPrincipal',
            type: 'post',
            //dataType: 'json',
            success: function (resp) {
                $('#todosComplementos').html(resp);
            }
        });
    }

    function CarregarObservacoesParaAgrupadorMeioMeio(codigosProdutoDetalhe) {
        
        $.ajax({
            data: { codigosProdutoDetalhe: codigosProdutoDetalhe }, // << Without JSON.stringify 
            traditional: true,  // << Important line
            url: '/comanda/pedido/ObservacoesProdutoTamanhoFracionadoPrincipal',
            type: 'post',
            //dataType: 'json',
            success: function (resp) {
                $('#todasObservacoes').html(resp);
            }
        });
    }

    $('input[name="Tamanho"]').change(function () {
        var hasWizard = $('input[name="Tamanho"]:checked').data("wizard");

        if (hasWizard) {
            location.href = '/comanda/wizard/add/' + $('#CodigoProduto').val() + '?codigoTamanho=' + this.value;
            return;
        }

        var max = $('input[name="Tamanho"]:checked').data("qtdmaximapartes");

        $('#todasPartes').toggleClass("hide", max < 2);
        $('#todasPartesTitle').text(getLocalizedContent("AdicioneAteRes") + (max - 1) + getLocalizedContent("SaboresRes"));


        var $options = $("#todasPartesBase > option").clone();
        $('select[name="SelectedPartes"]').append($options);
        $('select[name="SelectedPartes"] option:not([data-codigotamanho="' + this.value + '"])').remove();
        $('select[name="SelectedPartes"]').prepend('<option value="">Outro sabor...</option>');
        $('select[name="SelectedPartes"]').prop("selectedIndex", 0);

        $('#selectedPartes li').toggleClass("hide", true);
        $('#selectedPartes li:first').toggleClass("hide", false);

        $('#todasObservacoes').empty();
        $('#todosComplementos').empty();

        var codigosProdutoDetalhe = selecionarCodigosProdutoDetalheMeioMeio();
        CarregarObservacoesParaAgrupadorMeioMeio(codigosProdutoDetalhe);
        CarregaComplementosParaAgrupadorMeioMeio(codigosProdutoDetalhe);
    });

    $("#q").on("keypress", "", function (e) {
        var code = e.keyCode ? e.keyCode : e.which;
        if (code === 13) { //Enter keycode                        
            e.preventDefault();
            obterProdutos(0, $(this).val());
        }
    });

    $("#btn-pesquisar").on("click", "", function () {
        obterProdutos(0, $("#q").val());
    });

    $("#item-todos-produtos").on("click", "", function () {
        obterProdutos(0, "");
    });

    $(document).on('click', '.btn-minus', function (e) {
        e.preventDefault();
        var input = $(this).parent().siblings('input');
        var minValue = parseInt($(input).data('val-range-min'));
        var newValue = parseInt($(input).val()) - 1;
        if (newValue < minValue) newValue = minValue;
        $(input).val(newValue).change();
    });

    $(document).on('click', '.btn-plus', function (e) {
        e.preventDefault();
        var input = $(this).parent().siblings('input');
        var maxValue = parseInt($(input).data('val-range-max'));
        var newValue = parseInt($(input).val()) + 1;
        if (newValue > maxValue) newValue = maxValue;
        $(input).val(newValue).change();
    });
});

$('#sort-options').on('click', '#btn-orderbyname', function () {

    $('.item-rank').hide();
    $('#btn-orderbyname').hide();
    $('#btn-orderbysells').show();

    var $divs = $(".item-produto-container");
    var alphabeticallyOrderedDivs = $divs.sort(function (a, b) {
        var an = $(a).data("nomeproduto").toUpperCase(),
            bn = $(b).data("nomeproduto").toUpperCase();

        if (an > bn) {
            return 1;
        }
        if (an < bn) {
            return -1;
        }
        return 0;
    });

    $("#list-produto").html(alphabeticallyOrderedDivs);

});

$('#sort-options').on('click', '#btn-orderbysells', function () {

    $('#btn-orderbysells').hide();
    $('#btn-orderbyname').show();

    var $divs = $(".item-produto-container");
    var numericallyOrderedDivs = $divs.sort(function (a, b) {
        var an = parseInt($(a).data("qtdvendida")),
            bn = parseInt($(b).data("qtdvendida"));

        if (an < bn) {
            return 1;
        }
        if (an > bn) {
            return -1;
        }
        return 0;
    });

    $(numericallyOrderedDivs).each(function (k, v) {
        $('.item-rank', v).html(String(k + 1) + "º");
    });

    $('.item-rank').show();

    $("#list-produto").html(numericallyOrderedDivs);

});

function createCliente() {
    var pesquisa = $('#Pedido_CodigoContatoCliente').data('select2').results.lastParams.term || '';
    $.ajax({
        url: '/Comanda/Pedido/CreateCliente?search=' + pesquisa,
        type: 'GET'
    }).done(function (result) {
        $('#modalCliente').remove();
        $('body').append(result);
        $('#modalCliente').modal();
        $.validator.unobtrusive.parse("#modalCliente");
        aplicarMascaras();
    });
}

function OnSuccessCreateCliente(data) {
    if (data && data.success) {
        if ($('#Pedido_CodigoContatoCliente').find("option[value='" + data.id + "']").length) {
            $('#Pedido_CodigoContatoCliente').val(data.id).trigger('change');
        } else {
            // Create a DOM Option and pre-select by default
            var newOption = new Option(data.text, data.id, true, true);
            // Append it to the select
            $('#Pedido_CodigoContatoCliente').append(newOption).trigger('change');
        }
    }
    else {
        $('#modalCliente').remove();
        $('body').append(data);
        $('#modalCliente').modal();
    }
}

function OnBeginCreateCliente() {
    $('#modalCliente').modal('hide');
    $('body').removeClass('modal-open');
    $('.modal-backdrop').remove();
}

function aplicarMascaras() {
    $('input[id*="Cep"]').mask('99999-999', { clearIfNotMatch: true });

    $('input[id*="Cep"]').on('click', function (event) {
        setCursorToStart(event);
    });

    $('input[id*="fone"]').mask('(99) 9999-9999?9', { placeholder: " " }).on('focusout', function (event) {
        var target, phone, element;
        target = event.currentTarget ? event.currentTarget : event.srcElement;
        phone = target.value.replace(/\D/g, '');
        element = $(target);
        var re = new RegExp("^\\d{2,3}(0{8,9}|1{8,9}|2{8,9}|3{8,9}|4{8,9}|5{8,9}|6{8,9}|7{8,9}|8{8,9}|9{8,9})$");
        if (!phone.match(re)) {
            element.unmask();
            if (phone.length > 10) {
                if (phone.substr(2, 1) !== "9")
                    element.val("");
                element.mask("(99) 99999-999?9", { placeholder: " " });
            } else {
                element.mask("(99) 9999-9999?9", { placeholder: " " });
            }
        }
        else
            element.val(null);
    });
}

function setCursorToStart(event) {
    var inputField = event.target;
    // Posiciona o cursor no início do campo de texto
    if (inputField.value == "" || inputField.value == "_____-___") 
        inputField.setSelectionRange(0, 0);
}

//Wizard
function validarPergunta($pergunta) {
    var errorMessage = null;
    var perguntaId = $pergunta.data('id');

    var $listOpcoes = $('[data-parent=' + perguntaId + ']');

    if ($listOpcoes.filter('input[type="radio"]').length > 0) { // Validar radio
        var radioChecked = $listOpcoes.filter('input[type="radio"]:checked').length;

        if (radioChecked < $pergunta.data('opc-min'))
            errorMessage = 'Selecione no mínimo ' + $pergunta.data('opc-min');

        if (radioChecked < $pergunta.data('item-min'))
            errorMessage = 'Selecione no mínimo ' + $pergunta.data('item-min');

        if ($pergunta.data('opc-max') > 0 && radioChecked > $pergunta.data('opc-max'))
            errorMessage = 'Selecione no máximo ' + $pergunta.data('opc-max');

    }
    else if ($listOpcoes.filter('input[type="checkbox"]').length > 0) { // Validar checkbox
        var checkboxChecked = $listOpcoes.filter('input[type="checkbox"]:checked').length;

        if (checkboxChecked < $pergunta.data('opc-min'))
            errorMessage = 'Selecione no mínimo ' + $pergunta.data('opc-min');

        if (checkboxChecked < $pergunta.data('item-min'))
            errorMessage = 'Selecione no mínimo ' + $pergunta.data('item-min');

        if ($pergunta.data('opc-max') > 0 && checkboxChecked > $pergunta.data('opc-max'))
            errorMessage = 'Selecione no máximo ' + $pergunta.data('opc-max');
    }
    else {  // Validar numbers
        var numberChecked = 0;
        var numberTotal = 0;
        $listOpcoes.filter('input[type="number"]').each(function () {
            var valorInformado = parseInt($(this).val()) || 0;

            numberTotal += valorInformado;

            if (valorInformado > 0)
                numberChecked++;
        });

        if (numberChecked < $pergunta.data('opc-min'))
            errorMessage = 'Selecione no mínimo ' + $pergunta.data('opc-min');

        if ($pergunta.data('opc-max') > 0 && numberChecked > $pergunta.data('opc-max'))
            errorMessage = 'Selecione no máximo ' + $pergunta.data('opc-max');

        if (numberTotal < $pergunta.data('item-min'))
            errorMessage = 'Escolha no mínimo ' + $pergunta.data('item-min');

        if ($pergunta.data('item-max') > 0 && numberTotal > $pergunta.data('item-max'))
            errorMessage = 'Escolha no máximo ' + $pergunta.data('item-max');
    }

    if (errorMessage) {
        ExibirErro(perguntaId, errorMessage);
        return false;
    }
    else {
        return true;
    }
}

function ExibirErro(perguntaId, mensagem) {
    var $el = $('[value=' + perguntaId + ']').parents('.panel');
    $(document).scrollTop($el.offset().top - 70);
    $el.find('input:visible:first').focus();
    $el.animo({
        animation: "shake"
    });
    var customVal = $el.closest('form').validate();
    var input = $('input[value="' + perguntaId + '"]');
    customVal.showErrors(JSON.parse('{ "' + $(input).attr('name') + '": "' + mensagem + '"}'));
}

$(document).ready(function () {

    obterEDefinirExibicaoValorDuracao();
    //obterEDefinirTamanhoMesas();
    //alterarTamanhoMesas();

    $(document).on('submit', '#formWizard', function (e) {
        var formValid = true;
        $('[data-pergunta]').each(function () {
            var isValid = validarPergunta($(this));

            if (!isValid) {
                formValid = false;
                return false;
            }
        });

        if (!formValid) {
            $('button,input[type="submit"]').removeAttr('disabled');
            e.preventDefault();
            return false;
        }
    });

    var timeoutId;

    $("#querymesa").on("keyup", function () {
        // Cancelar o temporizador existente se houver um
        if (timeoutId) {
            clearTimeout(timeoutId);
        }

        // Configurar um novo temporizador para aguardar 1 segundo
        timeoutId = setTimeout(function () {
            ListarMesasComandas();
        }, 1000);
    });

    $(document).on('change', 'input[type=checkbox][data-parent],input[type=radio][data-parent],input[type=number][data-parent]', function () {
        var parent = $(this).data('parent');

        var $pergunta = $('[data-id="' + parent + '"]');

        var qtd1 = $('input[type="checkbox"][data-parent="' + parent + '"]:checked').length;
        var qtd2 = $('input[type="radio"][data-parent="' + parent + '"]:checked').length;

        var qtd3 = 0;
        $('input[type="number"][data-parent="' + parent + '"]').each(function (index, element) {
            if ($(this).val() > 0)
                qtd3++;
        });

        var qtdChecked = qtd1 + qtd2 + qtd3;

        if ($pergunta.data('opc-max') > 0 && qtdChecked > $pergunta.data('opc-max')) {

            if ($(this).is('input[type="number"]'))
                $(this).prop('value', 0);
            else
                $(this).prop('checked', false);

            $(this).parent().animo({ animation: "shake" });
            $(this).parents('.panel').children('.panel-heading').animo({ animation: "shake" });
        }
    });

    /* js layout comanda */

    $(".toggleSwitch, #labelToggleSwitch").on("click", function () {
        exibirValorDuracao()
    });

    //$(document).on("input change", "#formControlRange", function () {
    //    alterarTamanhoMesas();
    //});

    //function alterarTamanhoMesas() {
    //    //var value = parseInt($("#formControlRange").val());
    //    //var max = parseInt($("#formControlRange").attr("max"));

    //    //if (value) {
    //    //    var largura = $(".listaDeMesas").width();

    //    //    var qtdCards = max + (value - 2) * (-1);

    //    //    var gaps = (qtdCards - 1) * 5;

    //    //    var cardWidth = (largura - gaps) / qtdCards;

    //    //    var calcCardWidth = cardWidth * .2

    //    //    $(".iconeMesaBloqueada").css("font-size", calcCardWidth + "px");

    //    //    if (cardWidth > 120) {
    //    //        $(".iconeMesaBloqueada").css("left", "20px");
    //    //    }

    //    //    $(".mesas").css("width", cardWidth);
    //    //    $(".mesas").css("height", cardWidth);

    //    //    if (value <= 3) {

    //    //        if (value == 1) {
    //    //            $(".numeroNome p").css("font-size", "1rem");
    //    //        } else {
    //    //            $(".numeroNome p").css("font-size", "1.1rem");
    //    //        }

    //    //        $(".abrirMesa").css("font-size", "2rem");
    //    //        $(".numeroNome p").css("margin-top", "-10px");
    //    //        $(".duracao").css("font-size", "1rem");
    //    //        $(".valor").css("font-size", "1rem");
    //    //    } else if (value > 3) {
    //    //        $(".abrirMesa").css("font-size", "3rem");
    //    //    }

    //    //    salvarTamanhoMesas(value);
    //    //}
    //}

    function exibirValorDuracao() {
        var value = parseInt($("#formControlRange").val());
        var max = parseInt($("#formControlRange").val());

        $(".duracao, .valor").toggleClass("hidden");

        if ($("#switch").hasClass("switchDisabled")) {
            $("#switch").removeClass("switchDisabled");
            $("#switch").addClass("switchEnabled");
            //salvarExibicaoValorDuracao('true');

            if (value > max - 1) {
                $("#formControlRange").val('4').change();
            }
        } else {
            $("#switch").addClass("switchDisabled");
            $("#switch").removeClass("switchEnabled");
            //salvarExibicaoValorDuracao('false');
        }
    }

    function salvarExibicaoValorDuracao(valor) {
        localStorage.setItem("exibirDuracaoValor", valor);
    }

    //function salvarTamanhoMesas(tamanho) {
    //    localStorage.setItem("tamanhosMesas", tamanho);
    //}
});

function obterEDefinirExibicaoValorDuracao() {
    var item = localStorage.getItem("exibirDuracaoValor");

    if (item != 'true') {
        $("#switch").addClass("switchDisabled");
        $("#switch").removeClass("switchEnabled");
        $(".duracao, .valor").addClass("hidden");
    }
    else {
        $("#switch").removeClass("switchDisabled");
        $("#switch").addClass("switchEnabled");
        $(".duracao, .valor").removeClass("hidden");

    }
}

//function obterEDefinirTamanhoMesas() {
//    var item = localStorage.getItem("tamanhosMesas");

//    if (item != undefined) {
//        $("#formControlRange").val(item).change();
//    }
//}

function ListarMesasComandas() {
    var txt = $('#querymesa').val().trim();

    $.ajax({
        url: '/Comanda/Home/ListarMesasComandas?id=' + txt,
        type: 'GET',
        cache: false,
        success: function (data) {
            $('#ListaComandas').html(data);
            //obterEDefinirTamanhoMesas();
            obterEDefinirExibicaoValorDuracao();
        },
        error: function () {
            alert("error");
            location.reload();
        }
    });
}