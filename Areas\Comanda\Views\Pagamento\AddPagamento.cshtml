﻿@using Consumer.Resources.Mobile.Comanda.Views.Home
@model RAL.Consumer.Mobile.Areas.Comanda.Models.AddPagamentoViewModel

@using RAL.Common

@{
    ViewBag.Title = Model.DescricaoFormaPagto ?? @AddPagamentoRes.AdicionarPagamento;
}

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()

    @Html.HiddenFor(model => model.CodigoPedido);
    @Html.HiddenFor(model => model.CodigoFormaPagto);
    @Html.HiddenFor(model => model.ValorRestante);
    @Html.HiddenFor(model => model.ValorTotal);

    <div class="form-group text-center">
        @Html.LabelFor(model => model.ValorRecebido, @AddPagamentoRes.InformeValorRecebido, htmlAttributes: new { @class = "control-label" })
        @Html.EditorFor(model => model.ValorRecebido, new { htmlAttributes = new { @class = "form-control input-lg text-center", data_money = "money", autocomplete = "off" } })
        @Html.ValidationMessageFor(model => model.ValorRecebido, "", new { @class = "text-danger small" })
        <p id="pTotalRestante">
            <label id="lblTotalRestante" class="control-label text-danger small">@AddPagamentoRes.Faltam</label>
            <span id="spnTotalRestante" data-valorOriginal="@Model.ValorRestante.ToString(System.Globalization.CultureInfo.InvariantCulture)" class="text-danger small">@Model.ValorRestante.ToString("C")</span>
        </p>
    </div>

    <div class="form-group text-center">
        <i class="text-muted small" style="font-size: 75%; margin-bottom: 0">@AddPagamentoRes.PreencherAutomatico</i>
        <div class="btn-group btn-group-justified" role="group" aria-label="...">
            @if (Model.QtdPessoas > 1)
            {
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-default" data-valorrecebido="@Model.ValorPorPessoa.ToString(System.Globalization.CultureInfo.InvariantCulture)">@Model.ValorPorPessoa.ToString("N2") (@Model.QtdPessoas @AddPagamentoRes.Pessoas)</button>
                </div>
            }
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-success" data-valorrecebido="@Model.ValorRestante.ToString(System.Globalization.CultureInfo.InvariantCulture)">@Model.ValorRestante.ToString("N2") @AddPagamentoRes.Faltando</button>
            </div>
        </div>
    </div>

    <div class="form-group text-center">
        <i class="text-muted small" style="font-size: 75%">@AddPagamentoRes.SomarAoClicar</i>
        <div class="btn-toolbar" role="toolbar" aria-label="..." style="display: inline-block">
            <div class="btn-group" data-toggle="buttons">
                <label class="btn btn-default">
                    <input type="checkbox" id="btnSomar" autocomplete="off"> +
                </label>
            </div>
            <div class="btn-group" role="group" aria-label="...">
                <button type="button" class="btn btn-default" data-valorpre="2">@(2M.ToString("N"))</button>
                <button type="button" class="btn btn-default" data-valorpre="5">@(5M.ToString("N"))</button>
                <button type="button" class="btn btn-default" data-valorpre="10">@(10M.ToString("N"))</button>
                <button type="button" class="btn btn-default" data-valorpre="20">@(20M.ToString("N"))</button>
                <button type="button" class="btn btn-default" data-valorpre="50">@(50M.ToString("N"))</button>
            </div>
        </div>
    </div>

    if (Model.ListaOperadoraCartao != null && Model.ListaOperadoraCartao.Count() > 0)
    {
        <div class="form-group">
            @if (Model.CodigoFormaPagto != (short)FormasPagamento.PixPagtoInstantaneo && Model.CodigoFormaPagto != (short)FormasPagamento.PixOnline)
            {
                @Html.LabelFor(model => model.CodigoOperadoraCartao, @AddPagamentoRes.Bandeira, htmlAttributes: new { @class = "control-label" })
            }
            else
            {
                @Html.LabelFor(model => model.CodigoOperadoraCartao, @AddPagamentoRes.ChavePIX, htmlAttributes: new { @class = "control-label" })
            }
            @Html.DropDownListFor(model => model.CodigoOperadoraCartao, Model.ListaOperadoraCartao, null, new { @class = "form-control" })
            @Html.ValidationMessageFor(model => model.CodigoOperadoraCartao, "", new { @class = "text-danger" })
        </div>
    }

    if (Model.CodigoFormaPagto == (short)FormasPagamento.PixPagtoInstantaneo)
    {
        <div class="form-group">
            @Html.LabelFor(model => model.NumeroAutorizacaoCartao, @AddPagamentoRes.NumeroAutorizacaoPIX, htmlAttributes: new { @class = "control-label" })
            @Html.EditorFor(model => model.NumeroAutorizacaoCartao, new { htmlAttributes = new { @class = "form-control text-center" } })
            @Html.ValidationMessageFor(model => model.NumeroAutorizacaoCartao, "", new { @class = "text-danger" })
        </div>
    }

    if (Model.CodigoFormaPagto == (short)FormasPagamento.CartaoCredito ||
        Model.CodigoFormaPagto == (short)FormasPagamento.CartaoDebito ||
        Model.CodigoFormaPagto == (short)FormasPagamento.ValeAlimentacao ||
        Model.CodigoFormaPagto == (short)FormasPagamento.ValeRefeicao)
    {
        <div class="form-group">
            @Html.LabelFor(model => model.CodigoPos, @AddPagamentoRes.CodigoPOS, htmlAttributes: new { @class = "control-label" })
            @if (Model.ListaPOS != null)
            {
                @Html.DropDownListFor(model => model.CodigoPos, Model.ListaPOS, null, new { @class = "form-control" })
            }
            else
            {
                @Html.HiddenFor(model => model.CodigoPos, new { htmlAttributes = new { @class = "form-control text-center" } })
            }
            @Html.ValidationMessageFor(model => model.CodigoPos, "", new { @class = "text-danger" })
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.NumeroAutorizacaoCartao, @AddPagamentoRes.NumeroAutorizacao, htmlAttributes: new { @class = "control-label" })
            @Html.EditorFor(model => model.NumeroAutorizacaoCartao, new { htmlAttributes = new { @class = "form-control text-center" } })
            @Html.ValidationMessageFor(model => model.NumeroAutorizacaoCartao, "", new { @class = "text-danger" })
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.NsuTransacao, @AddPagamentoRes.NSUTransacao, htmlAttributes: new { @class = "control-label" })
            @Html.EditorFor(model => model.NsuTransacao, new { htmlAttributes = new { @class = "form-control text-center" } })
            @Html.ValidationMessageFor(model => model.NsuTransacao, "", new { @class = "text-danger" })
        </div>
    }

    if (Model.CodigoFormaPagto == (short)FormasPagamento.PixOnline)
    {
        <div class="form-group">
            <a href="@Url.Action("AddPagamento", "Pagamento", new { formaPagamento = (short)FormasPagamento.PixPagtoInstantaneo, codigoPedido = Model.CodigoPedido, gerarQRCodePix = true, forcarPixManual = true })">@AddPagamentoRes.PixManual</a>
        </div>
    }

    <nav id="actions" class="navbar navbar-default navbar-fixed-bottom">
        <div id="pedido-actions" class="container-fluid">
            <div class="col-xs-6">
                <a id="btn-voltar" class="btn btn-lg btn-default btn-block navbar-btn" title="@AddPagamentoRes.Voltar"><i class="glyphicon glyphicon-circle-arrow-left"></i> <span class="hidden-xs">@AddPagamentoRes.Voltar1</span><small class="visible-xs">@AddPagamentoRes.Voltar2</small></a>
            </div>
            @if (Model.CodigoFormaPagto != (short)FormasPagamento.PixOnline)
            {
                if (Model.IsCieloLio)
                {
                    <div class="col-xs-6">
                        <button type="button" id="btn-pedido-cielolio" class="btn btn-lg btn-success btn-block navbar-btn" title="@AddPagamentoRes.Ok">
                            <i class="glyphicon glyphicon-check"></i>
                            <span class="hidden-xs">@AddPagamentoRes.Ok1</span>
                            <small class="visible-xs">@AddPagamentoRes.Ok2</small>
                        </button>
                    </div>
                }
                else
                {
                    <div class="col-xs-6">
                        <button class="btn btn-lg btn-success btn-block navbar-btn" title="@AddPagamentoRes.Ok"><i class="glyphicon glyphicon-check"></i> <span class="hidden-xs">@AddPagamentoRes.Ok1</span><small class="visible-xs">@AddPagamentoRes.Ok2</small></button>
                    </div>
                }

            }
            else
            {
                <div class="col-xs-6">
                    <button class="btn btn-lg btn-success btn-block navbar-btn" title="@AddPagamentoRes.GerarQrCode"><i class="glyphicon glyphicon-check"></i> <span class="hidden-xs">@AddPagamentoRes.GerarQrCode</span><small class="visible-xs">@AddPagamentoRes.GerarQrCode</small></button>
                </div>
            }
        </div>
    </nav>
}

@section Scripts{
    <script>
        $(document).ready(function () {
            $('#ValorRecebido').on('keyup keypress blur change input', function () {
                $('#ValorRecebido').valid();
                var customVal = $(this).parents("form").validate();
                var valorRecebido = $('#ValorRecebido').maskMoney('unmasked')[0];
                var valorRestante = $('#spnTotalRestante').data('valororiginal') - valorRecebido;
                //$("#pTotalRestante").toggleClass("hide", valorRestante <= 0)
                if (valorRestante >= 0) {
                    $('#lblTotalRestante').text('@AddPagamentoRes.Faltam');
                    $('#spnTotalRestante').text(`${appCurrent.CurrencySymbol} ` + valorRestante.toLocaleString(`${appCurrent.CultureFormat}`, { minimumFractionDigits: 2 }));
                    if (valorRecebido == 0) {
                        customVal.showErrors({
                            ValorRecebido: '@AddPagamentoRes.ValorMaiorQueZero'
                        });
                    }
                }
                else {
                    $('#lblTotalRestante').text('@Html.Raw(AddPagamentoRes.Maximo)');
                    $('#spnTotalRestante').text(`${appCurrent.CurrencySymbol} ` + $('#spnTotalRestante').data('valororiginal').toLocaleString(`${appCurrent.CultureFormat}`, { minimumFractionDigits: 2 }));

                    customVal.showErrors({
                        ValorRecebido: '@AddPagamentoRes.ValorExcedeQuantia'
                    });
                }
            });

            $('[data-valorpre]').click(function () {
                var valorAtual = $('#ValorRecebido').maskMoney('unmasked')[0];
                if ($('#btnSomar').prop("checked")) {
                    valorAtual += Number($(this).data("valorpre"));
                }
                else
                    valorAtual = Number($(this).data("valorpre"));
                $('#ValorRecebido').val(valorAtual.toLocaleString(appCurrent.CultureFormat, { minimumFractionDigits: 2 }));
                $("#ValorRecebido").trigger("change");
            });

            $('[data-valorrecebido]').click(function () {
                var valorAtual = Number($(this).data("valorrecebido"));
                $('#ValorRecebido').val(valorAtual.toLocaleString(appCurrent.CultureFormat, { minimumFractionDigits: 2 }));
                $("#ValorRecebido").trigger("change");
            });

            $("#btn-pedido-cielolio").on("click", function (e) {
                e.preventDefault();
                var valorRecebido = Number($('#ValorRecebido').maskMoney('unmasked')[0]);

                if (valorRecebido <= 0.01) {
                    var customVal = $(this).parents("form").validate();
                    customVal.showErrors({
                        ValorRecebido: '@AddPagamentoRes.ValorMaiorQueZero'
                    });
                    return;
                }

                window.open(`/pagamentolio?valorRecebido=${valorRecebido}&formaPagamento=${@Model.CodigoFormaPagto}&codigoPedido=${@Model.CodigoPedido}`, '_self')
            })
        });
    </script>
}
