﻿@model RAL.Consumer.Data.Entities.ITENSPEDIDO

@{
    Layout = null;
}
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <div class="pull-right mr">
        <a href="#" class="font btn btn-default" data-qtd="+2">A+</a>
        <a href="#" class="font btn btn-default" data-qtd="-2">A-</a>
    </div>
    <h4 class="modal-title">
        @Model.NOMEPRODUTO
    </h4>
</div>
<div class="modal-body">

    <div class="row">
        <div class="col-sm-2 hidden-xs">
            @if (System.IO.File.Exists(Server.MapPath(string.Format("~/Content/images/cardapio/{0}.jpg", Model.PRODUTODETALHE.PRODUTOS.CODIGO))))
            {
                <img src="@Url.Content(string.Format("~/Content/images/cardapio/{0}.jpg?q={1}", Model.PRODUTODETALHE.PRODUTOS.CODIGO, DateTime.Now.Ticks.ToString()))" alt="Foto" class="img-responsive center-block" />
            }
            else
            {
                <img src="@Url.Content(string.Format("~/Content/images/cardapio/{0}", ViewBag.ImagemPadrao))" alt="Foto" class="img-responsive center-block" />
            }
        </div>

        <div class="col-sm-10">
            <p class="text-bold">Descrição</p>
            @if (!string.IsNullOrWhiteSpace(Model.PRODUTODETALHE.PRODUTOS.DESCRICAO))
            {
                @Html.Raw(Model.PRODUTODETALHE.PRODUTOS.DESCRICAO)
            }
            else
            {
                <p>Descrição não cadastrada.</p>
            }
        </div>
    </div>

    <div class="row" style="margin-top:20px">
        <div class="col-sm-12">
            <p class="text-bold">Ficha Técnica para 1 Unidade</p>
            @if (Model.PRODUTODETALHE.ITENSDAFICHA.Any())
            {
                <table class="table table-condensed">
                    @foreach (var item in Model.PRODUTODETALHE.ITENSDAFICHA)
                    {
                        <tr>
                            <td>@item.PRODUTODETALHEITEM.NomeExtendido</td>
                            <td>@item.QUANTIDADE.GetValueOrDefault().ToString("N3") @item.PRODUTODETALHEITEM.PRODUTOS.UNIDADECOMERCIALIZACAO1.SIGLA</td>
                        </tr>
                    }
                </table>
            }
            else
            {
                <p>Ficha técnica não cadastrada.</p>
            }
        </div>
    </div>

    <div class="row">
        <div class="col-sm-12">
            <p class="text-bold">Modo de Preparo</p>
            @if (!string.IsNullOrWhiteSpace(Model.PRODUTODETALHE.PRODUTOS.MODOPREPARO))
            {
                @Html.Raw(Model.PRODUTODETALHE.PRODUTOS.MODOPREPARO.Replace("\n", "<br />"))
            }
            else
            {
                <p>Modo de preparo não cadastrado.</p>
            }
        </div>
    </div>

</div>
<div class="modal-footer">
    <button type="button" class="btn btn-lg btn-default" data-dismiss="modal"><i class="fa fa-close"></i> Fechar</button>
</div>
