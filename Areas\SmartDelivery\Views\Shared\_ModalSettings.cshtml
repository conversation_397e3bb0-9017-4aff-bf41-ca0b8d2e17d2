﻿@using Consumer.Resources.Mobile.SmartDelivery.Views;
@model RAL.Consumer.Mobile.Areas.SmartDelivery.ViewModel.SdSettingsViewModel
<!-- Modal Settings -->

@using (Html.BeginForm("settings", "app"))
{
    <div class="modal-header">
        <h5 class="modal-title">@ModalSettingsRes.Configuracoes</h5>
        <button type="button" id="btnHelp" class="btn btn-link pull-right" @*data-toggle="popover" title="Como funciona?" data-html="true"*@>
            <span class="material-icons">
                help
            </span>
        </button>
    </div>
    <div class="modal-body p-3">
        <div class="container">

            <div class="row mb-3">
                <div class="col">
                    <div class="custom-control custom-switch">
                        @Html.CheckBoxFor(m => m.Ativo, new { @class = "custom-control-input" })
                        @Html.LabelFor(m => m.Ativo, new { @class = "custom-control-label" })
                    </div>
                </div>
            </div>

            <div id="help-content" class="small alert alert-info" role="alert" style="display: none;">
                <b>@ModalSettingsRes.ComoFunciona</b>
                <p>@ModalSettingsRes.AposAtivacao</p>
                <p>@ModalSettingsRes.Status <b>@ModalSettingsRes.EmPreparo</b>@ModalSettingsRes.ImpressoAuto</p>
                <p>@ModalSettingsRes.StatusPedido<b>@ModalSettingsRes.ProntoRetirar</b>@ModalSettingsRes.ImpressaoManual</p>
                <p><strong>@ModalSettingsRes.ArtigoCompleto</strong> <a href="https://ajuda.consumer.com.br/como-funciona-o-smart-delivery-do-programa-consumer/">https://ajuda.consumer.com.br/como-funciona-o-smart-delivery-do-programa-consumer/</a></p>
            </div>

            <div class="row mb-3">
                <div class="col">
                    <div class="form-group">
                        <div class="row">
                            @Html.LabelFor(m => m.TempoLimiteEmMinutos, new { @class = "col-form-label col-auto" })
                            <div class="col-2">
                                @Html.TextBoxFor(m => m.TempoLimiteEmMinutos, new { @class = "form-control text-center", type = "tel" })
                            </div>
                            <label class="col-form-label col-auto">@ModalSettingsRes.Minutos</label>
                        </div>
                        @Html.ValidationMessageFor(m => m.TempoLimiteEmMinutos, "", new { @class = "text-danger small" })
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col">
                    <div class="custom-control custom-switch">
                        @Html.CheckBoxFor(m => m.AutoAgrupamentoAtivo, new { @class = "custom-control-input" })
                        @Html.LabelFor(m => m.AutoAgrupamentoAtivo, new { @class = "custom-control-label" })
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col">
                    <div class="form-group">
                        <div class="row">
                            @Html.LabelFor(m => m.AutoAgrupamentoAposSelecionar, new { @class = "col-form-label col-auto" })
                            <div class="col-2">
                                @Html.TextBoxFor(m => m.AutoAgrupamentoAposSelecionar, new { @class = "form-control text-center", type = "tel" })
                            </div>
                            <label class="col-form-label col-auto">@ModalSettingsRes.Pedidos</label>
                        </div>
                        @Html.ValidationMessageFor(m => m.AutoAgrupamentoAposSelecionar, "", new { @class = "text-danger small" })
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col">
                    <a class="mb-2 d-block" data-toggle="collapse" href="#collapseSugestoes" role="button" aria-expanded="false" aria-controls="collapseSugestoes">
                        @ModalSettingsRes.SugestaoAgrup <small class="badge badge-primary font-weight-normal">Beta</small>
                    </a>
                    <div class="collapse small" id="collapseSugestoes">
                        <div class="form-group">
                            @Html.LabelFor(m => m.GrupoSugerirPor, new { @class = "d-block col-form-label" })
                            <div class="btn-group btn-group-sm btn-group-toggle" data-toggle="buttons">
                                <label class="btn btn-secondary @(Model.GrupoSugerirPor == Consumer.Services.SmartDelivery.GrupoSugerirPorOptions.DistanciaEntrePedidos ? "active" : "")">
                                    <input type="radio" name="GrupoSugerirPor" autocomplete="off" value="@Consumer.Services.SmartDelivery.GrupoSugerirPorOptions.DistanciaEntrePedidos" @(Model.GrupoSugerirPor == Consumer.Services.SmartDelivery.GrupoSugerirPorOptions.DistanciaEntrePedidos ? " checked" : "")> @ModalSettingsRes.Proximos
                                </label>
                                <label class="btn btn-secondary @(Model.GrupoSugerirPor == Consumer.Services.SmartDelivery.GrupoSugerirPorOptions.OrdemChegada ? "active" : "")">
                                    <input type="radio" name="GrupoSugerirPor" autocomplete="off" value="@Consumer.Services.SmartDelivery.GrupoSugerirPorOptions.OrdemChegada" @(Model.GrupoSugerirPor == Consumer.Services.SmartDelivery.GrupoSugerirPorOptions.OrdemChegada ? " checked" : "")> @ModalSettingsRes.OrdemChegada
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="row">
                                @Html.LabelFor(m => m.GrupoDistanciaMaximaEmMetros, new { @class = "col-form-label col-auto" })
                                <div class="col-2">
                                    @Html.TextBoxFor(m => m.GrupoDistanciaMaximaEmMetros, new { @class = "form-control form-control-sm text-center", type = "tel" })
                                </div>
                                <label class="col-form-label col-auto">@ModalSettingsRes.Metros</label>
                            </div>
                            @Html.ValidationMessageFor(m => m.GrupoDistanciaMaximaEmMetros, "", new { @class = "text-danger small" })
                        </div>
                        <div class="form-group">
                            <div class="row">
                                @Html.LabelFor(m => m.GrupoDistanciaUnificarEmMetros, new { @class = "col-form-label col-auto" })
                                <div class="col-2">
                                    @Html.TextBoxFor(m => m.GrupoDistanciaUnificarEmMetros, new { @class = "form-control form-control-sm text-center", type = "tel" })
                                </div>
                                <label class="col-form-label col-auto">@ModalSettingsRes.Metros</label>
                            </div>
                            @Html.ValidationMessageFor(m => m.GrupoDistanciaUnificarEmMetros, "", new { @class = "text-danger small" })
                        </div>
                        <div class="form-group">
                            <div class="row">
                                @Html.LabelFor(m => m.GrupoQtdPedidosPadrao, new { @class = "col-form-label col-auto" })
                                <div class="col-2">
                                    @Html.TextBoxFor(m => m.GrupoQtdPedidosPadrao, new { @class = "form-control form-control-sm text-center", type = "tel" })
                                </div>
                                <label class="col-form-label col-auto">@ModalSettingsRes.Pedidos</label>
                            </div>
                            @Html.ValidationMessageFor(m => m.GrupoQtdPedidosPadrao, "", new { @class = "text-danger small" })
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col">
                    <a class="mb-2 d-block" data-toggle="collapse" href="#collapseMore" role="button" aria-expanded="false" aria-controls="collapseExample">
                        @ModalSettingsRes.OutrasConfig
                    </a>
                    <div class="collapse small" id="collapseMore">
                        <div class="form-group">
                            @Html.LabelFor(m => m.PinSize, new { @class = "d-block col-form-label" })
                            <div class="btn-group btn-group-sm btn-group-toggle" data-toggle="buttons">
                                <label class="btn btn-secondary @(Model.PinSize == "sm" ? "active" : "")">
                                    <input type="radio" name="pinSize" autocomplete="off" value="sm" @(Model.PinSize == "sm" ? " checked" : "")>@ModalSettingsRes.Pequeno
                                </label>
                                <label class="btn btn-secondary @(Model.PinSize == "md" ? "active" : "")">
                                    <input type="radio" name="pinSize" autocomplete="off" value="md" @(Model.PinSize == "md" ? " checked" : "")>@ModalSettingsRes.Medio
                                </label>
                                <label class="btn btn-secondary @(Model.PinSize == "lg" ? "active" : "")">
                                    <input type="radio" name="pinSize" autocomplete="off" value="lg" @(Model.PinSize == "lg" ? " checked" : "")>@ModalSettingsRes.Grande
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="row">
                                @Html.LabelFor(m => m.TempoAtualizacaoEmSegundos, new { @class = "col-form-label col-auto" })
                                <div class="col-2">
                                    @Html.TextBoxFor(m => m.TempoAtualizacaoEmSegundos, new { @class = "form-control form-control-sm text-center", type = "tel" })
                                </div>
                                <label class="col-form-label col-auto">@ModalSettingsRes.Segundos</label>
                            </div>
                            @Html.ValidationMessageFor(m => m.TempoAtualizacaoEmSegundos, "", new { @class = "text-danger small" })
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer justify-content-center mb-3">
        @if (Model.Ativo)
        {
        <button type="button" class="btn btn-secondary col-5" data-dismiss="modal">@ModalSettingsRes.Voltar</button>
        }
    <button type="submit" class="btn btn-dark col-5">@ModalSettingsRes.Salvar</button>
    </div>
}
