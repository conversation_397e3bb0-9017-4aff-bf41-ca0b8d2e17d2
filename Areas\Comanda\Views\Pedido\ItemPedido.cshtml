﻿@using Consumer.Resources.Mobile.Comanda.Views.Pedido
@model RAL.Consumer.Mobile.Models.ItemPedidoViewModels

@{
    ViewBag.Title = @ItemPedidoRes.Opcionais;
}

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true)

    <h2>
        @if (Model.Quantidade > 0)
        {
            @Model.Quantidade.GetValueOrDefault().ToString("0.##x")
        }
        @Model.NomeExtendido
    </h2>
    @Html.HiddenFor(model => model.CodigoItem);
    @Html.HiddenFor(model => model.CodigoItemPai);
    @Html.HiddenFor(model => model.CodigoProdutoDetalhe);
    @Html.HiddenFor(model => model.CodigoEtiqueta);
    @Html.HiddenFor(model => model.Origem);

    if (Model.CodigoProdutoTipo == 4)
    {
        <div class="alert alert-warning">
            <i class="glyphicon glyphicon-warning-sign"></i> @ItemPedidoRes.SelecioneItemCombo
        </div>
    }
    else
    {
        <h4>Observações</h4>
        if (Model.TodasObservacoes.Count() == 0)
        {
            <div class="alert alert-warning">
                <i class="glyphicon glyphicon-warning-sign"></i> @ItemPedidoRes.NaoHaObs
            </div>
        }
        <ul class="list-group">
            @foreach (var g in Model.TodasObservacoes)
            {
                <li class="list-group-item list-group-item-warning">
                    <input type="checkbox"
                           name="SelectedObservacoes"
                           value="@g.Chave" 
                           id="<EMAIL>"
                           class="pull-right" @(Model.SelectedObservacoes != null && Model.SelectedObservacoes.Contains(g.Chave) ? " checked=checked" : string.Empty) />
                    <label for="<EMAIL>" style="width: 100%">
                        @g.Descricao
                    </label>
                </li>
            }
            <li class="list-group-item list-group-item-warning">
                @Html.TextBoxFor(m => m.ObservacoesAdicionais, new { @class = "form-control", placeholder = @ItemPedidoRes.ObsAdicionais })
                <!--<input type="text" name="ObservacoesAdicionais" class="form-control" placeholder="Obs. adicionais. Coloque &quot;,&quot; para sair um item por linha." />-->
            </li>
        </ul>

        <h4>Complementos</h4>
        if (Model.TodosComplementos.Count() == 0)
        {
            <div class="alert alert-warning">
                <i class="glyphicon glyphicon-warning-sign"></i> @ItemPedidoRes.NaoHaCompl
            </div>
        }

        <ul class="list-group">
            @for (int i = 0; i < Model.TodosComplementos.Count; i++)
            {
                var g = Model.TodosComplementos;
                <li class="list-group-item list-group-item-success clearfix">
                    <input type="hidden" name="SelectedComplementos[@i].Codigo" value="@g[i].Codigo" />
                    <label style="line-height: 35px">
                        @g[i].Descricao (@g[i].Valor.ToString("C"))
                    </label>
                    <div class="pull-right" style="width: 130px">
                        <div class="input-group">
                            <span class="input-group-btn">
                                <button type="button" class="btn btn-default btn-minus">
                                    <span class="fa fa-minus text-danger"></span>
                                </button>
                            </span>
                            <input type="number" class="form-control text-center" 
                                   name="SelectedComplementos[@i].Qtd" 
                                   id="com-@g[i].Codigo" 
                                   @(Model.SelectedComplementos != null ? " value=" + Model.SelectedComplementos.Where(c => c.Codigo == g[i].Codigo).Select(c => c.Qtd).FirstOrDefault() : "value=0") data-val-range-min="0" data-val-range-max="99">
                            <span class="input-group-btn">
                                <button type="button" class="btn btn-default btn-plus">
                                    <span class="fa fa-plus text-success"></span>
                                </button>
                            </span>
                        </div>
                    </div>
                </li>
            }
        </ul>
    }

    <nav id="actions" class="navbar navbar-default navbar-fixed-bottom">
        <div id="item-pedido-actions" class="container-fluid">
            <div class="col-xs-6">
                <a id="btn-voltar" class="btn btn-lg btn-default btn-block navbar-btn" title="@ItemPedidoRes.Voltar"><i class="glyphicon glyphicon-circle-arrow-left"></i> <span class="hidden-xs">@ItemPedidoRes.Voltar</span><small class="visible-xs">@ItemPedidoRes.Voltar</small></a>
            </div>
            <div class="col-xs-6">
                <button class="btn btn-lg btn-success btn-block navbar-btn" title="@ItemPedidoRes.Ok"><i class="glyphicon glyphicon-check"></i> <span class="hidden-xs">@ItemPedidoRes.Ok</span><small class="visible-xs">@ItemPedidoRes.Ok</small></button>
            </div>
        </div>
    </nav>
}
