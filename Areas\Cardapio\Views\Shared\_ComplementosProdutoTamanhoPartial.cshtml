﻿@using Consumer.Resources.Mobile.Cardapio.Views.Shared;
@model List<RAL.Consumer.Mobile.Models.ProdutoViewModel>

@if (Model.Count() == 0)
{
    <div class="alert alert-warning">
        <i class="glyphicon glyphicon-warning-sign"></i> @ComplementosProdutoTamanhoPartialRes.ComplCad
    </div>
}
else
{
    for (int i = 0; i < Model.Count(); i++)
    {
        var g = Model;
        <div class="checkbox c-checkbox clearfix mb-lg">
            <label for="com1-@g[i].Codigo" class="meioameio hide">
                <input type="checkbox" name="SelectedComplementosParte[0][@i].Codigo" value="@g[i].Codigo" id="com1-@g[i].Codigo" data-precovenda="@g[i].Valor.ToString("N", System.Globalization.CultureInfo.InvariantCulture)">
                <span class="fa fa-check"></span>
            </label>
            <label for="com-@g[i].Codigo">
                <input type="checkbox" name="SelectedComplementos[@i].Codigo" value="@g[i].Codigo" id="com-@g[i].Codigo" data-precovenda="@g[i].Valor.ToString("N", System.Globalization.CultureInfo.InvariantCulture)">
                <span class="fa fa-check"></span>
            </label>
            <label for="com2-@g[i].Codigo" class="meioameio hide">
                <input type="checkbox" name="SelectedComplementosParte[1][@i].Codigo" value="@g[i].Codigo" id="com2-@g[i].Codigo" data-precovenda="@g[i].Valor.ToString("N", System.Globalization.CultureInfo.InvariantCulture)">
                <span class="fa fa-check"></span>
            </label>
            <i>@g[i].Descricao</i><i class="pull-right">+@g[i].Valor.ToString("C")</i>
        </div>
        <input type="hidden" name="SelectedComplementos[@i].Qtd" value="1" />
        <input type="hidden" name="SelectedComplementosParte[0][@i].Qtd" value="1" />
        <input type="hidden" name="SelectedComplementosParte[1][@i].Qtd" value="1" />
    }
}