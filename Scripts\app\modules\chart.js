// Start Bootstrap JS
// ----------------------------------- 

(function(window, document, $, undefined){

  $(function(){

    if ( typeof Chart === 'undefined' ) return;

    // random values for demo
    var rFactor = function(){ return Math.round(Math.random()*100); };


  // Line chart
  // ----------------------------------- 

    var lineData = {
        labels : ['January','February','March','April','May','June','July'],
        datasets : [
          {
            label: 'My First dataset',
            fillColor : 'rgba(114,102,186,0.2)',
            strokeColor : 'rgba(114,102,186,1)',
            pointColor : 'rgba(114,102,186,1)',
            pointStrokeColor : '#fff',
            pointHighlightFill : '#fff',
            pointHighlightStroke : 'rgba(114,102,186,1)',
            data : [rFactor(),rFactor(),rFactor(),rFactor(),rFactor(),rFactor(),rFactor()]
          },
          {
            label: 'My Second dataset',
            fillColor : 'rgba(35,183,229,0.2)',
            strokeColor : 'rgba(35,183,229,1)',
            pointColor : 'rgba(35,183,229,1)',
            pointStrokeColor : '#fff',
            pointHighlightFill : '#fff',
            pointHighlightStroke : 'rgba(35,183,229,1)',
            data : [rFactor(),rFactor(),rFactor(),rFactor(),rFactor(),rFactor(),rFactor()]
          }
        ]
      };


    var lineOptions = {
      scaleShowGridLines : true,
      scaleGridLineColor : 'rgba(0,0,0,.05)',
      scaleGridLineWidth : 1,
      bezierCurve : true,
      bezierCurveTension : 0.4,
      pointDot : true,
      pointDotRadius : 4,
      pointDotStrokeWidth : 1,
      pointHitDetectionRadius : 20,
      datasetStroke : true,
      datasetStrokeWidth : 2,
      datasetFill: true,
      responsive: true
    };

    var linectx = document.getElementById("chartjs-linechart").getContext("2d");
    var lineChart = new Chart(linectx).Line(lineData, lineOptions);

  // Bar chart
  // ----------------------------------- 

    var barData = {
        labels : ['January','February','March','April','May','June','July'],
        datasets : [
          {
            fillColor : '#23b7e5',
            strokeColor : '#23b7e5',
            highlightFill: '#23b7e5',
            highlightStroke: '#23b7e5',
            data : [rFactor(),rFactor(),rFactor(),rFactor(),rFactor(),rFactor(),rFactor()]
          },
          {
            fillColor : '#5d9cec',
            strokeColor : '#5d9cec',
            highlightFill : '#5d9cec',
            highlightStroke : '#5d9cec',
            data : [rFactor(),rFactor(),rFactor(),rFactor(),rFactor(),rFactor(),rFactor()]
          }
        ]
    };
    
    var barOptions = {
      scaleBeginAtZero : true,
      scaleShowGridLines : true,
      scaleGridLineColor : 'rgba(0,0,0,.05)',
      scaleGridLineWidth : 1,
      barShowStroke : true,
      barStrokeWidth : 2,
      barValueSpacing : 5,
      barDatasetSpacing : 1,
      responsive: true
    };

    var barctx = document.getElementById("chartjs-barchart").getContext("2d");
    var barChart = new Chart(barctx).Bar(barData, barOptions);

  //  Doughnut chart
  // ----------------------------------- 
    
    var doughnutData = [
          {
            value: 300,
            color: '#7266ba',
            highlight: '#7266ba',
            label: 'Purple'
          },
          {
            value: 50,
            color: '#23b7e5',
            highlight: '#23b7e5',
            label: 'Info'
          },
          {
            value: 100,
            color: '#fad732',
            highlight: '#fad732',
            label: 'Yellow'
          }
        ];

    var doughnutOptions = {
      segmentShowStroke : true,
      segmentStrokeColor : '#fff',
      segmentStrokeWidth : 2,
      percentageInnerCutout : 85,
      animationSteps : 100,
      animationEasing : 'easeOutBounce',
      animateRotate : true,
      animateScale : false,
      responsive: true
    };

    var doughnutctx = document.getElementById("chartjs-doughnutchart").getContext("2d");
    var doughnutChart = new Chart(doughnutctx).Doughnut(doughnutData, doughnutOptions);

  // Pie chart
  // ----------------------------------- 

    var pieData =[
          {
            value: 300,
            color: '#7266ba',
            highlight: '#7266ba',
            label: 'Purple'
          },
          {
            value: 40,
            color: '#fad732',
            highlight: '#fad732',
            label: 'Yellow'
          },
          {
            value: 120,
            color: '#23b7e5',
            highlight: '#23b7e5',
            label: 'Info'
          }
        ];

    var pieOptions = {
      segmentShowStroke : true,
      segmentStrokeColor : '#fff',
      segmentStrokeWidth : 2,
      percentageInnerCutout : 0, // Setting this to zero convert a doughnut into a Pie
      animationSteps : 100,
      animationEasing : 'easeOutBounce',
      animateRotate : true,
      animateScale : false,
      responsive: true
    };

    var piectx = document.getElementById("chartjs-piechart").getContext("2d");
    var pieChart = new Chart(piectx).Pie(pieData, pieOptions);

  // Polar chart
  // ----------------------------------- 
    
    var polarData = [
          {
            value: 300,
            color: '#f532e5',
            highlight: '#f532e5',
            label: 'Red'
          },
          {
            value: 50,
            color: '#7266ba',
            highlight: '#7266ba',
            label: 'Green'
          },
          {
            value: 100,
            color: '#f532e5',
            highlight: '#f532e5',
            label: 'Yellow'
          },
          {
            value: 140,
            color: '#7266ba',
            highlight: '#7266ba',
            label: 'Grey'
          },
        ];

    var polarOptions = {
      scaleShowLabelBackdrop : true,
      scaleBackdropColor : 'rgba(255,255,255,0.75)',
      scaleBeginAtZero : true,
      scaleBackdropPaddingY : 1,
      scaleBackdropPaddingX : 1,
      scaleShowLine : true,
      segmentShowStroke : true,
      segmentStrokeColor : '#fff',
      segmentStrokeWidth : 2,
      animationSteps : 100,
      animationEasing : 'easeOutBounce',
      animateRotate : true,
      animateScale : false,
      responsive: true
    };

    var polarctx = document.getElementById("chartjs-polarchart").getContext("2d");
    var polarChart = new Chart(polarctx).PolarArea(polarData, polarOptions);

  // Radar chart
  // ----------------------------------- 

    var radarData = {
      labels: ['Eating', 'Drinking', 'Sleeping', 'Designing', 'Coding', 'Cycling', 'Running'],
      datasets: [
        {
          label: 'My First dataset',
          fillColor: 'rgba(114,102,186,0.2)',
          strokeColor: 'rgba(114,102,186,1)',
          pointColor: 'rgba(114,102,186,1)',
          pointStrokeColor: '#fff',
          pointHighlightFill: '#fff',
          pointHighlightStroke: 'rgba(114,102,186,1)',
          data: [65,59,90,81,56,55,40]
        },
        {
          label: 'My Second dataset',
          fillColor: 'rgba(151,187,205,0.2)',
          strokeColor: 'rgba(151,187,205,1)',
          pointColor: 'rgba(151,187,205,1)',
          pointStrokeColor: '#fff',
          pointHighlightFill: '#fff',
          pointHighlightStroke: 'rgba(151,187,205,1)',
          data: [28,48,40,19,96,27,100]
        }
      ]
    };

    var radarOptions = {
      scaleShowLine : true,
      angleShowLineOut : true,
      scaleShowLabels : false,
      scaleBeginAtZero : true,
      angleLineColor : 'rgba(0,0,0,.1)',
      angleLineWidth : 1,
      pointLabelFontFamily : "'Arial'",
      pointLabelFontStyle : 'bold',
      pointLabelFontSize : 10,
      pointLabelFontColor : '#565656',
      pointDot : true,
      pointDotRadius : 3,
      pointDotStrokeWidth : 1,
      pointHitDetectionRadius : 20,
      datasetStroke : true,
      datasetStrokeWidth : 2,
      datasetFill : true,
      responsive: true
    };

    var radarctx = document.getElementById("chartjs-radarchart").getContext("2d");
    var radarChart = new Chart(radarctx).Radar(radarData, radarOptions);

  });

})(window, document, window.jQuery);
