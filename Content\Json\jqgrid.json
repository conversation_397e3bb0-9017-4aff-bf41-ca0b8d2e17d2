{"rows": [{"CategoryName": "Beverages", "ProductName": "<PERSON><PERSON> Stout", "Country": "UK", "Price": "1008.0000", "Quantity": "65"}, {"CategoryName": "Beverages", "ProductName": "Laughing Lumberjack Lager", "Country": "USA", "Price": "140.0000", "Quantity": "10"}, {"CategoryName": "Beverages", "ProductName": "<PERSON><PERSON>lik<PERSON><PERSON><PERSON>", "Country": "USA", "Price": "2160.0000", "Quantity": "120"}, {"CategoryName": "Beverages", "ProductName": "Guaraná Fantástica", "Country": "USA", "Price": "436.5000", "Quantity": "97"}, {"CategoryName": "Beverages", "ProductName": "Ipoh Coffee", "Country": "UK", "Price": "1656.0000", "Quantity": "36"}, {"CategoryName": "Beverages", "ProductName": "<PERSON>", "Country": "UK", "Price": "342.0000", "Quantity": "20"}, {"CategoryName": "Beverages", "ProductName": "Chartreuse verte", "Country": "USA", "Price": "648.0000", "Quantity": "42"}, {"CategoryName": "Beverages", "ProductName": "Ipoh Coffee", "Country": "USA", "Price": "1656.0000", "Quantity": "39"}, {"CategoryName": "Beverages", "ProductName": "<PERSON><PERSON>", "Country": "UK", "Price": "1314.0000", "Quantity": "73"}, {"CategoryName": "Beverages", "ProductName": "<PERSON>", "Country": "USA", "Price": "5168.0000", "Quantity": "294"}, {"CategoryName": "Beverages", "ProductName": "<PERSON><PERSON> Stout", "Country": "USA", "Price": "2772.0000", "Quantity": "174"}, {"CategoryName": "Beverages", "ProductName": "Côte de Blaye", "Country": "USA", "Price": "42160.0000", "Quantity": "170"}, {"CategoryName": "Beverages", "ProductName": "Outback Lager", "Country": "USA", "Price": "567.0000", "Quantity": "42"}, {"CategoryName": "Beverages", "ProductName": "Sasquatch Ale", "Country": "USA", "Price": "1610.0000", "Quantity": "122"}, {"CategoryName": "Beverages", "ProductName": "<PERSON><PERSON>", "Country": "USA", "Price": "3103.2000", "Quantity": "180"}, {"CategoryName": "Beverages", "ProductName": "Chartreuse verte", "Country": "UK", "Price": "288.0000", "Quantity": "20"}, {"CategoryName": "Beverages", "ProductName": "Guaraná Fantástica", "Country": "UK", "Price": "288.0000", "Quantity": "80"}, {"CategoryName": "Beverages", "ProductName": "Sasquatch Ale", "Country": "UK", "Price": "156.8000", "Quantity": "14"}, {"CategoryName": "Beverages", "ProductName": "<PERSON><PERSON>lik<PERSON><PERSON><PERSON>", "Country": "UK", "Price": "266.4000", "Quantity": "16"}, {"CategoryName": "Beverages", "ProductName": "Rhönbräu Klosterbier", "Country": "USA", "Price": "2292.4500", "Quantity": "297"}, {"CategoryName": "Beverages", "ProductName": "Outback Lager", "Country": "UK", "Price": "900.0000", "Quantity": "66"}, {"CategoryName": "Condiments", "ProductName": "<PERSON><PERSON>", "Country": "USA", "Price": "3505.5000", "Quantity": "141"}, {"CategoryName": "Condiments", "ProductName": "Original Frankfurter grüne Soße", "Country": "USA", "Price": "988.0000", "Quantity": "82"}, {"CategoryName": "Condiments", "ProductName": "Louisiana Fiery Hot Pepper Sauce", "Country": "USA", "Price": "904.3500", "Quantity": "47"}, {"CategoryName": "Condiments", "ProductName": "Chef <PERSON>'s Gumbo Mix", "Country": "UK", "Price": "1281.0000", "Quantity": "60"}, {"CategoryName": "Condiments", "ProductName": "Louisiana Hot Spiced Okra", "Country": "UK", "Price": "408.0000", "Quantity": "24"}, {"CategoryName": "Condiments", "ProductName": "Chef <PERSON>'s <PERSON><PERSON><PERSON> Seasoning", "Country": "UK", "Price": "550.0000", "Quantity": "25"}, {"CategoryName": "Condiments", "ProductName": "Chef <PERSON>'s Gumbo Mix", "Country": "USA", "Price": "1783.9000", "Quantity": "86"}, {"CategoryName": "Condiments", "ProductName": "Louisiana Fiery Hot Pepper Sauce", "Country": "UK", "Price": "442.0500", "Quantity": "21"}, {"CategoryName": "Condiments", "ProductName": "Chef <PERSON>'s <PERSON><PERSON><PERSON> Seasoning", "Country": "USA", "Price": "1188.0000", "Quantity": "61"}, {"CategoryName": "Condiments", "ProductName": "<PERSON><PERSON><PERSON>", "Country": "USA", "Price": "40.0000", "Quantity": "4"}, {"CategoryName": "Condiments", "ProductName": "Grandma's <PERSON><PERSON><PERSON> Spread", "Country": "USA", "Price": "25.0000", "Quantity": "1"}, {"CategoryName": "Condiments", "ProductName": "Northwoods Cranberry Sauce", "Country": "USA", "Price": "2320.0000", "Quantity": "72"}, {"CategoryName": "Condiments", "ProductName": "Louisiana Hot Spiced Okra", "Country": "USA", "Price": "17.0000", "Quantity": "1"}, {"CategoryName": "Condiments", "ProductName": "Vegie-spread", "Country": "USA", "Price": "4300.6000", "Quantity": "114"}, {"CategoryName": "Condiments", "ProductName": "Gula Malacca", "Country": "USA", "Price": "2553.5000", "Quantity": "152"}, {"CategoryName": "Condiments", "ProductName": "<PERSON><PERSON><PERSON>", "Country": "UK", "Price": "240.0000", "Quantity": "30"}, {"CategoryName": "Condiments", "ProductName": "Northwoods Cranberry Sauce", "Country": "UK", "Price": "800.0000", "Quantity": "20"}, {"CategoryName": "Condiments", "ProductName": "<PERSON><PERSON>", "Country": "UK", "Price": "855.0000", "Quantity": "30"}, {"CategoryName": "Condiments", "ProductName": "<PERSON><PERSON>", "Country": "USA", "Price": "930.0000", "Quantity": "60"}, {"CategoryName": "Confections", "ProductName": "Max<PERSON><PERSON>", "Country": "UK", "Price": "680.0000", "Quantity": "40"}, {"CategoryName": "Confections", "ProductName": "Scottish Longbreads", "Country": "USA", "Price": "3060.0000", "Quantity": "247"}, {"CategoryName": "Confections", "ProductName": "Tarte au sucre", "Country": "UK", "Price": "591.6000", "Quantity": "12"}, {"CategoryName": "Confections", "ProductName": "<PERSON><PERSON><PERSON>", "Country": "USA", "Price": "3950.0000", "Quantity": "100"}, {"CategoryName": "Confections", "ProductName": "<PERSON><PERSON><PERSON><PERSON>", "Country": "USA", "Price": "4739.5000", "Quantity": "295"}, {"CategoryName": "Confections", "ProductName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Country": "UK", "Price": "374.7600", "Quantity": "12"}, {"CategoryName": "Confections", "ProductName": "NuNuCa Nuß-Nougat-Creme", "Country": "USA", "Price": "568.4000", "Quantity": "42"}, {"CategoryName": "Confections", "ProductName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Country": "USA", "Price": "3397.3500", "Quantity": "125"}, {"CategoryName": "Confections", "ProductName": "<PERSON><PERSON><PERSON> koeken", "Country": "USA", "Price": "627.0000", "Quantity": "66"}, {"CategoryName": "Confections", "ProductName": "Max<PERSON><PERSON>", "Country": "USA", "Price": "1260.0000", "Quantity": "63"}, {"CategoryName": "Confections", "ProductName": "Teatime Chocolate Biscuits", "Country": "USA", "Price": "1278.2000", "Quantity": "141"}, {"CategoryName": "Confections", "ProductName": "<PERSON> <PERSON>'s <PERSON><PERSON>", "Country": "USA", "Price": "1390.0000", "Quantity": "139"}, {"CategoryName": "Confections", "ProductName": "Tarte au sucre", "Country": "USA", "Price": "16953.8000", "Quantity": "371"}, {"CategoryName": "Confections", "ProductName": "<PERSON><PERSON><PERSON><PERSON>", "Country": "UK", "Price": "778.4000", "Quantity": "56"}, {"CategoryName": "Confections", "ProductName": "Sir <PERSON>'s Marmalade", "Country": "UK", "Price": "1215.0000", "Quantity": "15"}, {"CategoryName": "Confections", "ProductName": "Sir <PERSON>'s Marmalade", "Country": "USA", "Price": "2170.8000", "Quantity": "28"}, {"CategoryName": "Confections", "ProductName": "Teatime Chocolate Biscuits", "Country": "UK", "Price": "46.0000", "Quantity": "5"}, {"CategoryName": "Confections", "ProductName": "Scottish Longbreads", "Country": "UK", "Price": "250.0000", "Quantity": "20"}, {"CategoryName": "Confections", "ProductName": "<PERSON> <PERSON>'s <PERSON><PERSON>", "Country": "UK", "Price": "262.0000", "Quantity": "29"}, {"CategoryName": "Dairy Products", "ProductName": "Mozza<PERSON> di <PERSON>", "Country": "UK", "Price": "1356.8000", "Quantity": "41"}, {"CategoryName": "Dairy Products", "ProductName": "Geitost", "Country": "UK", "Price": "136.5000", "Quantity": "57"}, {"CategoryName": "Dairy Products", "ProductName": "<PERSON><PERSON><PERSON>", "Country": "UK", "Price": "770.0000", "Quantity": "14"}, {"CategoryName": "Dairy Products", "ProductName": "Queso Manchego La Pastora", "Country": "USA", "Price": "4636.0000", "Quantity": "122"}, {"CategoryName": "Dairy Products", "ProductName": "Gudbrandsdalsost", "Country": "UK", "Price": "972.0000", "Quantity": "33"}, {"CategoryName": "Dairy Products", "ProductName": "Fløtemysost", "Country": "UK", "Price": "1689.9000", "Quantity": "82"}, {"CategoryName": "Dairy Products", "ProductName": "Gorgonzola Telino", "Country": "USA", "Price": "2832.5000", "Quantity": "241"}, {"CategoryName": "Dairy Products", "ProductName": "<PERSON><PERSON>", "Country": "UK", "Price": "1365.0000", "Quantity": "65"}, {"CategoryName": "Dairy Products", "ProductName": "Ma<PERSON><PERSON>one F<PERSON>oli", "Country": "USA", "Price": "1056.0000", "Quantity": "41"}, {"CategoryName": "Dairy Products", "ProductName": "<PERSON><PERSON><PERSON>", "Country": "UK", "Price": "4590.0000", "Quantity": "151"}, {"CategoryName": "Dairy Products", "ProductName": "<PERSON><PERSON>", "Country": "USA", "Price": "1113.0000", "Quantity": "53"}, {"CategoryName": "Dairy Products", "ProductName": "Geitost", "Country": "USA", "Price": "500.5000", "Quantity": "221"}, {"CategoryName": "Dairy Products", "ProductName": "Mozza<PERSON> di <PERSON>", "Country": "USA", "Price": "3763.6000", "Quantity": "117"}, {"CategoryName": "Dairy Products", "ProductName": "Fløtemysost", "Country": "USA", "Price": "4364.5000", "Quantity": "215"}, {"CategoryName": "Dairy Products", "ProductName": "<PERSON><PERSON><PERSON>", "Country": "USA", "Price": "14080.0000", "Quantity": "276"}, {"CategoryName": "Dairy Products", "ProductName": "Gudbrandsdalsost", "Country": "USA", "Price": "3600.0000", "Quantity": "100"}, {"CategoryName": "Dairy Products", "ProductName": "Gorgonzola Telino", "Country": "UK", "Price": "700.0000", "Quantity": "70"}, {"CategoryName": "Dairy Products", "ProductName": "<PERSON><PERSON><PERSON>", "Country": "USA", "Price": "5603.2000", "Quantity": "173"}, {"CategoryName": "Dairy Products", "ProductName": "Ma<PERSON><PERSON>one F<PERSON>oli", "Country": "UK", "Price": "768.0000", "Quantity": "24"}, {"CategoryName": "Grains/Cereals", "ProductName": "<PERSON><PERSON>", "Country": "UK", "Price": "117.0000", "Quantity": "6"}, {"CategoryName": "Grains/Cereals", "ProductName": "Gustaf's Knäckebröd", "Country": "UK", "Price": "642.6000", "Quantity": "33"}, {"CategoryName": "Grains/Cereals", "ProductName": "Filo Mix", "Country": "UK", "Price": "154.0000", "Quantity": "26"}, {"CategoryName": "Grains/Cereals", "ProductName": "<PERSON><PERSON>b<PERSON><PERSON><PERSON>", "Country": "UK", "Price": "736.2000", "Quantity": "86"}, {"CategoryName": "Grains/Cereals", "ProductName": "<PERSON><PERSON>", "Country": "USA", "Price": "1072.5000", "Quantity": "65"}, {"CategoryName": "Grains/Cereals", "ProductName": "Filo Mix", "Country": "USA", "Price": "252.0000", "Quantity": "36"}, {"CategoryName": "Grains/Cereals", "ProductName": "Singaporean Hokkien Fried <PERSON>", "Country": "USA", "Price": "1246.0000", "Quantity": "89"}, {"CategoryName": "Grains/Cereals", "ProductName": "Gnocchi di nonna Alice", "Country": "USA", "Price": "12980.8000", "Quantity": "366"}, {"CategoryName": "Grains/Cereals", "ProductName": "<PERSON><PERSON>b<PERSON><PERSON><PERSON>", "Country": "USA", "Price": "414.0000", "Quantity": "46"}, {"CategoryName": "Grains/Cereals", "ProductName": "Gustaf's Knäckebröd", "Country": "USA", "Price": "1008.0000", "Quantity": "48"}, {"CategoryName": "Grains/Cereals", "ProductName": "Wimmers gute Semmelknödel", "Country": "USA", "Price": "3458.0000", "Quantity": "110"}, {"CategoryName": "Grains/Cereals", "ProductName": "Wimmers gute Semmelknödel", "Country": "UK", "Price": "239.4000", "Quantity": "9"}, {"CategoryName": "Grains/Cereals", "ProductName": "Gnocchi di nonna Alice", "Country": "UK", "Price": "1178.0000", "Quantity": "35"}, {"CategoryName": "Meat/Poultry", "ProductName": "Perth Pasties", "Country": "USA", "Price": "5916.0000", "Quantity": "186"}, {"CategoryName": "Meat/Poultry", "ProductName": "<PERSON><PERSON>", "Country": "UK", "Price": "291.0000", "Quantity": "3"}, {"CategoryName": "Meat/Poultry", "ProductName": "Thüringer Rostbratwurst", "Country": "UK", "Price": "2079.0000", "Quantity": "21"}, {"CategoryName": "Meat/Poultry", "ProductName": "<PERSON><PERSON><PERSON> chin<PERSON>", "Country": "USA", "Price": "4008.0000", "Quantity": "191"}, {"CategoryName": "Meat/Poultry", "ProductName": "<PERSON><PERSON><PERSON> chin<PERSON>", "Country": "UK", "Price": "840.0000", "Quantity": "35"}, {"CategoryName": "Meat/Poultry", "ProductName": "Thüringer Rostbratwurst", "Country": "USA", "Price": "19705.1600", "Quantity": "173"}, {"CategoryName": "Meat/Poultry", "ProductName": "Tourtière", "Country": "USA", "Price": "1673.3000", "Quantity": "240"}, {"CategoryName": "Meat/Poultry", "ProductName": "<PERSON>", "Country": "USA", "Price": "13509.6000", "Quantity": "361"}, {"CategoryName": "Meat/Poultry", "ProductName": "<PERSON>", "Country": "UK", "Price": "975.0000", "Quantity": "25"}, {"CategoryName": "Meat/Poultry", "ProductName": "Tourtière", "Country": "UK", "Price": "52.1500", "Quantity": "7"}, {"CategoryName": "Meat/Poultry", "ProductName": "Perth Pasties", "Country": "UK", "Price": "1113.6000", "Quantity": "42"}, {"CategoryName": "Meat/Poultry", "ProductName": "<PERSON><PERSON>", "Country": "USA", "Price": "582.0000", "Quantity": "6"}, {"CategoryName": "Produce", "ProductName": "<PERSON><PERSON><PERSON><PERSON>", "Country": "USA", "Price": "1549.2000", "Quantity": "37"}, {"CategoryName": "Produce", "ProductName": "Manjimup Dried Apples", "Country": "USA", "Price": "3286.0000", "Quantity": "62"}, {"CategoryName": "Produce", "ProductName": "Uncle <PERSON>'s Organic Dried Pears", "Country": "USA", "Price": "3780.0000", "Quantity": "131"}, {"CategoryName": "Produce", "ProductName": "Tofu", "Country": "USA", "Price": "1850.7000", "Quantity": "91"}, {"CategoryName": "Produce", "ProductName": "<PERSON><PERSON><PERSON><PERSON>", "Country": "UK", "Price": "1822.4000", "Quantity": "44"}, {"CategoryName": "Produce", "ProductName": "Uncle <PERSON>'s Organic Dried Pears", "Country": "UK", "Price": "3840.0000", "Quantity": "134"}, {"CategoryName": "Produce", "ProductName": "Manjimup Dried Apples", "Country": "UK", "Price": "1420.4000", "Quantity": "31"}, {"CategoryName": "Seafood", "ProductName": "Kon<PERSON>", "Country": "UK", "Price": "202.8000", "Quantity": "34"}, {"CategoryName": "Seafood", "ProductName": "Spegesild", "Country": "UK", "Price": "180.0000", "Quantity": "15"}, {"CategoryName": "Seafood", "ProductName": "Jack's New England Clam Chowder", "Country": "USA", "Price": "2139.8500", "Quantity": "227"}, {"CategoryName": "Seafood", "ProductName": "Escargots de Bourgogne", "Country": "USA", "Price": "1073.2500", "Quantity": "87"}, {"CategoryName": "Seafood", "ProductName": "Nord-Ost Matjeshering", "Country": "UK", "Price": "388.3500", "Quantity": "15"}, {"CategoryName": "Seafood", "ProductName": "Ikura", "Country": "USA", "Price": "2945.0000", "Quantity": "95"}, {"CategoryName": "Seafood", "ProductName": "Spegesild", "Country": "USA", "Price": "1368.0000", "Quantity": "120"}, {"CategoryName": "Seafood", "ProductName": "<PERSON><PERSON><PERSON><PERSON> sild", "Country": "USA", "Price": "1767.0000", "Quantity": "186"}, {"CategoryName": "Seafood", "ProductName": "Gravad lax", "Country": "USA", "Price": "1560.0000", "Quantity": "60"}, {"CategoryName": "Seafood", "ProductName": "<PERSON><PERSON><PERSON>", "Country": "USA", "Price": "3515.0000", "Quantity": "185"}, {"CategoryName": "Seafood", "ProductName": "<PERSON><PERSON><PERSON><PERSON> sild", "Country": "UK", "Price": "114.0000", "Quantity": "15"}, {"CategoryName": "Seafood", "ProductName": "Kon<PERSON>", "Country": "USA", "Price": "1560.0000", "Quantity": "262"}, {"CategoryName": "Seafood", "ProductName": "<PERSON><PERSON><PERSON>", "Country": "USA", "Price": "1230.0000", "Quantity": "82"}, {"CategoryName": "Seafood", "ProductName": "Gravad lax", "Country": "UK", "Price": "468.0000", "Quantity": "18"}, {"CategoryName": "Seafood", "ProductName": "Jack's New England Clam Chowder", "Country": "UK", "Price": "211.9000", "Quantity": "26"}, {"CategoryName": "Seafood", "ProductName": "<PERSON><PERSON><PERSON>", "Country": "UK", "Price": "1140.0000", "Quantity": "60"}, {"CategoryName": "Seafood", "ProductName": "Ikura", "Country": "UK", "Price": "1116.0000", "Quantity": "36"}, {"CategoryName": "Seafood", "ProductName": "Boston Crab Meat", "Country": "USA", "Price": "1876.6000", "Quantity": "104"}, {"CategoryName": "Seafood", "ProductName": "Boston Crab Meat", "Country": "UK", "Price": "147.0000", "Quantity": "10"}, {"CategoryName": "Seafood", "ProductName": "Carnarvon Tigers", "Country": "USA", "Price": "5187.5000", "Quantity": "88"}, {"CategoryName": "Seafood", "ProductName": "Nord-Ost Matjeshering", "Country": "USA", "Price": "926.3700", "Quantity": "44"}]}