﻿@{
    var alerts = TempData.ContainsKey(Alert.TempDataKey)
                ? (List<Alert>)TempData[Alert.TempDataKey]
                : new List<Alert>();

                @*if (alerts.Any())
                    {
                        <hr />
                    }*@

    foreach (var alert in alerts)
    {
        @*var dismissableClass = alert.Dismissable ? "alert-dismissable" : null;
        <div class="alert <EMAIL> @dismissableClass">
            @if (alert.Dismissable)
            {
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
            }
            <i class="@alert.AlertIcon"></i> @Html.Raw(alert.Message)
        </div>*@
        <button type="button" data-notify="" data-onload="" data-message="&lt;i class='@alert.AlertIcon'&gt;&lt;/i&gt; @Html.Raw(alert.Message)" data-options="{ @(!string.IsNullOrWhiteSpace(alert.AlertStyle) ?  "\"status\": \""+ alert.AlertStyle +"\", \"timeout\": " +  alert.Timeout : "\"timeout\": " + alert.Timeout )}" class="hide"></button>

    }
}