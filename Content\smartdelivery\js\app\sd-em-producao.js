﻿'use strict';

let inProduction = {
    groups: [],
    getOrders: function () {
        $.post('/smartdelivery/emproducao/getorders', function (response) {
            if (response.Success) {

                inProduction.groups = response.Model;

                let totalOrders = inProduction.groups.reduce((a, b) => a + b.groupQuantityOfOrders, 0);
                $('#orderProducingQty').html(`(${totalOrders})`);

                for (let i = 0; i < inProduction.groups.length; i++) {
                    let currentGroup = inProduction.groups[i];
                    for (let j = 0; j < currentGroup.items.length; j++) {
                        let currentOrder = currentGroup.items[j];
                        currentOrder.orderStatusExpiration = getStatusExpiration(currentOrder.orderCreatedDateTime, appSettings.TempoLimiteEmMinutos);
                    }
                }

                let ordersRows = '<tr><td colspan="6" class="text-center">Nenhum pedido em produção</td></tr>';
                if (inProduction.groups.length > 0) {
                    ordersRows = MapObjectToHtmlFromList('template-grid-item-sd-order-producing', inProduction.groups);
                }
                $('#table-orders-producing tbody').html(ordersRows);

                calcTimeLapse();
            }
            else {
                if (response.ErrorMessage)
                    $.notify(`<i class="material-icons align-bottom">cancel</i> ${response.ErrorMessage}`, { timeout: 0, status: 'danger' });
                else // Erro desconhecido
                    $.notify(`<i class="material-icons align-bottom">cancel</i> Não foi possível obter os dados, por favor, recarregue a página.`, { timeout: 15, status: 'danger' });
            }

            //após obter pedidos, agenda uma nova chamada
            clearTimeout(inProduction.nextUpdateTimeout);
            inProduction.nextUpdateTimeout = setTimeout(inProduction.getOrders, appSettings.TempoAtualizacaoEmMS);
        });
    },
    undoGroup: function (e) {
        let orderId = null;
        if (e) {
            let sender = e.srcElement || e.target;
            orderId = $(sender).data('id');
        }

        swal({
            title: `Remover pedido #${orderId} do grupo?`,
            text: 'O status do pedido será alterado para Em Aberto e todos os itens serão marcados como NÃO impresso.',
            type: "warning",
            showCancelButton: true,
            confirmButtonClass: "btn-warning",
            confirmButtonText: "Confirmar",
            cancelButtonText: "Voltar",
            closeOnConfirm: true
        },
            function () {
                $.post('/smartdelivery/emproducao/undogroup', { orderId: orderId }, function (response) {
                    if (response.Success) {
                        inProduction.getOrders();
                    }
                    else {
                        $.notify(`<i class="material-icons align-bottom">cancel</i> ${response.ErrorMessage}`, { timeout: 0, status: 'danger' });
                    }
                });
            }
        );
    }
};

// essa função é chamada no sd.init.js
function initTabInProduction() {
    inProduction.getOrders();
}