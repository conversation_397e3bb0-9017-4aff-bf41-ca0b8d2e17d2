﻿@using Consumer.Resources.Mobile.Cardapio.Views.Shared;
@model RAL.Consumer.Mobile.Models.ComboItemOpcaoSelecionada

@if (Model.TodosComplementos != null && Model.TodosComplementos.Count > 0)
{
    <div class="panel panel-danger">
        <div id="headingComboProduto@(Model.Produto.Codigo)" role="tab" class="panel-heading">
            <h4 class="panel-title">
                <a data-toggle="collapse" data-parent="#accordionComboItem@(Model.ComboItemCodigo)" href="#collapseComboProduto@(Model.Produto.Codigo)" aria-expanded="false" aria-controls="collapseComboProduto@(Model.Produto.Codigo)" class="collapsed btn-block">@ComplementosObservacoesComboRes.Complementos<span class="collapse-icon pull-right"><i class="fa fa-chevron-up"></i><i class="fa fa-chevron-down"></i></span></a>
            </h4>
        </div>
        <div id="collapseComboProduto@(Model.Produto.Codigo)" role="tabpanel" aria-labelledby="headingComboProduto@(Model.Produto.Codigo)" class="panel-collapse collapse" aria-expanded="false" style="height: 0px;">
            <div class="panel-body">
                @for (int i = 0; i < Model.TodosComplementos.Count; i++)
                {
                    var g = Model.TodosComplementos;
                    <div class="checkbox c-checkbox clearfix mb-lg">

                        <label for="<EMAIL>-@g[i].Codigo">
                            <input type="checkbox" name="ComboItens[@Model.ComboItemIndex].OpcaoSelecionada.SelectedComplementos" value="@g[i].Codigo" id="<EMAIL>-@g[i].Codigo" data-precovenda="@g[i].Valor.ToString("N", System.Globalization.CultureInfo.InvariantCulture)">
                            <span class="fa fa-check"></span>
                        </label>

                        <i>@g[i].Descricao</i><i class="pull-right">+@g[i].Valor.ToString("C")</i>
                    </div>
                }
            </div>
        </div>
    </div>
}

@if (Model.TodasObservacoes != null && Model.TodasObservacoes.Count > 0)
{
    <div class="panel panel-danger">
        <div id="headingThree" role="tab" class="panel-heading">
            <h4 class="panel-title">
                <a data-toggle="collapse" data-parent="#accordion" href="#collapseThree" aria-expanded="false" aria-controls="collapseThree" class="collapsed btn-block">@ComplementosObservacoesComboRes.Observacoes<span class="collapse-icon pull-right"><i class="fa fa-chevron-up"></i><i class="fa fa-chevron-down"></i></span></a>
            </h4>
        </div>
        <div id="collapseThree" role="tabpanel" aria-labelledby="headingThree" class="panel-collapse collapse" aria-expanded="false">
            <div class="panel-body">

                @foreach (var item in Model.TodasObservacoes)
                {
                    <div class="checkbox c-checkbox clearfix mb-lg mt-lg">

                        <label for="<EMAIL>">
                            <input type="checkbox" name="SelectedObservacoes" value="@item.CODIGO" id="<EMAIL>">
                            <span class="fa fa-check"></span>
                        </label>

                        <i>@item.DESCRICAO</i>
                    </div>
                }
            </div>
        </div>
    </div>
}