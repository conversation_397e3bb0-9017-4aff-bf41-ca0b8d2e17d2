﻿@using Consumer.Resources.Mobile.Comanda.Views.Shared

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name='viewport' content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0' />
    <title>@ViewBag.Title - Mobile App</title>

    <link rel="apple-touch-icon" sizes="57x57" href="/favicons/apple-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="/favicons/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/favicons/apple-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/favicons/apple-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="/favicons/apple-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/favicons/apple-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/favicons/apple-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/favicons/apple-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/favicons/apple-icon-180x180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/favicons/android-icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="/favicons/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicons/favicon-16x16.png">
    <link rel="manifest" href="/favicons/manifest.json">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="msapplication-TileImage" content="/favicons/ms-icon-144x144.png">
    <meta name="theme-color" content="#ffffff">

    @Styles.Render("~/bundles/fontawesome")
    <link href="@Styles.Url("~/Content/bootstrap.css")" rel="stylesheet" type="text/css" id="bscss" />
    <link href="@Styles.Url("~/Content/bootstrap.xl.css")" rel="stylesheet" type="text/css" />
    @Styles.Render("~/bundles/select2Css")
    @Styles.Render("~/bundles/sweetalertcss")
    @Styles.Render("~/bundles/animatecss")
    @Styles.Render("~/bundles/comandaCss")
    @Scripts.Render("~/bundles/modernizr")
</head>
<body>
    <div class="navbar navbar-inverse navbar-fixed-top">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <span class="navbar-brand">@ViewBag.Title</span>
            </div>
            <div class="navbar-collapse collapse">
                <ul class="nav navbar-nav">
                    <li>@Html.ActionLink(LayoutRes.Mesa, "Index", "Home")</li>
                    @if (Request.Cookies.Get("PaymentMethodsLIO") == null)
                    {
                        <li>@Html.ActionLink(LayoutRes.Monitor, "Index", "Monitor", new { id = string.Empty }, new { })</li>
                        <li>@Html.ActionLink(LayoutRes.Smart, "Index", "App", new { area = "SmartDelivery" }, new { })</li>
                    }
                    <li>@*@Html.ActionLink("Configurações", "Config", "Home", new { area = string.Empty }, null)*@</li>
                </ul>
                @Html.Partial("_LoginPartial")
            </div>
        </div>
    </div>
    <div class="container-fluid body-content">
        @{ Html.RenderPartial("_Alerts"); }
        @RenderBody()
    </div>

    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/jqueryval")
    @Scripts.Render("~/bundles/bootstrap")
    @Scripts.Render("~/bundles/jquerymask")
    @Scripts.Render("~/bundles/overlay")
    @Scripts.Render("~/bundles/notify")
    @Scripts.Render("~/bundles/animo")
    @Scripts.Render("~/bundles/select2")
    @Scripts.Render("~/bundles/sweetalert")
    @Scripts.Render("~/bundles/comandaApp")

    <script>
        $(function () {
            var notificarGarcom = function (numeroMesa) {
                $.notify("<i class='fa fa-bell'></i> Mesa " + numeroMesa + " está chamando!<div class='text-center'><a href='#' class='btnAtenderMesaChamando' data-id='" + numeroMesa + "'><strong>ESTOU INDO!</strong></a></div>", { timeout: 0, group: "MesaChamando" });
                navigator.vibrate = navigator.vibrate || navigator.webkitVibrate || navigator.mozVibrate || navigator.msVibrate;
                if (navigator.vibrate) {
                    navigator.vibrate(500);
                }
            };

            @*var verificarMesaChamando = function () {
                $.notify.closeAll("MesaChamando");
                showLoading = false;
                $.get("@Url.Action("VerificarMesaChamando", "Home")", function (data) {
                    for (var item in data) {
                        notificarGarcom(data[item].numeroMesa);
                    }
                });
            };*@

            var delaymesa = 15000;
            var timerverificarmesa = setTimeout(function verificarMesaChamando() {
                $.notify.closeAll("MesaChamando");
                showLoading = false;
                $.get("/Comanda/Home/VerificarMesaChamando", function (data) {
                    for (var item in data) {
                        notificarGarcom(data[item].numeroMesa);
                    }

                    timerverificarmesa = setTimeout(verificarMesaChamando, delaymesa);
                });
            }, delaymesa);

            $(document).on('click', '.btnAtenderMesaChamando', function () {
                $.post("@Url.Action("AtenderMesaChamando", "Home")", { numeroMesa: $(this).data('id') }, function (data) {
                    $.notify("<i class='" + data.icone + "'></i> " + data.mensagem);
                });
            });

            //verificarMesaChamando();
            //setInterval(verificarMesaChamando, 15000);

            //Item pronto na cozinha
            var notificarItemPronto = function (codigoCozinha, nomeCozinha) {
                $.notify("<i class='fa fa-bell'></i> @Consumer.Resources.Mobile.Comanda.App.Shared._LayoutRes.ItensParaEntrega: <strong>" + nomeCozinha + "</strong><div class='text-center'><a href='#' class='btnItemPronto btn btn-primary' data-id='" + codigoCozinha + "'><strong>@Consumer.Resources.Mobile.Comanda.App.Shared._LayoutRes.ESTOUINDO</strong></a><br><br><a href='#' class='btnSilenciarItensProntos'><strong>@Consumer.Resources.Mobile.Comanda.App.Shared._LayoutRes.SilenciarPor5Minutos</strong></a></div>", { timeout: 0, group: "ItensProntos" });
                navigator.vibrate = navigator.vibrate || navigator.webkitVibrate || navigator.mozVibrate || navigator.msVibrate;
                if (navigator.vibrate) {
                    navigator.vibrate(500);
                }
            };

            @*var verificarItensProntos = function () {
                $.notify.closeAll("ItensProntos");
                showLoading = false;
                $.get("@Url.Action("VerificarItensProntos", "Home")", function (data) {
                    for (var item in data) {
                        notificarItemPronto(data[item].codigoCozinha, data[item].nomeCozinha);
                    }
                });
            };*@

            $(document).on('click', '.btnItemPronto', function () {
                $.post("@Url.Action("AtenderItemPronto", "Home")", { codigoCozinha: $(this).data('id') }, function (data) {
                    $.notify("<i class='" + data.icone + "'></i> " + data.mensagem);
                    if (data.codigoCozinha)
                        window.location.href = "/Comanda/Monitor?ReturnUrl=/Comanda/Monitor/Produzir/" + data.codigoCozinha + "?acao=Entregar";
                });
            });

            $(document).on('click', '.btnSilenciarItensProntos', function () {
                $.post("@Url.Action("SilenciarNotificacoesItensProntos", "Home")", function (data) {
                    $.notify("<i class='" + data.icone + "'></i> " + data.mensagem);
                });
            });

            if (window.location.href.indexOf("Monitor") == -1) {
                //verificarItensProntos();
                //setInterval(verificarItensProntos, 15000);

                var delayitens = 15000;
                var timerverificaritens = setTimeout(function verificarItensProntos() {
                    $.notify.closeAll("ItensProntos");
                    showLoading = false;
                    $.get("@Url.Action("VerificarItensProntos", "Home")", function (data) {
                        for (var item in data) {
                            notificarItemPronto(data[item].codigoCozinha, data[item].nomeCozinha);
                        }

                        timerverificaritens = setTimeout(verificarItensProntos, delayitens);
                    });
                }, delayitens);
            }
            //Fim - Item pronto na cozinha
        });
    </script>

    @RenderSection("scripts", required: false)
</body>
</html>
