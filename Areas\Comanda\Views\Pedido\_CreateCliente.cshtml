﻿@using Consumer.Resources.Mobile.Comanda.Views.Pedido
@model RAL.Consumer.Mobile.Areas.Comanda.Models.ClienteViewModel
@{
    Layout = null;
}
<div id="modalCliente" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            @using (Ajax.BeginForm("CreateCliente", "Pedido", new AjaxOptions { OnSuccess = "OnSuccessCreateCliente", OnBegin = "OnBeginCreateCliente" }))
            {
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">@CreateClienteRes.NovoCliente</h4>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        @Html.LabelFor(model => model.Nome, htmlAttributes: new { @class = "control-label" })
                        @Html.EditorFor(model => model.Nome, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.Nome, "", new { @class = "text-danger" })
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.Telefone, htmlAttributes: new { @class = "control-label" })
                        @Html.EditorFor(model => model.Telefone, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.Telefone, "", new { @class = "text-danger" })
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.Email, htmlAttributes: new { @class = "control-label" })
                        @Html.EditorFor(model => model.Email, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.Email, "", new { @class = "text-danger" })
                    </div>

                    <div class="row">
                        <div class="col-xs-8">
                            <div class="form-group">
                                @Html.LabelFor(model => model.Cep, htmlAttributes: new { @class = "control-label" })
                                @Html.EditorFor(model => model.Cep, new { htmlAttributes = new { @class = "form-control", type = "tel" } })
                                @Html.ValidationMessageFor(model => model.Cep, "", new { @class = "text-danger" })
                            </div>
                        </div>
                        <div class="col-xs-4">
                            <div class="form-group">
                                @Html.LabelFor(model => model.Numero, htmlAttributes: new { @class = "control-label" })
                                @Html.EditorFor(model => model.Numero, new { htmlAttributes = new { @class = "form-control", type = "tel" } })
                                @Html.ValidationMessageFor(model => model.Numero, "", new { @class = "text-danger" })
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-lg btn-default" data-dismiss="modal">@CreateClienteRes.Voltar</button>
                    <button type="submit" class="btn btn-lg btn-primary">@CreateClienteRes.Salvar</button>
                </div>
            }
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->