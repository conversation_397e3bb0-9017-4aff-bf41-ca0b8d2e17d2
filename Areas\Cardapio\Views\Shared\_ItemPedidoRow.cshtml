﻿@using Consumer.Resources.Mobile.Cardapio.Views.Shared;
@model RAL.Consumer.Data.Entities.ITENSPEDIDO
@{
    ViewBag.Nivel = (ViewBag.Nivel ?? -1) + 1;
    string detailId = Model.CODIGO.ToString();
    if (ViewBag.Nivel == 1)
    {
        detailId = string.Format("{0}-{1}", Model.ITEMPEDIDOPAI.CODIGO, Model.CODIGO);
    }
    else if (ViewBag.Nivel == 2)
    {
        detailId = string.Format("{0}-{1}-{2}", Model.ITEMPEDIDOPAI.ITEMPEDIDOPAI.CODIGO, Model.CODIGOPAI, Model.CODIGO);
    }
}
<tr id="detail-@detailId" class="details">
    <td class="order-item-name" @(Model.QUANTIDADE == 0 ? "colspan=4" : "") style="padding-left: @(15 * ViewBag.Nivel)px">
        <i class="fa fa-@(ViewBag.Nivel == 0 ? "chevron-right" : (Model.PRODUTODETALHE.PRODUTOS.CODIGOPRODUTOTIPO == 3 ? "plus-circle" : (Model.CODIGOITEMPEDIDOTIPO == 7 ? "adjust" : "inbox")))"></i>
        @Html.Raw(Model.NOMEPRODUTO ?? (Model.PRODUTODETALHE.PRODUTOS.CODIGOPRODUTOTIPO == 5 ? (Model.QUANTIDADE == 0 ? string.Format("{0} {1}", Model.PRODUTODETALHE.TipoProdutoPersonalizado, Model.PRODUTODETALHE.PRODUTOTAMANHO.SIGLA) : string.Format("{0} {1} {2}", Model.PRODUTODETALHE.TipoProdutoPersonalizado, Model.PRODUTODETALHE.PRODUTOS.NOME, Model.PRODUTODETALHE.PRODUTOTAMANHO.SIGLA)) : Model.PRODUTODETALHE.NomeExtendido))
        @if (!string.IsNullOrWhiteSpace(Model.DETALHES))
        {
            <br />
            <small><i> @ItemPedidoRowRes.Obs @Model.DETALHES</i></small>
        }
    </td>
    @if (Model.QUANTIDADE > 0)
    {
        <td class="order-qty">@Model.QUANTIDADE.GetValueOrDefault().ToString("0.##x")</td>
        <td class="order-price hidden-xs">@Model.VALORUNITARIO.GetValueOrDefault().ToString("C")</td>
        <td class="order-total">@((Model.VALORUNITARIO * Model.QUANTIDADE).GetValueOrDefault().ToString("C"))</td>
    }
</tr>
@foreach (var itemFilho in Model.ITENSPEDIDOFILHOS.OrderBy(c => c.PRODUTODETALHE.PRODUTOS.CODIGOPRODUTOTIPO).ThenBy(c => c.CODIGOITEMPEDIDOTIPO))
{
    @Html.Partial("_ItemPedidoRow", itemFilho)
}
@if (ViewBag.Nivel == 0 && Model.ValorTotal != Model.ValorTotalIncluindoFilhos)
{
    <tr id="detail-subtotal-@detailId">
        <td colspan="4" class="text-right text-bold">
            @Model.ValorTotalIncluindoFilhos.ToString("C")
        </td>
    </tr>
}