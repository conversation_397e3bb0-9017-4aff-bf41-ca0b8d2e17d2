﻿@model List<RAL.Consumer.Data.Entities.ITENSPEDIDO>

@{
    /**/

    /**/

    /**/

    ViewBag.Title = "Monitor de " + ViewBag.Acao;
    List<int?> codigosPedidos = ViewBag.IdsPedidos;
}

<span id="acao" class="hide">@ViewBag.Acao</span>

@if (Model != null && Model.Count > 0)
{
    <input type="hidden" id="maxItemPedido" name="maxItemPedido" value="@ViewBag.MaxItemPedido">

    <div class="portlets-wrapper" style="overflow-x:auto; padding-bottom:30px">
        <div class="row row-horizon">
            @{int ordem = 1; int idItemAtual = 0; string bgClassItem = "bg-gray-lighter";}

            @foreach (var codigoPedido in codigosPedidos)
            {
                var pedido = Model.FirstOrDefault(i => i.CODIGOPEDIDO == codigoPedido).PEDIDOS;

                <div id="item@(pedido.CODIGO)" data-iditempedido="@pedido.CODIGO" data-toggle="portlet" class="col-xl-2 col-lg-3 col-md-6 col-sm-6 col-xs-12 itempedidoproducao" style="height:100%; min-height: 120px">
                    <div class="panel panel-default" style="border-color:#808080">
                        <div class="panel-heading">
                            <div class="row">
                                <div class="col-xs-12">
                                    <h4>
                                        <span class="label label-info pull-right">Ped @pedido.CODIGO</span>
                                        <label class="label label-primary">@(ordem++)º</label>
                                        @if (pedido.Tipo == "Mesa/Comanda")
                                        {
                                            @ViewBag.MesaOuComanda
                                        }
                                        else
                                        {
                                            @pedido.Tipo
                                        }
                                        @if (pedido.NUMERO > 0)
                                        {
                                            @pedido.NUMERO
                                        }
                                    </h4>
                                </div>
                            </div>

                            @if (pedido.PEDIDOGRUPOENTREGA != null)
                            {
                                <div class="row">
                                    <div class="col-xs-12">
                                        <strong>Grupo de Entrega: #@pedido.PEDIDOGRUPOENTREGA.CODIGOGRUPOENTREGA</strong>
                                    </div>
                                </div>
                            }


                            <div class="row">
                                <div class="col-xs-12">
                                    @if (ViewBag.ExibePedidoSenha == true)
                                    {
                                        <span class="label label-primary pull-right"> Senha: @pedido.SENHA</span>
                                    }
                                    @if (!string.IsNullOrWhiteSpace(pedido.NOME))
                                    {
                                        <span><small>@pedido.NOME</small></span>
                                    }
                                    else
                                    {
                                        <span><small>&nbsp;</small></span>
                                    }
                                    @if (pedido.DELIVERY != null)
                                    {
                                        if (pedido.DELIVERY.TIPOENTREGA.CODIGO != 1)
                                        {
                                            <span><small>*** Cliente irá @pedido.DELIVERY.TIPOENTREGA.DESCRICAO ***</small></span>
                                        }

                                        if (!string.IsNullOrWhiteSpace(pedido.DELIVERY.OBSERVACAO))
                                        {
                                            <span><small>@pedido.DELIVERY.OBSERVACAO</small></span>
                                        }
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="panel-wrapper">

                            @foreach (var itemPedidos in Model.Where(i => i.CODIGOPEDIDO == codigoPedido))
                            {

                                if (idItemAtual != itemPedidos.CODIGO)
                                {
                                    if (bgClassItem == "bg-gray-lighter")
                                    {
                                        bgClassItem = "bg-gray";
                                    }
                                    else
                                    {
                                        bgClassItem = "bg-gray-lighter";
                                    }

                                    idItemAtual = itemPedidos.CODIGO;
                                }

                                <div class="panel-body panel-body-pedido @bgClassItem" style="cursor: pointer;">
                                    <div class="row">
                                        <div class="col-xs-12 modo-preparo" style="color:black" data-load="@Url.Action("ModoPreparo", new { id = itemPedidos.CODIGO })">
                                            <span style="background-color:#fff;padding: 0 4px"><strong>@(itemPedidos.QUANTIDADE > 0 ? itemPedidos.QUANTIDADE : 1)</strong></span> @itemPedidos.NOMEPRODUTO

                                            <span class="pull-right text-right">

                                                @if (ViewBag.Acao == "Produção")
                                                {
                                                    <a class="btn btn-purple navbar-btn btn-pronto btn-item-individual" title="Pronto" href="@Url.Action("MarcarProduzido")" data-codigoitempedido="@itemPedidos.CODIGO" data-codigocozinha="@itemPedidos.PRODUTODETALHE.PRODUTOS.CODIGOCOZINHA" data-nome="@itemPedidos.NOMEPRODUTO" data-codigopedido="@itemPedidos.PEDIDOS.CODIGO">P</a>

                                                    <a class="btn btn-success navbar-btn btn-pronto-entregue btn-item-individual" title="Pronto e Entregue" href="@Url.Action("MarcarProduzidoEntregue")" data-codigoitempedido="@itemPedidos.CODIGO" data-codigocozinha="@itemPedidos.PRODUTODETALHE.PRODUTOS.CODIGOCOZINHA" data-nome="@itemPedidos.NOMEPRODUTO" data-codigopedido="@itemPedidos.PEDIDOS.CODIGO">E</a>
                                                }
                                                else
                                                {
                                                    <a class="btn btn-success navbar-btn btn-entregue btn-item-individual" title="Entregue" href="@Url.Action("MarcarEntregue")" data-codigoitempedido="@itemPedidos.CODIGO" data-codigocozinha="@itemPedidos.PRODUTODETALHE.PRODUTOS.CODIGOCOZINHA" data-nome="@itemPedidos.NOMEPRODUTO" data-codigopedido="@itemPedidos.PEDIDOS.CODIGO">Entregue</a>
                                                }
                                            </span>
                                        </div>

                                        @if (!string.IsNullOrEmpty(itemPedidos.DETALHES))
                                        {
                                            <div class="col-xs-12">
                                                <i>*@itemPedidos.DETALHES</i><br />
                                            </div>
                                        }

                                        @foreach (var itemComplemento in itemPedidos.ITENSPEDIDOFILHOS.Where(i => !i.DATADELETE.HasValue && i.PRODUTODETALHE.PRODUTOS.CODIGOPRODUTOTIPO == 3))
                                        {
                                            string qtd = itemComplemento.QUANTIDADE == 0 ? "1" : itemComplemento.QUANTIDADE.ToString();
                                            decimal qtdOriginal = itemComplemento.QUANTIDADE.GetValueOrDefault();

                                            if (itemComplemento.ITEMPEDIDOPAI.QUANTIDADE > 0)
                                            {
                                                qtdOriginal = Convert.ToInt32(qtdOriginal / itemComplemento.ITEMPEDIDOPAI.QUANTIDADE);
                                            }

                                            if (itemComplemento.ITEMPEDIDOPAI.CODIGOITEMPEDIDOTIPO != 7)
                                            {
                                                if (qtdOriginal != 1)
                                                {
                                                    qtd = "+" + qtd;
                                                }
                                                else // Se quantidade Original é igual a 1, não exibe para não confundir a multiplicidade
                                                {
                                                    qtd = "+";
                                                }
                                            }
                                            else
                                            {
                                                if (qtdOriginal > 1)
                                                {
                                                    qtd = "+" + "1/" + itemComplemento.ITEMPEDIDOPAI.ITEMPEDIDOPAI.ITENSPEDIDOFILHOS.Count(s => !s.DATADELETE.HasValue && s.CODIGOITEMPEDIDOTIPO == 7).ToString() + " x" + qtdOriginal;
                                                }
                                                else
                                                {
                                                    qtd = "+" + "1/" + itemComplemento.ITEMPEDIDOPAI.ITEMPEDIDOPAI.ITENSPEDIDOFILHOS.Count(s => !s.DATADELETE.HasValue && s.CODIGOITEMPEDIDOTIPO == 7).ToString();
                                                }
                                            }

                                            <div class="col-xs-12 pl-lg">
                                                <span>@qtd @itemComplemento.NOMEPRODUTO </span>
                                            </div>
                                        }

                                        @foreach (var itemFilho in itemPedidos.ITENSPEDIDOFILHOS.Where(i => !i.DATADELETE.HasValue && i.QUANTIDADE > 0 && i.PRODUTODETALHE.PRODUTOS.CODIGOPRODUTOTIPO != 3 && (!i.PRODUTODETALHE.PRODUTOS.CODIGOCOZINHA.HasValue || i.PRODUTODETALHE.PRODUTOS.CODIGOCOZINHA == i.ITEMPEDIDOPAI.PRODUTODETALHE.PRODUTOS.CODIGOCOZINHA)))
                                        {
                                            <div class="col-xs-12 pl-lg modo-preparo" data-load="@Url.Action("ModoPreparo", new { id = itemFilho.CODIGO })">
                                                <b><span style="background-color:#fff;padding: 0 4px">@((itemFilho.QUANTIDADE / (itemPedidos.QUANTIDADE > 0 ? itemPedidos.QUANTIDADE : 1)).GetValueOrDefault().ToString("0.##"))</span> @itemFilho.NomeComDetalheQuebraLinha.Replace("<br />", " ")</b>

                                                @foreach (var itemComplemento in itemFilho.ITENSPEDIDOFILHOS.Where(i => !i.DATADELETE.HasValue && i.PRODUTODETALHE.PRODUTOS.CODIGOPRODUTOTIPO == 3))
                                                {
                                                    string qtd = itemComplemento.QUANTIDADE == 0 ? "1" : itemComplemento.QUANTIDADE.ToString();
                                                    decimal qtdOriginal = itemComplemento.QUANTIDADE.GetValueOrDefault();

                                                    if (itemComplemento.ITEMPEDIDOPAI.QUANTIDADE > 0)
                                                    {
                                                        qtdOriginal = Convert.ToInt32(qtdOriginal / itemComplemento.ITEMPEDIDOPAI.QUANTIDADE);
                                                    }

                                                    if (itemComplemento.ITEMPEDIDOPAI.CODIGOITEMPEDIDOTIPO != 7)
                                                    {
                                                        if (qtdOriginal != 1)
                                                        {
                                                            qtd = "+" + qtd;
                                                        }
                                                        else // Se quantidade Original é igual a 1, não exibe para não confundir a multiplicidade
                                                        {
                                                            qtd = "+";
                                                        }
                                                    }
                                                    else
                                                    {
                                                        if (qtdOriginal > 1)
                                                        {
                                                            qtd = "+" + "1/" + itemComplemento.ITEMPEDIDOPAI.ITEMPEDIDOPAI.ITENSPEDIDOFILHOS.Count(s => !s.DATADELETE.HasValue && s.CODIGOITEMPEDIDOTIPO == 7).ToString() + " x" + qtdOriginal;
                                                        }
                                                        else
                                                        {
                                                            qtd = "+" + "1/" + itemComplemento.ITEMPEDIDOPAI.ITEMPEDIDOPAI.ITENSPEDIDOFILHOS.Count(s => !s.DATADELETE.HasValue && s.CODIGOITEMPEDIDOTIPO == 7).ToString();
                                                        }
                                                    }

                                                    <span>@qtd @itemComplemento.NOMEPRODUTO </span>
                                                }
                                            </div>
                                        }
                                    </div>
                                    <div class="row" style="margin-top:6px;">
                                        <div class="col-xs-12">
                                            @{
                                                string NomeExibir = "Não Definido";
                                                if (itemPedidos.CONTATOS != null)
                                                {
                                                    NomeExibir = itemPedidos.CONTATOS.NOME; //se existe um coloaborador atribuído ao item, será exibido o nome dele
                                                }
                                                else
                                                {
                                                    if (itemPedidos.PEDIDOS.CONTATOSCOLABORADOR != null)
                                                    {
                                                        NomeExibir = itemPedidos.PEDIDOS.CONTATOSCOLABORADOR.NOME;  // senão exibe o colaborador atribuído ao pedido
                                                    }
                                                }
                                            }
                                            <span class="text-nowrap" style="overflow: hidden; text-overflow: ellipsis"><i class="fa fa-user" /> @NomeExibir</span>
                                            <span class="text-nowrap pull-right" style="overflow: hidden; text-overflow: ellipsis"><i class="fa fa-clock-o" /> @(((TimeSpan)(DateTime.Now - itemPedidos.DATAHORACADASTRO)).ToString("h'h 'm'm 's's'"))</span>
                                        </div>

                                    </div>
                                </div>
                            }

                            <div class="panel-footer">
                                <div class="row">
                                    @if (ViewBag.Acao == "Produção")
                                    {
                                        <div class="col-xs-6">
                                            <a class="btn btn-purple btn-block navbar-btn btn-xl btn-pronto" title="Pronto" href="@Url.Action("MarcarPedidoProduzido")" data-codigopedido="@pedido.CODIGO" data-codigocozinha="@Model.FirstOrDefault().PRODUTODETALHE.PRODUTOS.CODIGOCOZINHA" data-nome="@(Model.Count == 1 ? Model.FirstOrDefault().NOMEPRODUTO : "Vários itens")" data-codigopedido="@Model.FirstOrDefault().PEDIDOS.CODIGO"><strong>Pronto (@Model.Where(i => i.CODIGOPEDIDO == codigoPedido).Count())</strong></a>
                                        </div>
                                        <div class="col-xs-6">
                                            <a class="btn btn-success btn-block navbar-btn btn-xl btn-pronto-entregue" title="Pronto e Entregue" href="@Url.Action("MarcarPedidoProduzidoEntregue")" data-codigopedido="@pedido.CODIGO" data-codigocozinha="@Model.FirstOrDefault().PRODUTODETALHE.PRODUTOS.CODIGOCOZINHA" data-nome="@(Model.Count == 1 ? Model.FirstOrDefault().NOMEPRODUTO : "Vários itens")" data-codigopedido="@Model.FirstOrDefault().PEDIDOS.CODIGO"><strong>Entregue (@Model.Where(i => i.CODIGOPEDIDO == codigoPedido).Count())</strong></a>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="col-xs-12">
                                            <a class="btn btn-success btn-block navbar-btn btn-xl btn-entregue" title="Entregue" href="@Url.Action("MarcarPedidoEntregue")" data-codigopedido="@pedido.CODIGO" data-codigocozinha="@Model.FirstOrDefault().PRODUTODETALHE.PRODUTOS.CODIGOCOZINHA" data-nome="@(Model.Count == 1 ? Model.FirstOrDefault().NOMEPRODUTO : "Vários itens")" data-codigopedido="@Model.FirstOrDefault().PEDIDOS.CODIGO"><strong>Entregue (@Model.Where(i => i.CODIGOPEDIDO == codigoPedido).Count())</strong></a>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            }
        </div>
    </div>
}
else
{
    <div class="well-lg alert alert-success">
        @Consumer.Resources.Mobile.Comanda.Views.Monitor.ProduzirPorPedidoRes.TodosPedidosAtendidos
    </div>
}

<nav class="navbar navbar-inverse navbar-fixed-bottom" id="barraStatusInferior">
    <div class="container-fluid">
        <div class="text-center text-white p-sm">
            @(codigosPedidos.Count()) pedidos para @ViewBag.Acao
            @if (codigosPedidos != null && codigosPedidos.Count > 0)
            {
                <text>
                    <small> | <a class="text-warning" id="btnLimparTodos" data-codigocozinha="@Model.Select(i=> i.PRODUTODETALHE.PRODUTOS.CODIGOCOZINHA).FirstOrDefault()" data-acao="@ViewBag.Acao" href="@Url.Action("LimparTodos")">@Consumer.Resources.Mobile.Cardapio.Views.Home.IndexRes.LimparTodosDessaCozinha</a> | <a class="text-warning" href="#" data-toggle="popover" data-placement="top" data-trigger="hover click" title="Limpar tudo" data-content="No Consumer, acesse o menu APPS > Consumer Mobile > Configurar > Avançado">@Consumer.Resources.Mobile.Cardapio.Views.Home.IndexRes.LimparTudoEmTodasAsCozinhas</a></small>
                    <br>@Consumer.Resources.Mobile.Cardapio.Views.Produto.IndexRes.ExibindoItensPendentesDasUltimas @ViewBag.LimiteHoras @Consumer.Resources.Mobile.Cardapio.Views.Home.IndexRes.Horas <a class="text-warning" href="#" data-toggle="popover" data-placement="top" data-trigger="hover click" title="Alterar quantidade de horas" data-content="No Consumer, acesse o menu APPS > Consumer Mobile > Configurar > Avançado"><span class="fa fa-info-circle"></span></a>
                </text>
            }
        </div>
    </div>
</nav>

<script>
    $(function () {
        $('[data-toggle="popover"]').popover()
    })
</script>

<style>
    .popover {
        color: #000
    }

    body {
        font-size: 16px;
    }

    .navbar-btn {
        margin: 0;
    }

    .panel-body {
        height: auto;
    }

    /*row .col-lg-3 {
                            display: flex;
                            flex: 0 0 25%;
                            max-width: 25%
                        }

                        row  .col-xl-2 {
                            display: flex;
                            flex: 0 0 16%;
                            max-width: 16%
                        }

                        row  .col-md-4 {
                            display: flex;
                            flex: 0 0 33%;
                            max-width: 33%
                        }

                        row  .col-sm-6 {
                            display: flex;
                            flex: 0 0 50%;
                            max-width: 50%
                        }

                        .flex-nowrap {
                            -webkit-flex-wrap: nowrap !important;
                            -ms-flex-wrap: nowrap !important;
                            flex-wrap: nowrap !important;
                        }

                        .flex-row {
                            display: flex;
                            -webkit-box-orient: horizontal !important;
                            -webkit-box-direction: normal !important;
                            -webkit-flex-direction: row !important;
                            -ms-flex-direction: row !important;
                            flex-direction: row !important;
                        }*/

    .row-horizon {
        overflow-x: auto;
        overflow-y: hidden;
        white-space: nowrap;
        -webkit-overflow-scrolling: touch;
    }

        .row-horizon > [class*="col-lg"], .row-horizon > [class*="col-md"], .row-horizon > [class*="col-sm"], .row-horizon > [class*="col-xs"] {
            float: none;
            display: inline-block;
            white-space: normal;
            vertical-align: top;
        }

        .row-horizon > .col-xs-12 {
            width: 90%;
        }

        .row-horizon > .col-xs-11 {
            width: 82.5%;
        }

        .row-horizon > .col-xs-10 {
            width: 75%;
        }

        .row-horizon > .col-xs-9 {
            width: 67.5%;
        }

        .row-horizon > .col-xs-8 {
            width: 60%;
        }

        .row-horizon > .col-xs-7 {
            width: 52.5%;
        }

        .row-horizon > .col-xs-6 {
            width: 45%;
        }

        .row-horizon > .col-xs-5 {
            width: 37.5%;
        }

        .row-horizon > .col-xs-4 {
            width: 30%;
        }

        .row-horizon > .col-xs-3 {
            width: 22.5%;
        }

        .row-horizon > .col-xs-2 {
            width: 15%;
        }

        .row-horizon > .col-xs-1 {
            width: 7.5%;
        }

    @@media (min-width: 768px) {
        .row-horizon > .col-sm-12 {
            width: 90%;
        }

        .row-horizon > .col-sm-11 {
            width: 82.5%;
        }

        .row-horizon > .col-sm-10 {
            width: 75%;
        }

        .row-horizon > .col-sm-9 {
            width: 67.5%;
        }

        .row-horizon > .col-sm-8 {
            width: 60%;
        }

        .row-horizon > .col-sm-7 {
            width: 52.5%;
        }

        .row-horizon > .col-sm-6 {
            width: 45%;
        }

        .row-horizon > .col-sm-5 {
            width: 37.5%;
        }

        .row-horizon > .col-sm-4 {
            width: 30%;
        }

        .row-horizon > .col-sm-3 {
            width: 22.5%;
        }

        .row-horizon > .col-sm-2 {
            width: 15%;
        }

        .row-horizon > .col-sm-1 {
            width: 7.5%;
        }
    }

    @@media (min-width: 992px) {
        .row-horizon > .col-md-12 {
            width: 90%;
        }

        .row-horizon > .col-md-11 {
            width: 82.5%;
        }

        .row-horizon > .col-md-10 {
            width: 75%;
        }

        .row-horizon > .col-md-9 {
            width: 67.5%;
        }

        .row-horizon > .col-md-8 {
            width: 60%;
        }

        .row-horizon > .col-md-7 {
            width: 52.5%;
        }

        .row-horizon > .col-md-6 {
            width: 45%;
        }

        .row-horizon > .col-md-5 {
            width: 37.5%;
        }

        .row-horizon > .col-md-4 {
            width: 30%;
        }

        .row-horizon > .col-md-3 {
            width: 22.5%;
        }

        .row-horizon > .col-md-2 {
            width: 15%;
        }

        .row-horizon > .col-md-1 {
            width: 7.5%;
        }
    }

    @@media (min-width: 1200px) {
        .row-horizon > .col-lg-12 {
            width: 90%;
        }

        .row-horizon > .col-lg-11 {
            width: 82.5%;
        }

        .row-horizon > .col-lg-10 {
            width: 75%;
        }

        .row-horizon > .col-lg-9 {
            width: 67.5%;
        }

        .row-horizon > .col-lg-8 {
            width: 60%;
        }

        .row-horizon > .col-lg-7 {
            width: 52.5%;
        }

        .row-horizon > .col-lg-6 {
            width: 45%;
        }

        .row-horizon > .col-lg-5 {
            width: 37.5%;
        }

        .row-horizon > .col-lg-4 {
            width: 30%;
        }

        .row-horizon > .col-lg-3 {
            width: 22.5%;
        }

        .row-horizon > .col-lg-2 {
            width: 15%;
        }

        .row-horizon > .col-lg-1 {
            width: 7.5%;
        }
    }
</style>