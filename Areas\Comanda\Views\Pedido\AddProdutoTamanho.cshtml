﻿@using Consumer.Resources.Mobile.Comanda.Views.Pedido
@model RAL.Consumer.Mobile.Models.ItemPedidoTamanhoViewModels
@{
    ViewBag.Title = Model.NomeProduto;
}

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true)

    @*<h2>
            @Model.NomeProduto
        </h2>*@
    @Html.HiddenFor(model => model.CodigoProduto)
    @Html.HiddenFor(model => model.CodigoEtiqueta)

    if (Model.TodosTamanhos.Count > 0)
    {
        <h4>@AddProdutoTamanhoRes.SelecioneOTamanho</h4>
        <div class="btn-group" data-toggle="buttons">
            @foreach (var item in Model.TodosTamanhos)
            {
                <label class="btn btn-primary">
                    <input type="radio" required data-msg="@AddProdutoTamanhoRes.SelecioneTamanho" id="tam-@(item.Codigo)" data-CodigoProdutoDetalhe="@item.Codigo" name="Tamanho" data-QtdMaximaPartes="@item.QtdMaximaPartes" value="@item.CodigoTamanho" data-wizard="@(Json.Encode(item.HasWizard))" />@item.Descricao<br /><small class="parte-tamanho-preco">@item.Valor.ToString("C")</small>
                </label>
            }
        </div>
        @Html.ValidationMessage("Tamanho", new { @class = "text-danger" })
    }

    <div id="todasPartes" class="hide">
        <h4 id="todasPartesTitle">@AddProdutoTamanhoRes.SelecioneUmTamanho</h4>
        @if (Model.TodasPartes.Count == 0)
        {
            <div class="alert alert-warning">
                <i class="glyphicon glyphicon-warning-sign"></i>@AddProdutoTamanhoRes.NaoHaItens
            </div>
        }
        else
        {
            <select id="todasPartesBase" class="hide">
                <option value="">@AddProdutoTamanhoRes.OutroSabor</option>
                @foreach (var parte in Model.TodasPartes)
                {
                    <option value="@parte.Codigo"
                            data-codigotamanho="@parte.CodigoTamanho" data-sigla="@parte.Sigla">
                        @parte.Descricao (@parte.Valor.ToString("C"))
                    </option>
                }
            </select>
            <ul id="selectedPartes" class="list-group">
                @for (int parteIndex = 1; parteIndex < Model.TodosTamanhos.Max(t => t.QtdMaximaPartes); parteIndex++)
                {
                    <li class="list-group-item list-group-item-info@(parteIndex > 1 ? " hide" : "")" data-parte="@parteIndex">
                        <select name="@Html.NameFor(m => m.SelectedPartes)" class="form-control">
                            <option value="">@AddProdutoTamanhoRes.OutroSabor</option>
                        </select>
                    </li>
                }
            </ul>
        }
    </div>

    <div id="todasObservacoes">
        
    </div>

    <div id="todosComplementos">

    </div>

    <nav id="actions" class="navbar navbar-default navbar-fixed-bottom">
        <div id="item-pedido-actions" class="container-fluid">
            <div class="col-xs-6">
                <a id="btn-voltar" class="btn btn-default btn-block navbar-btn" title="@AddProdutoTamanhoRes.Voltar"><i class="glyphicon glyphicon-circle-arrow-left"></i> <span class="hidden-xs">@AddProdutoTamanhoRes.Voltar</span><small class="visible-xs">@AddProdutoTamanhoRes.Voltar</small></a>
            </div>
            <div class="col-xs-6">
                <button class="btn btn-success btn-block navbar-btn" title="@AddProdutoTamanhoRes.Ok"><i class="glyphicon glyphicon-check"></i> <span class="hidden-xs">@AddProdutoTamanhoRes.Ok</span><small class="visible-xs">@AddProdutoTamanhoRes.Ok</small></button>
            </div>
        </div>
    </nav>
}

