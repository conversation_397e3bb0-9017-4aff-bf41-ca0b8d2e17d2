﻿@using Consumer.Resources.Mobile.Comanda.Views.Wizard
@model RAL.Consumer.Mobile.Areas.Comanda.Models.ProdutoDetalheViewModel
@{
    ViewBag.Title = Model.Nome;
}

@using (Html.BeginForm(null, null, FormMethod.Post, new { id = "formWizard" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true)

    @Html.HiddenFor(model => model.CodigoProdutoDetalhe)
    @Html.HiddenFor(model => model.CodigoProduto)

    if (Model.QtdMaximaPartes > 1 && Model.TodasPartes.Count > 0)
    {
        <div class="panel panel-danger" id="todasPartes">
            <div role="tab" class="panel-heading" style="color: #000; background-color: limegreen">
                <h4 class="panel-title">
                    @AddRes.AdicionarPartes
                </h4>
                <p style="margin: 5px 0 0 0; line-height: 1.2; font-size: 85%; color: #555; font-style: italic">
                    @String.Format(@AddRes.SelecionePartes, (Model.QtdMaximaPartes - 1))
                </p>
            </div>
            <div>
                <div class="panel-body">
                    <ul id="selectedPartes" class="list-group">
                        @for (int parteIndex = 1; parteIndex < Model.QtdMaximaPartes; parteIndex++)
                        {
                            <li class="list-group-item" data-parte="@parteIndex">
                                <select name="@Html.NameFor(m => m.SelectedPartes)" class="form-control">
                                    <option value="">@(parteIndex + 1)º sabor...</option>
                                    @foreach (var parte in Model.TodasPartes)
                                    {
                                        <option value="@parte.Codigo"
                                                data-codigotamanho="@parte.CodigoTamanho" data-sigla="@parte.Sigla">
                                            @parte.Descricao (@parte.Valor.ToString("C"))
                                        </option>
                                    }
                                </select>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </div>
    }

    foreach (var container in Model.Perguntas.Select((pergunta, i) => new { i, pergunta }))
    {
        <div class="panel panel-danger">
            <div role="tab" class="panel-heading" style="color: #000; background-color: #@container.pergunta.ColorHex">
                <h4 class="panel-title">
                    @container.pergunta.Descricao
                </h4>
                <p style="margin: 5px 0 0 0; line-height: 1.2; font-size: 85%; color: #555; font-style: italic">@container.pergunta.Instrucao</p>
            </div>
            <div>
                <div class="panel-body">
                    @Html.HiddenFor(modelItem => modelItem.Perguntas[container.i].CodigoPergunta, new { data_id = container.pergunta.CodigoPergunta, data_pergunta = true, data_item_min = container.pergunta.QtdItensMin, data_item_max = container.pergunta.QtdItensMax, data_opc_min = container.pergunta.QtdRespostaMin, data_opc_max = container.pergunta.QtdRespostaMax })
                    @*<input type="hidden" name="PerguntasSelecionadas[@accordionId].Id" value="@container.pergunta.Id" data-id="@(container.pergunta.Id)" data-pergunta="true" data-item-min="@container.pergunta.QtdItensMin" data-item-max="@container.pergunta.QtdItensMax" data-opc-min="@container.pergunta.QtdRespostaMin" data-opc-max="@container.pergunta.QtdRespostaMax" />*@

                    @Html.ValidationMessageFor(modelItem => modelItem.Perguntas[container.i].CodigoPergunta)
                    @*@Html.ValidationMessage($"PerguntasSelecionadas[{accordionId}].Id")*@

                    @for (int o = 0; o < container.pergunta.Opcoes.Count; o++)
                    {
                        var opc = container.pergunta.Opcoes;
                        if (container.pergunta.QtdRespostaMax == 1 && (container.pergunta.QtdItensMax == 1 || opc[o].CodigoProdutoDetalhe == null || container.pergunta.Opcoes.All(t => t.QtdItensMax == 1)))
                        {
                            <div class="radio c-radio c-right clearfix mb-lg mt-lg">
                                <label for="opc-@opc[o].CodigoOpcao" style="width: 100%">
                                    <input type="radio" name="Perguntas[@container.i].OpcoesEscolhidas[0].CodigoOpcao" value="@opc[o].CodigoOpcao" id="opc-@opc[o].CodigoOpcao" data-parent="@(container.pergunta.CodigoPergunta)" data-nome="@opc[o].Descricao" data-precovenda="@opc[o].PrecoVenda.GetValueOrDefault().ToString("N", System.Globalization.CultureInfo.InvariantCulture)">
                                    <span class="fa fa-check"></span>@opc[o].Descricao
                                    @if (opc[o].PrecoVenda.HasValue)
                                    {
                                        <br />
                                        <b>+ @opc[o].PrecoFormatado</b>
                                    }
                                </label>
                            </div>
                        }
                        else if (container.pergunta.QtdItensMax == 1 || opc[o].CodigoProdutoDetalhe == null || container.pergunta.Opcoes.All(t => t.QtdItensMax == 1))
                        {
                            <div class="checkbox c-checkbox c-right clearfix mb0 mt-lg">
                                <label for="opc-@opc[o].CodigoOpcao" style="width: 100%">
                                    <input type="checkbox" name="Perguntas[@container.i].OpcoesEscolhidas[@o].CodigoOpcao" value="@opc[o].CodigoOpcao" id="opc-@opc[o].CodigoOpcao" data-parent="@(container.pergunta.CodigoPergunta)" data-nome="@opc[o].Descricao" data-precovenda="@opc[o].PrecoVenda.GetValueOrDefault().ToString("N", System.Globalization.CultureInfo.InvariantCulture)">
                                    <span class="fa fa-check"></span>@opc[o].Descricao
                                    @if (opc[o].PrecoVenda.HasValue)
                                    {
                                        <br />
                                        <b>+ @opc[o].PrecoFormatado</b>
                                    }
                                </label>
                            </div>
                            <input type="hidden" name="Perguntas[@container.i].OpcoesEscolhidas[@o].Quantidade" value="1">
                        }
                        else
                        {
                            <input type="hidden" name="Perguntas[@container.i].OpcoesEscolhidas[@o].CodigoOpcao" value="@opc[o].CodigoOpcao" />
                            <div class="pb pt clearfix">
                                <div class="pull-right text-right" style="width: 130px">
                                    <div class="input-group">
                                        <span class="input-group-btn">
                                            <button type="button" class="btn btn-default btn-minus">
                                                <span class="fa fa-minus text-danger"></span>
                                            </button>
                                        </span>
                                        <input type="number" class="form-control text-center" name="Perguntas[@container.i].OpcoesEscolhidas[@o].Quantidade" id="com-@opc[o].CodigoOpcao" data-parent="@(container.pergunta.CodigoPergunta)" data-nome="@opc[o].Descricao" data-precovenda="@opc[o].PrecoVenda.GetValueOrDefault().ToString("N", System.Globalization.CultureInfo.InvariantCulture)" value="0" data-val-range-min="0" @(opc[o].QtdItensMax > 0 ? " data-val-range-max=" + opc[o].QtdItensMax + "" : "")>
                                        <span class="input-group-btn">
                                            <button type="button" class="btn btn-default btn-plus">
                                                <span class="fa fa-plus text-success"></span>
                                            </button>
                                        </span>
                                    </div>
                                    @if (opc[o].QtdItensMax > 0 && (opc[o].QtdItensMax < container.pergunta.QtdItensMax || container.pergunta.QtdItensMax == 0))
                                    {
                                        <i class="small"><small>Máx. @opc[o].QtdItensMax</small></i>
                                    }
                                </div>
                                @opc[o].Descricao
                                @if (opc[o].PrecoVenda.HasValue)
                                {
                                    <br />
                                    <b>+ @opc[o].PrecoFormatado</b>
                                }
                            </div>
                        }
                    }
                </div>
            </div>
        </div>
    }

    <nav id="actions" class="navbar navbar-default navbar-fixed-bottom">
        <div id="item-pedido-actions" class="container-fluid">
            <div class="col-xs-6">
                <a id="btn-voltar" class="btn btn-lg btn-default btn-block navbar-btn" title="@AddRes.Voltar"><i class="glyphicon glyphicon-circle-arrow-left"></i> <span class="hidden-xs">@AddRes.Voltar</span><small class="visible-xs">@AddRes.Voltar</small></a>
            </div>
            <div class="col-xs-6">
                <button class="btn btn-lg btn-success btn-block navbar-btn" title="Ok"><i class="glyphicon glyphicon-check"></i> <span class="hidden-xs">Ok</span><small class="visible-xs">Ok</small></button>
            </div>
        </div>
    </nav>
}

@*LOG: <pre>@Newtonsoft.Json.JsonConvert.SerializeObject(Model, Newtonsoft.Json.Formatting.Indented)</pre>*@
