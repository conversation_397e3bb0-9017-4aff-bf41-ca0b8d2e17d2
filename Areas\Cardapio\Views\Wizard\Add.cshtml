﻿@using Consumer.Resources.Mobile.Cardapio.Views.Wizard;
@model RAL.Consumer.Mobile.Areas.Comanda.Models.ProdutoDetalheViewModel
@{
    ViewBag.Title = Model.Nome;
}

@using (Ajax.BeginForm("Add", "Wizard", new { }, new AjaxOptions { HttpMethod = "POST", OnSuccess = "OnSuccessAdd", OnBegin = "OnBeginAddWizard" }, new { id = "formWizard" }))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true)

    @Html.HiddenFor(model => model.CodigoProdutoDetalhe)
    @Html.HiddenFor(model => model.CodigoProduto)
    @Html.HiddenFor(model => model.TodasObservacoes)


    if (ViewBag.ExibirImagemDetalhe == true)
    {
        <div class="row" style="margin-top: -15px">
            <img id="produtoModalImagePath" src="@(Model.UrlImage + "?q=" + DateTime.Now.Ticks)" alt="Foto" class="img-responsive center-block" />
        </div>
    }
    if (ViewBag.ExibirDescricaoDetalhe == true)
    {
        <div class="row">
            <div class="col-xs-12">
                <p text-justify">
                    @Model.Descricao
                </p>
            </div>
        </div>
    }

    if (Model.QtdMaximaPartes > 1 && Model.TodasPartes.Count > 0)
    {
        <div class="panel panel-success" id="todasPartes">
            <div role="tab" class="panel-heading" style="color: #000; background-color: #32cd32">
                <h4 class="panel-title">
                    @AddRes.AddPartes
                </h4>
                <p style="margin: 5px 0 0 0; line-height: 1.2; font-size: 85%; color: #555; font-style: italic">
                    @AddRes.SelecioneAte @(Model.QtdMaximaPartes - 1) @AddRes.Partes
                </p>
            </div>
            <div>
                <div class="panel-body">
                    <ul id="selectedPartes" class="list-group">
                        @for (int parteIndex = 1; parteIndex < Model.QtdMaximaPartes; parteIndex++)
                        {
                            <li class="list-group-item" data-parte="@parteIndex">
                                <select name="@Html.NameFor(m => m.SelectedPartes)" class="form-control">
                                    <option value="">@(parteIndex + 1)@AddRes.Sabor</option>
                                    @foreach (var parte in Model.TodasPartes)
                                    {
                                        <option value="@parte.Codigo"
                                                data-codigotamanho="@parte.CodigoTamanho" data-sigla="@parte.Sigla">
                                            @parte.Descricao (@parte.Valor.ToString("C"))
                                        </option>
                                    }
                                </select>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </div>
    }

    // if se tem pergunta
    if (Model.Perguntas != null && Model.Perguntas.Count > 0)
    {
        foreach (var container in Model.Perguntas.Select((pergunta, i) => new { i, pergunta }))
        {
            <div class="panel panel-danger">
                <div role="tab" class="panel-heading" style="color: #000; background-color: #@container.pergunta.ColorHex">
                    <h4 class="panel-title">
                        @container.pergunta.Descricao
                    </h4>
                    <p style="margin: 5px 0 0 0; line-height: 1.2; font-size: 85%; color: #555; font-style: italic">@container.pergunta.Instrucao</p>
                </div>
                <div>
                    <div class="panel-body">
                        @Html.HiddenFor(modelItem => modelItem.Perguntas[container.i].CodigoPergunta, new { data_id = container.pergunta.CodigoPergunta, data_pergunta = true, data_item_min = container.pergunta.QtdItensMin, data_item_max = container.pergunta.QtdItensMax, data_opc_min = container.pergunta.QtdRespostaMin, data_opc_max = container.pergunta.QtdRespostaMax })
                        @*<input type="hidden" name="PerguntasSelecionadas[@accordionId].Id" value="@container.pergunta.Id" data-id="@(container.pergunta.Id)" data-pergunta="true" data-item-min="@container.pergunta.QtdItensMin" data-item-max="@container.pergunta.QtdItensMax" data-opc-min="@container.pergunta.QtdRespostaMin" data-opc-max="@container.pergunta.QtdRespostaMax" />*@

                        @Html.ValidationMessageFor(modelItem => modelItem.Perguntas[container.i].CodigoPergunta)
                        @*@Html.ValidationMessage($"PerguntasSelecionadas[{accordionId}].Id")*@

                        @for (int o = 0; o < container.pergunta.Opcoes.Count; o++)
                        {
                            var opc = container.pergunta.Opcoes;
                            if (container.pergunta.QtdRespostaMax == 1 && (container.pergunta.QtdItensMax == 1 || opc[o].CodigoProdutoDetalhe == null || container.pergunta.Opcoes.All(t => t.QtdItensMax == 1)))
                            {
                                <div class="radio c-radio c-right clearfix mb-lg mt-lg">
                                    <label for="opc-@opc[o].CodigoOpcao" style="width: 100%">
                                        <input type="radio" name="Perguntas[@container.i].OpcoesEscolhidas[0].CodigoOpcao" value="@opc[o].CodigoOpcao" id="opc-@opc[o].CodigoOpcao" data-parent="@(container.pergunta.CodigoPergunta)" data-nome="@opc[o].Descricao" data-precovenda="@opc[o].PrecoVenda.GetValueOrDefault().ToString("N", System.Globalization.CultureInfo.InvariantCulture)">
                                        <span class="fa fa-check"></span>@opc[o].Descricao
                                        @if (opc[o].PrecoVenda.HasValue)
                                        {
                                            <br />
                                            <b>+ @opc[o].PrecoFormatado</b>
                                        }
                                    </label>
                                </div>
                            }
                            else if (container.pergunta.QtdItensMax == 1 || opc[o].CodigoProdutoDetalhe == null || container.pergunta.Opcoes.All(t => t.QtdItensMax == 1))
                            {
                                <div class="checkbox c-checkbox c-right clearfix mb0 mt-lg">
                                    <label for="opc-@opc[o].CodigoOpcao" style="width: 100%">
                                        <input type="checkbox" name="Perguntas[@container.i].OpcoesEscolhidas[@o].CodigoOpcao" value="@opc[o].CodigoOpcao" id="opc-@opc[o].CodigoOpcao" data-parent="@(container.pergunta.CodigoPergunta)" data-nome="@opc[o].Descricao" data-precovenda="@opc[o].PrecoVenda.GetValueOrDefault().ToString("N", System.Globalization.CultureInfo.InvariantCulture)">
                                        <span class="fa fa-check"></span>@opc[o].Descricao
                                        @if (opc[o].PrecoVenda.HasValue)
                                        {
                                            <br />
                                            <b>+ @opc[o].PrecoFormatado</b>
                                        }
                                    </label>
                                </div>
                                <input type="hidden" name="Perguntas[@container.i].OpcoesEscolhidas[@o].Quantidade" value="1">
                            }
                            else
                            {
                                <input type="hidden" name="Perguntas[@container.i].OpcoesEscolhidas[@o].CodigoOpcao" value="@opc[o].CodigoOpcao" />
                                <div class="pb pt clearfix">
                                    <div class="pull-right text-right" style="width: 130px">
                                        <div class="input-group">
                                            <span class="input-group-btn">
                                                <button type="button" class="btn btn-default btn-minus">
                                                    <span class="fa fa-minus text-danger"></span>
                                                </button>
                                            </span>
                                            <input type="number" class="form-control text-center espacoInterno" name="Perguntas[@container.i].OpcoesEscolhidas[@o].Quantidade" id="com-@opc[o].CodigoOpcao" data-parent="@(container.pergunta.CodigoPergunta)" data-nome="@opc[o].Descricao" data-precovenda="@opc[o].PrecoVenda.GetValueOrDefault().ToString("N", System.Globalization.CultureInfo.InvariantCulture)" value="0" data-val-range-min="0" @(opc[o].QtdItensMax > 0 ? " data-val-range-max=" + opc[o].QtdItensMax + "" : "")>
                                            <span class="input-group-btn">
                                                <button type="button" class="btn btn-default btn-plus">
                                                    <span class="fa fa-plus text-success"></span>
                                                </button>
                                            </span>
                                        </div>
                                        @if (opc[o].QtdItensMax > 0 && (opc[o].QtdItensMax < container.pergunta.QtdItensMax || container.pergunta.QtdItensMax == 0))
                                        {
                                            <i class="small"><small>Máx. @opc[o].QtdItensMax</small></i>
                                        }
                                    </div>
                                    @opc[o].Descricao
                                    @if (opc[o].PrecoVenda.HasValue)
                                    {
                                        <br />
                                        <b>+ @opc[o].PrecoFormatado</b>
                                    }
                                </div>
                            }
                        }
                    </div>
                </div>
            </div>
        }
        <div class="panel panel-danger">
            <div id="headingObs" role="tab" class="panel-heading">
                <h4 class="panel-title">
                    @Consumer.Resources.Mobile.Cardapio.Views.Produto.AddRes.AlgumaObservacao
                </h4>
            </div>
            <div class="panel-body">
                <div>
                    <span class="pull-right text-muted count_message">0 / 140</span>
                    @Html.TextAreaFor(m => m.ObservacoesAdicionais, new { @class = "form-control mb contar-texto", maxlength = "140", placeholder = Consumer.Resources.Mobile.Cardapio.Views.Produto.AddRes.AddObs })
                    @Html.ValidationMessageFor(model => model.ObservacoesAdicionais, "", new { @class = "text-danger" })
                </div>
                <div>
                    <span class="text-muted small">@Consumer.Resources.Mobile.Cardapio.Views.Produto.AddRes.AlterarValor</span><br />
                </div>
            </div>
        </div>
    }
    else
    {
        @Html.HiddenFor(model => model.PrecoVenda)
        <span style="visibility: hidden" id="PrecoTotal"></span>


        if (Model.TodosComplementos != null && Model.TodosComplementos.Count() > 0)
        {
            <div class="panel panel-danger">
                <div id="headingTwo" role="tab" class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion" href="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo" class="collapsed btn-block">@Consumer.Resources.Mobile.Cardapio.Views.Produto.AddRes.Complementos<span class="collapse-icon pull-right"><i class="fa fa-chevron-up"></i><i class="fa fa-chevron-down"></i></span></a>
                    </h4>
                </div>
                <div id="collapseTwo" role="tabpanel" aria-labelledby="headingTwo" class="panel-collapse collapse" aria-expanded="false" style="height: 0px;">
                    <div class="panel-body">
                        <div class="mb-lg meioameio hide">
                            <span title="@Consumer.Resources.Mobile.Cardapio.Views.Produto.AddRes.MetadePrincipal" class="fa fa-adjust fa-rotate-180" style="font-size: 20px; display: inline-block; vertical-align: top; width: 20px; height: 20px; margin-right: 5px; text-align: center;"></span>
                            <span title="@Consumer.Resources.Mobile.Cardapio.Views.Produto.AddRes.PizzaInteira" class="fa fa-circle" style="font-size: 20px; display: inline-block; vertical-align: top; width: 20px; height: 20px; margin-right: 5px; text-align: center;"></span>
                            <span title="@Consumer.Resources.Mobile.Cardapio.Views.Produto.AddRes.OutraMetade" class="fa fa-adjust" style="font-size: 20px; display: inline-block; vertical-align: top; width: 20px; height: 20px; margin-right: 5px; text-align: center;"></span>
                        </div>

                        <div id="complementosProdutoTamanho">
                            @for (var i = 0; i < Model.TodosComplementos.Count(); i++)
                            {
                                var g = Model.TodosComplementos.ToList();
                                <div class="checkbox c-checkbox clearfix mb-lg">
                                    @*<label for="com1-@g[i].Codigo" class="meioameio hide">
                                            <input type="checkbox" name="SelectedWizardComplementosParte[0][@i].Codigo" value="@g[i].Codigo" id="com1-@g[i].Codigo" data-precovenda="@g[i].Valor.ToString("N", System.Globalization.CultureInfo.InvariantCulture)">
                                            <span class="fa fa-check"></span>
                                        </label>*@
                                    <label for="com-@g[i].Codigo">
                                        <input type="checkbox" name="SelectedWizardComplementos[@i].Codigo" value="@g[i].Codigo" id="com-@g[i].Codigo" data-precovenda="@g[i].Valor.ToString("N", System.Globalization.CultureInfo.InvariantCulture)">
                                        <span class="fa fa-check"></span>
                                    </label>
                                    @*<label for="com2-@g[i].Codigo" class="meioameio hide">
                                            <input type="checkbox" name="SelectedWizardComplementosParte[1][@i].Codigo" value="@g[i].Codigo" id="com2-@g[i].Codigo" data-precovenda="@g[i].Valor.ToString("N", System.Globalization.CultureInfo.InvariantCulture)">
                                            <span class="fa fa-check"></span>
                                        </label>*@
                                    <i>@g[i].Descricao</i><i class="pull-right">+@g[i].Valor.ToString("C")</i>
                                </div>
                                <input type="hidden" name="SelectedWizardComplementos[@i].Qtd" value="1" />
                                @*<input type="hidden" name="SelectedWizardComplementosParte[0][@i].Qtd" value="1" />*@
                                @*<input type="hidden" name="SelectedWizardComplementosParte[1][@i].Qtd" value="1" />*@
                            }
                        </div>
                    </div>
                </div>
            </div>
        }

        if (Model.TodasObservacoes.Count() > 0)
        {
            <div class="panel panel-danger">
                <div id="headingThree" role="tab" class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#accordion" href="#collapseThree" aria-expanded="false" aria-controls="collapseThree" class="collapsed btn-block">@Consumer.Resources.Mobile.Cardapio.Views.Produto.AddRes.Observacoes<span class="collapse-icon pull-right"><i class="fa fa-chevron-up"></i><i class="fa fa-chevron-down"></i></span></a>
                    </h4>
                </div>
                <div id="collapseThree" role="tabpanel" aria-labelledby="headingThree" class="panel-collapse collapse" aria-expanded="false">
                    <div class="panel-body">
                        <div class="mb-lg meioameio hide">
                            <span title="@Consumer.Resources.Mobile.Cardapio.Views.Produto.AddRes.MetadePrincipal" class="fa fa-adjust fa-rotate-180" style="font-size: 20px; display: inline-block; vertical-align: top; width: 20px; height: 20px; margin-right: 5px; text-align: center;"></span>
                            <span title="@Consumer.Resources.Mobile.Cardapio.Views.Produto.AddRes.PizzaInteira" class="fa fa-circle" style="font-size: 20px; display: inline-block; vertical-align: top; width: 20px; height: 20px; margin-right: 5px; text-align: center;"></span>
                            <span title="@Consumer.Resources.Mobile.Cardapio.Views.Produto.AddRes.OutraMetade" class="fa fa-adjust" style="font-size: 20px; display: inline-block; vertical-align: top; width: 20px; height: 20px; margin-right: 5px; text-align: center;"></span>
                        </div>
                        @for (var i = 0; i < Model.TodasObservacoes.Count(); i++)
                        {
                            var g = Model.TodasObservacoes.ToList();
                            <div class="checkbox c-checkbox clearfix mb-lg mt-lg">
                                <label for="obs-@g[i].Chave">
                                    <input type="checkbox" name="SelectedWizardObservacoes[@i].Codigo" value="@g[i].Chave" id="obs-@g[i].Chave">
                                    <span class="fa fa-check"></span>
                                </label>
                                <i name="@g[i].Descricao">@g[i].Descricao</i>
                            </div>
                            <input type="hidden" name="SelectedWizardObservacoes[@i].Qtd" value="1" />
                        }
                    </div>
                </div>
            </div>
        }

        <h4 class="bb">
            @Consumer.Resources.Mobile.Cardapio.Views.Produto.AddRes.Quantidade
        </h4>
        <div class="form-group">
            <div class="input-group input-group-lg">
                <input class="form-control text-left" data-val="true" data-val-number="@Consumer.Resources.Mobile.Cardapio.Views.Produto.AddRes.Numero" id="Quantidade" name="Quantidade" type="number" value="1" aria-describedby="Quantidade-error" aria-invalid="false" data-val-range-min="1" data-val-range-max="9">
                <span class="input-group-btn">
                    <button type="button" class="btn btn-default btn-minus" style="border-radius: 0">
                        <span class="fa fa-minus"></span>
                    </button>
                </span>
                <span class="input-group-btn">
                    <button type="button" class="btn btn-default btn-plus">
                        <span class="fa fa-plus"></span>
                    </button>
                </span>
            </div>
            @Html.ValidationMessageFor(model => model.Quantidade, "", new { @class = "text-danger" })
        </div>
    }
    <nav id="actions" class="navbar navbar-default">
        <div id="item-pedido-actions" class="container-fluid">
            <div class="col-xs-6">
                <a href="javascript:window.history.back();" id="btn-wizard-voltar" class="btn btn-lg btn-default btn-block navbar-btn" title="@AddRes.Voltar"><i class="fa fa-arrow-circle-o-left"></i> <span class="hidden-xs">@AddRes.Voltar</span><small class="visible-xs">@AddRes.Voltar</small></a>
            </div>
            <div class="col-xs-6">
                <button class="btn btn-lg btn-success btn-block navbar-btn" title="Ok"><i class="fa fa-check"></i> <span class="hidden-xs">Ok</span><small class="visible-xs">Ok</small></button>
            </div>
        </div>
    </nav>
}

@*LOG: <pre>@Newtonsoft.Json.JsonConvert.SerializeObject(Model, Newtonsoft.Json.Formatting.Indented)</pre>*@
