﻿@using Consumer.Resources.Mobile.Comanda.App.Shared;
@model System.Web.Mvc.HandleErrorInfo

@{
    ViewBag.Title = @ErrorRes.Erro;
    Layout = "~/Views/Shared/_LayoutPage.cshtml";
}

<section class="text-center">
    <h2 class="text-danger">@ViewBag.Title</h2>
    <h2 class="text-danger" style="transform: rotate(90deg)">= (</h2>

    @if (Model != null)
    {
        <h4>@((Model.Exception.InnerException ?? Model.Exception).Message)</h4>
        <p>Origem: @Model.ControllerName/@Model.ActionName</p>
    }

    @if (Request.IsAuthenticated && (User.IsInRole("ComandaMobile") || User.IsInRole("MonitorPreparo") || User.IsInRole("SmartDelivery")))
    {
        <a href="/Comanda" class="btn btn-lg btn-success">@ErrorRes.Recomecar</a>
    }
    else
    {
        <h4>@ErrorRes.LeiaQr</h4>
    }
</section>
