﻿@using Consumer.Resources.Mobile.Comanda.Views.Home
@model RAL.Consumer.Mobile.Areas.Comanda.Models.AddPagamentoPixViewModel

@using RAL.Common
@*@using RAL.Consumer.Data.Context*@
@*@using RAL.Consumer.Data.Models;*@

@{
    ViewBag.Title = "Pagamento via Pix Online";
}

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()

    @Html.HiddenFor(model => model.CodigoPedido);
    @Html.HiddenFor(model => model.CodigoFormaPagto);
    @Html.HiddenFor(model => model.ValorRestante);
    @Html.HiddenFor(model => model.ValorTotal);

    <div class="container">
        <h2>
            @ViewBag.Title
        </h2>

        <div class="row">
            <div class="col-xs-12">
                <input type="hidden" id="orderId" value="@Model.CodigoPedido" />
                <div class="alert alert-warning" style="background-color: lightgray; color: black;">
                    <p><strong>Não saia dessa tela, aguarde o pagamento ou clique em cancelar para lançar outra forma de pagamento...</strong></p>
                    <p>Valor da Transação <strong>@Model.ValorRecebido.ToString("C")</strong></p>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-xs-12">
                <p style="text-align: center;">
                    <img src="@Model.TransacaoPagtoPix.PixImageInBase64" id="pixQrCode" class="img-responsive" style="width: 300px; margin: 0 auto;" />
                </p>
            </div>
        </div>

        <div class="row">
            <div class="col-xs-12 text-center">
                <p>
                    <span id="timerExpiresAt"></span>
                </p>
            </div>
        </div>

        <div class="row">
            <div class="col-xs-12">
                <div class="input-group">
                    <input type="text" id="pixTransactionId" class="form-control" readonly="" value="@Model.TransacaoPagtoPix.PixTransactionId" />
                </div>
            </div>
            <div class="col-xs-12">
                <div>
                    <span class="input-group-btn">
                        <button id="copyPix" class="btn btn-lg btn-default btn-block navbar-btn"><i class="fa fa-copy"></i> Copiar Código Pix</button>
                    </span>
                </div>
            </div>
        </div>
        <hr />
        <div class="row">
            <div class="col-xs-12">
                <div id="copyAlert" class="alert alert-success" style="display: none; background-color: lightgreen; color: black; text-align: center;">
                    Código copiado com sucesso!
                </div>
            </div>
        </div>
    </div>

    <a href="#"
       class="btn btn-danger btn-block btn-md"
       data-recordid="@Model.TransacaoPagtoPix.PagamentoId"
       data-recordtitle="@PagamentoRes.EstornarExcluirPagamento"
       data-path="@Url.Action("CancelarPix", "Pagamento", new { codigoPedido = Model.CodigoPedido, pagamentoIdPix = Model.TransacaoPagtoPix.PagamentoId, valorRecebido = Model.ValorRecebido })"
       data-toggle="modal"
       data-target="#confirma-estorno">
       Cancelar Pix
    </a>


    <div id="confirma-estorno" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="Estorno" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title" id="myModalLabel">Confirmação</h4>
                </div>
                <div class="modal-body">
                    <p>Confirma o cancelamento desta transação Pix</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Não</button>
                    <button type="button" class="btn btn-danger btn-ok">Sim</button>
                </div>
            </div>
        </div>
    </div>

    <input type="hidden" id="expiresAt" name="expiresAt" value="@Model.ExpiresAt.ToString("yyyy-MM-ddTHH:mm:ss")" />
    <input type="hidden" id="pagamentoIdPix" name="pagamentoIdPix" value="@Model.TransacaoPagtoPix.PagamentoId" />
    <input type="hidden" id="valorRecebido" name="valorRecebido" value="@Model.ValorRecebido" />
}

@section Scripts{
    <script>
        $('#confirma-estorno').on('click', '.btn-ok', function (e) {
            var $modalDiv = $(e.delegateTarget);
            var id = $(this).data('recordid');
            var path = $(this).data('path');

            $.ajax({
                url: path,
                type: 'POST',
                cache: false,
                async: true,
                success: function (result) {
                    if (result !== null) {
                        if (result.sucesso) {
                            window.location = '@Html.Raw(@Url.Action("Pagamento", "Home", new {CodigoPedido = Model.CodigoPedido }))';
                        }
                    }
                },
                error: function (result) {
                    $.notify("<i class='" + result.icone + "'></i> " + result.mensagem);
                }
            });

            $modalDiv.addClass('loading');
            setTimeout(function () {
                $modalDiv.modal('hide').removeClass('loading');
            }, 1000);
        });
        $('#confirma-estorno').on('show.bs.modal', function (e) {
            var data = $(e.relatedTarget).data();

            $('.btn-ok', this).data('path', data.path);
            $('.btn-ok', this).data('recordid', data.recordid);
        });
    </script>

    <script src="~/scripts/app/payment.js"></script>
}
