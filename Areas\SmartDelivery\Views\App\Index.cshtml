﻿@using Consumer.Resources.Mobile.SmartDelivery.Views;
@{
    ViewBag.Title = "Smart Delivery";
}

<nav id="nav-main" class="navbar sticky-top navbar-expand-lg navbar-light bg-white">
    <a class="navbar-brand mr-1" href="/smartdelivery">
        <img src="/content/smartdelivery/assets/Logo.png" width="36" height="36" class="d-inline-block align-middle" alt="">
    </a>
    <span class=" font-weight-bold mr-3">
        Smart Delivery
    </span>
    <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
            aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>

    <div class="collapse navbar-collapse text-center" id="navbarSupportedContent">
        <ul class="navbar-nav nav nav-pills mr-auto" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="tab-paraproduzir" href="#paraproduzir" data-toggle="pill" role="tab" aria-controls="paraproduzir" aria-selected="true" title="@IndexRes.ParaProduzir">@IndexRes.ParaProduzir <span id="orderToProduceQty"></span><span class="sr-only">(current)</span></a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="tab-emproducao" href="#emproducao" data-toggle="pill" role="tab" aria-controls="emproducao" aria-selected="false" title="@IndexRes.EmProducao">@IndexRes.EmProducao <span id="orderProducingQty"></span></a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="tab-paraentrega" href="#paraentrega" data-toggle="pill" role="tab" aria-controls="paraentrega" aria-selected="false" title="@IndexRes.ParaEntrega">@IndexRes.ParaEntrega <span id="orderProducedQty"></span></a>
            </li>
        </ul>

        <ul class="navbar-nav nav nav-pills">
            <li class="nav-item">
                <a href="#" class="nav-link mr-3 text-custom-red" data-toggle="modal" data-target="#modalSettings">
                    <i class="material-icons md-24 align-bottom mr-1">settings</i>@IndexRes.Configurar
                </a>
            </li>
            <li class="nav-item">
                @using (Html.BeginForm("LogOff", "Account", new { area = "Comanda" }, FormMethod.Post, new { id = "logoutForm" }))
                {
                    @Html.AntiForgeryToken()
                <a class="nav-link text-custom-red" href="javascript:document.getElementById('logoutForm').submit()"><i class="material-icons md-24 align-bottom mr-1">exit_to_app</i>@IndexRes.Sair</a>
                }
            </li>
        </ul>
    </div>
</nav>

<div class="container-fluid bg-light overflow-auto flex-grow-1">
    <div class="tab-content h-100" id="pills-tabContent">
        <div class="tab-pane h-100 fade show active" id="paraproduzir" role="tabpanel" aria-labelledby="tab-paraproduzir">
            @Html.Action("Index", "ParaProduzir")
        </div>
        <div class="tab-pane h-100 fade" id="emproducao" role="tabpanel" aria-labelledby="tab-emproducao">
            @Html.Action("Index", "EmProducao")
        </div>
        <div class="tab-pane h-100 fade" id="paraentrega" role="tabpanel" aria-labelledby="tab-paraentrega">
            @Html.Action("Index", "ParaEntrega")
        </div>
    </div>
</div>

<!-- Modal Order -->
<div class="modal fade" id="modalOrder" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog" style="margin: 80px 15px auto auto">
        <div class="modal-content border-0">
            <div class="modal-body">
                @* JS *@
            </div>
            <div class="modal-footer border-0 justify-content-center my-2">
                <button type="button" class="btn btn-dark col-8" data-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Settings -->
<div class="modal fade sd-glass" id="modalSettings" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content border-0">
            @* JS *@
        </div>
    </div>
</div>

<script id="template-sd-order-detail" type="text/template">
    <div class="container-fluid px-3 py-1">
        <div class="row">
            <span class="d-inline"><img src="/content/smartdelivery/assets/{{orderOrigin}}.png" class="sd-imgsize50" alt=""></span>
            <div class="col d-inline">
                <div class="row">
                    <div class="col small">
                        <p class="float-left font-weight-bold my-0">Pedido #{{orderId}}</p>
                        <p class="float-right my-0">
                            Data: {{orderCreatedDateFormatted}} | <strong data-relativedatetime="{{orderCreatedDateTime}}">...</strong>
                        </p>
                    </div>
                </div>
                <div class="row mt-2 ml-1">
                    <div class="col-12">
                        <ul class="sd-tracking-progressbar">
                            <li class="{{orderCreatedCssClass}}">@IndexRes.Atendido<br />{{orderCreatedTimeFormatted}}</li>
                            <li class="{{orderPreparationCssClass}}">@IndexRes.Producao<br />{{orderPreparationTimeFormatted}}</li>
                            <li class="{{orderDispatchCssClass}}">@IndexRes.EmRota<br />{{orderDispatchTimeFormatted}}</li>
                            <li class="{{orderDeliveryCssClass}}">@IndexRes.Entregue<br />{{orderDeliveryTimeFormatted}}</li>
                        </ul>

                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-2">
            <div class="col p-0 small border-bottom pb-2">
                <strong>@IndexRes.Entregador</strong>{{orderDeliveryMan}}
            </div>
        </div>

        <div class="row mt-2 small">
            <div class="col-12 p-0 mb-1">
                <strong>@IndexRes.Cliente</strong>
                <div class="row">
                    <div class="col-7">
                        <p class="m-0">{{orderCustomerName}}</p>
                        <p class="m-0">{{orderAddress}}</p>
                    </div>
                    <div class="col-5 p-0">
                        <p class="m-0">{{orderCustomerPhone}}</p>
                        <p class="m-0 text-truncate">{{orderCustomerEmail}}</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-3 border-top justify-content-center">
            <table class="table table-borderless col-10 small" id="table-order-itens">
                <tbody>
                    @*template-sd-order-detail-item*@
                </tbody>
                @*Ocultado até consolidar valor totais ao adicionar itens no Consumer*@
                @*<tfoot>
                        <tr class="border-top">
                            <td class="text-right py-1"><strong>Subtotal:</strong></td>
                            <td class="text-right py-1"><strong>{{orderSubtotalPriceFormatted}}</strong></td>
                        </tr>
                        <tr class="">
                            <td class="text-right py-1">Frete:</td>
                            <td class="text-right py-1">{{orderDeliveryFeeFormatted}}</td>
                        </tr>
                        <tr class="">
                            <td class="text-right py-1"><strong>Total:</strong></td>
                            <td class="text-right py-1"><strong>{{orderTotalPriceFormatted}}</strong></td>
                        </tr>
                    </tfoot>*@
            </table>
        </div>
    </div>
</script>
<script id="template-sd-order-detail-item" type="text/template">
    <tr>
        <td class="py-1">
            <p class="m-0"><strong>{{itemDescription}}</strong></p>
            <p class="m-0 ml-3">
                {{itemChildrenDescription}}
            </p>
        </td>
        <td class="text-right py-1"><strong>{{itemTotalPrice}}</strong></td>
    </tr>
</script>

@section Scripts {
    <script src="https://maps.googleapis.com/maps/api/js?key=@(ViewBag.ChaveLocalGoogleMapsApi)&libraries=geometry"></script>

    <script src="/content/smartdelivery/js/app/gm-utils.js"></script>
    <script src="/content/smartdelivery/js/app/sd-init.js"></script>
    <script src="/content/smartdelivery/js/app/sd-para-produzir.js"></script>
    <script src="/content/smartdelivery/js/app/sd-em-producao.js"></script>
    <script src="/content/smartdelivery/js/app/sd-para-entrega.js"></script>
}

