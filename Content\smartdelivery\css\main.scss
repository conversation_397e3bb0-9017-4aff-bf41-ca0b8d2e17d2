// Var Custom Here
$custom-green: #2ECC71;
$custom-red: #C6575B;
$custom-gray: #C1C1C1;
$custom-darkgray: #272b29;
$custom-Font: 'Montserrat', sans-serif, 'Segoe UI', 'Helvetica Neue', Arial;

$theme-colors: (
    "custom-green" : $custom-green,
    "custom-red" : $custom-red,
    "custom-gray" : $custom-gray,
    "custom-darkgray" : $custom-darkgray
);

//alter font theme
$font-family-base: $custom-Font; 
$link-color: $custom-red; 


/* Rules for sizing the icon. */
.material-icons.md-18 { font-size: 18px; }
.material-icons.md-24 { font-size: 24px; }
.material-icons.md-36 { font-size: 36px; }
.material-icons.md-48 { font-size: 48px; }

/* Rules for using icons as black on a light background. */
.material-icons.md-dark { color: rgba(0, 0, 0, 0.54); }
.material-icons.md-dark.md-inactive { color: rgba(0, 0, 0, 0.26); }

/* Rules for using icons as white on a dark background. */
.material-icons.md-light { color: rgba(255, 255, 255, 1); }
.material-icons.md-light.md-inactive { color: rgba(255, 255, 255, 0.3); }

.material-icons.orange600 { color: #FB8C00; }
.material-icons.gray { color: #797979; }



.sd-line-header {
    height: 8px;
}

.sd-status-order {
    width: 7px;
}

.sd-iconcol {
    width: 24px;
    padding: 0;
}

.sd-iconcol26 {
    width: 34px;
    padding: 0;
    text-align: center;
}

.sd-textredcolor {
    color: $custom-red;
}

.sd-textgreencolor {
    color: $custom-green;
}

.sd-textgraycolor {
    color: $custom-gray;
}

.sd-bg-green {
    background-color: $custom-green;
}

.sd-bg-gray {
    background-color: $custom-gray;
}


.sd-imgsize24 {
    width: 24px;
    height: 24px;
}

.sd-imgsize50 {
    width: 50px;
    height: 50px;
}

.sd-btn-sombradark {
    box-shadow: 0px 3px 10px #2C2F3D65;
    opacity: 1;
}

.sd-glass {
    box-shadow: 0 0 1rem 0 rgba(0, 0, 0, .2);   
    background-color: rgba(255, 255, 255, .15);
    backdrop-filter: blur(10px);
}

.sd-tracking {
    height: 5px;
    width: 100%;
}

.sd-tracking-current {
    background: rgb(46,204,113);
    background: linear-gradient(90deg, rgba(46,204,113,1) 0%, rgba(193,193,193,1) 38%, rgba(193,193,193,1) 100%);
}

.sd-div-select {
    background: $custom-darkgray;
    color: white;
    padding: 10px;
    box-shadow: 2px 6px 16px #5C5C5C;
    opacity: 1;
    position: absolute;
    bottom:20px;
    border-radius: 8px;

    width: 300px;
    left:50%;
    margin-left:-150px;
    display: none;
}

html, body {
  height: 100%;
}

@import "../../node_modules/bootstrap/scss/bootstrap.scss";

@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
