﻿@using Consumer.Resources.Mobile.Comanda.App.Shared;

<style>
    .navbar-header {
        width: 50% !important;
    }

    .brand-logo {
        color: #fff;
        line-height: 34px;
        font-weight: bold;
        text-align: left;
        padding: 10px 15px !important;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
    }

    @@media (max-width: 768px) {
        .navbar-header {
            width: initial !important;
        }

        .brand-logo {
            width: 65%;
            padding: 10px 0px !important;
        }
    }
</style>

<!-- START Top Navbar-->
<nav class="navbar topnavbar">
    <!-- START navbar header-->
    <div class="navbar-header">
        <button type="button" data-toggle-state="aside-toggled" data-no-persist="true" class="navbar-toggle navbar-toggle-main">
            <span class="sr-only">Menu</span>
            <em class="icon-bar"></em>
            <em class="icon-bar"></em>
            <em class="icon-bar"></em>
        </button>
        @*<button type="button" data-toggle="collapse" data-target=".navbar-collapse" class="navbar-toggle navbar-toggle-icon">
                <span class="sr-only">Mais opções</span>
                <em class="icon-options-vertical"></em>
            </button>*@
        <button type="button" data-search-open="" class="navbar-toggle navbar-toggle-icon">
            <span class="sr-only">@_TopNavbarHorizontalRes.Pesquisar</span>
            <em class="icon-magnifier"></em>
        </button>
        <a href="#/" class="navbar-brand">
            <div class="brand-logo">
                @ViewBag.Title
            </div>
            <div class="brand-logo-collapsed">
            </div>
        </a>
    </div>
    <!-- END navbar header-->
    <!-- START Nav wrapper-->
    <div class="navbar-collapse collapse">
        <!-- START Right Navbar-->
        <ul class="nav navbar-nav navbar-right">
            <li>
                <!-- Button used to collapse the left sidebar. Only visible on tablet and desktops-->
                <a href="#" data-trigger-resize="" data-toggle-state="aside-collapsed-text" class="hidden-xs">
                    <em class="fa fa-navicon"></em>
                </a>
                <!-- Button to show/hide the sidebar on mobile. Visible on mobile only.-->
                <a href="#" data-toggle-state="aside-toggled" data-no-persist="true" class="visible-xs sidebar-toggle">
                    <em class="fa fa-navicon"></em>
                </a>
            </li>
            <!-- START lock screen-->
            @*<li>
                    <a href="@Url.Action("Lock", "Pages")" title="Lock screen">
                        <em class="icon-lock"></em>
                    </a>
                </li>*@
            <!-- END lock screen-->
            <!-- Search icon-->
            <li>
                <a href="#" data-search-open="">
                    <em class="icon-magnifier"></em> <span class="hidden-lg hidden-md hidden-sm">@_TopNavbarHorizontalRes.Pesquisar</span>
                </a>
            </li>
            <!-- Fullscreen (only desktops)-->
            <li class="visible-lg">
                <a href="#" data-toggle-fullscreen="">
                    <em class="fa fa-expand"></em>
                </a>
            </li>
            <li>
                <a href="@Url.Action("Index", "Home", new { Area = "Cardapio" })">
                    <em class="icon-basket-loaded"></em> <span class="hidden-lg hidden-md hidden-sm">@_TopNavbarHorizontalRes.MinhaComanda</span>
                </a>
            </li>
            <!-- START Alert menu-->
            @*<li class="dropdown dropdown-list">
                    <a href="#" data-toggle="dropdown">
                        <em class="icon-basket-loaded"></em> <span class="hidden-lg hidden-md hidden-sm">Minha Conta</span>
                        <div id="mini-cart-qtd" class="label label-danger hide">0</div>
                    </a>
                    <!-- START Dropdown menu-->
                    <ul class="dropdown-menu animated flipInX">
                        <li>
                            <!-- START list group-->
                            <!-- END list group-->
                        </li>
                    </ul>
                    <!-- END Dropdown menu-->
                </li>*@
            <!-- END Alert menu-->
            <!-- START Contacts button-->
            @*<li>
                    <a href="#" data-toggle-state="offsidebar-open" data-no-persist="true">
                        <em class="icon-notebook"></em>
                    </a>
                </li>*@
            <!-- END Contacts menu-->
        </ul>
        <!-- END Right Navbar-->
        <!-- START Left navbar-->
        @*<ul class="nav navbar-nav">
                <li class="@Html.isActive(controller: "Menu")">
                    <a href="#dashboard" data-toggle="dropdown"><em class="icon-cup"></em> Menu</a>
                    <ul class="dropdown-menu animated fadeIn">
                        @foreach (var item in Model)
                        {
                            <li class="@Html.isActive(controller: "Categoria")">
                                <a href="@Url.Action(item.DESCRICAO, "Produto")" title="@item.DESCRICAO">
                                    <em class="fa fa-cutlery"></em>
                                    <span>@item.DESCRICAO</span>
                                </a>
                            </li>
                        }
                    </ul>
                </li>
            </ul>*@
        <!-- END Left navbar-->
    </div>
    <!-- END Nav wrapper-->
    <!-- START Search form-->
    <form role="search" action="@Url.Action("Busca", "Produto")" class="navbar-form">
        <div class="form-group has-feedback">
            <input name="q" type="text" placeholder="@_TopNavbarHorizontalRes.Pedir" class="form-control" />
            <div data-search-dismiss="" class="fa fa-times form-control-feedback"></div>
        </div>
        <button type="submit" class="hidden btn btn-default">OK</button>
    </form>
    <!-- END Search form-->
</nav>
<!-- END Top Navbar-->