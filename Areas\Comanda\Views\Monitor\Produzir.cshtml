﻿@model List<RAL.Consumer.Data.Entities.ITENSPEDIDO>

@{
    /**/

    ViewBag.Title = "Monitor de " + ViewBag.Acao;
}

<span id="acao" class="hide">@ViewBag.Acao</span>

<nav class="navbar navbar-inverse navbar-fixed-bottom" id="barraStatusInferior">
    <div class="container-fluid">
        <div class="text-center text-white p-sm">
            @(Model.Sum(e => e.QUANTIDADE == 0 ? 1 : e.QUANTIDADE)) @Consumer.Resources.Mobile.Comanda.Views.Home.IndexRes.ProdutosPara @ViewBag.Acao
            @if (Model != null && Model.Count > 0)
            {
                <text>
                    <small> | <a class="text-warning" id="btnLimparTodos" data-codigocozinha="@Model.Select(i=> i.PRODUTODETALHE.PRODUTOS.CODIGOCOZINHA).FirstOrDefault()" data-acao="@ViewBag.Acao" href="@Url.Action("LimparTodos")">@Consumer.Resources.Mobile.Cardapio.Views.Home.IndexRes.LimparTodosDessaCozinha</a> | <a class="text-warning" href="#" data-toggle="popover" data-placement="top" data-trigger="hover click" title="Limpar tudo" data-content="No Consumer, acesse o menu APPS > Consumer Mobile > Configurar > Avançado">@Consumer.Resources.Mobile.Cardapio.Views.Home.IndexRes.LimparTudoEmTodasAsCozinhas</a></small>
                    <br>@Consumer.Resources.Mobile.Cardapio.Views.Produto.IndexRes.ExibindoItensPendentesDasUltimas @ViewBag.LimiteHoras @Consumer.Resources.Mobile.Cardapio.Views.Home.IndexRes.Horas <a class="text-warning" href="#" data-toggle="popover" data-placement="top" data-trigger="hover click" title="Alterar quantidade de horas" data-content="No Consumer, acesse o menu APPS > Consumer Mobile > Configurar > Avançado"><span class="fa fa-info-circle"></span></a>
                </text>
            }
        </div>
    </div>
</nav>

@if (Model != null && Model.Count > 0)
{
    <input type="hidden" id="maxItemPedido" name="maxItemPedido" value="@ViewBag.MaxItemPedido">

    <div class="portlets-wrapper" id="cardsArea">
        <div class="row">
            @{int ordem = 1; int idMesaAtual = 0; string bgClassMesa = "bg-gray-lighter";}

            @foreach (var itemPedidos in Model)
            {
                if (idMesaAtual != itemPedidos.PEDIDOS.NUMERO)
                {
                    if (bgClassMesa == "bg-gray-lighter")
                    {
                        bgClassMesa = "bg-gray";
                    }
                    else
                    {
                        bgClassMesa = "bg-gray-lighter";
                    }

                    idMesaAtual = itemPedidos.PEDIDOS.NUMERO.GetValueOrDefault();
                }

                <div id="item@(itemPedidos.CODIGO)" data-iditempedido="@itemPedidos.CODIGO" data-toggle="portlet" class="col-xl-2 col-lg-3 col-md-4 col-sm-6 itempedidoproducao">
                    <div class="panel panel-default" style="border-color:#808080">
                        <div class="panel-heading">
                            <div class="row">
                                <div class="col-xs-12">
                                    <h4>
                                        <span class="label label-info pull-right">Ped @itemPedidos.PEDIDOS.CODIGO</span>
                                        <label class="label label-primary">@(ordem++)º</label>
                                        @if (itemPedidos.PEDIDOS.Tipo == "Mesa/Comanda")
                                        {
                                            @ViewBag.MesaOuComanda
                                        }
                                        else
                                        {
                                            @itemPedidos.PEDIDOS.Tipo
                                        }
                                        @if (itemPedidos.PEDIDOS.NUMERO > 0)
                                        {
                                            @itemPedidos.PEDIDOS.NUMERO
                                        }
                                    </h4>
                                </div>
                            </div>

                            @if (itemPedidos.PEDIDOS.PEDIDOGRUPOENTREGA != null)
                            {
                                <div class="row">
                                    <div class="col-xs-12">
                                        <strong>Grupo de Entrega: #@itemPedidos.PEDIDOS.PEDIDOGRUPOENTREGA.CODIGOGRUPOENTREGA</strong>
                                    </div>
                                </div>
                            }

                            <div class="row">
                                <div class="col-xs-12">
                                    @if (ViewBag.ExibePedidoSenha == true)
                                    {
                                        <span class="label label-primary pull-right"> Senha: @itemPedidos.PEDIDOS.SENHA</span>
                                    }
                                    @if (!string.IsNullOrWhiteSpace(itemPedidos.PEDIDOS.NOME))
                                    {
                                        <small>@itemPedidos.PEDIDOS.NOME</small>
                                    }
                                    else
                                    {
                                        <span><small>&nbsp;</small></span>
                                    }
                                    @if (itemPedidos.PEDIDOS.DELIVERY != null)
                                    {
                                        if (itemPedidos.PEDIDOS.DELIVERY.TIPOENTREGA.CODIGO != 1)
                                        {
                                            <span><small>*** Cliente irá @itemPedidos.PEDIDOS.DELIVERY.TIPOENTREGA.DESCRICAO ***</small></span>
                                        }

                                        if (!string.IsNullOrWhiteSpace(itemPedidos.PEDIDOS.DELIVERY.OBSERVACAO))
                                        {
                                            <span><small>@itemPedidos.PEDIDOS.DELIVERY.OBSERVACAO</small></span>
                                        }
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="panel-wrapper">
                            @*<div class="panel-body @bgClassMesa" style="min-height: 109px; cursor: pointer" data-load="@Url.Action("ModoPreparo", new { id = itemPedidos.CODIGO })">*@
                            <div class="panel-body panel-body-item @bgClassMesa" style="min-height: 116px; cursor: pointer">
                                <div class="row">
                                    <div class="col-xs-12 modo-preparo" style="color:black" data-load="@Url.Action("ModoPreparo", new { id = itemPedidos.CODIGO })">
                                        <span style="background-color:#fff;padding: 0 4px"><strong>@(itemPedidos.QUANTIDADE > 0 ? itemPedidos.QUANTIDADE : 1)</strong></span> @itemPedidos.NOMEPRODUTO
                                    </div>

                                    <p class="small" style="line-height: 1">
                                        @if (!string.IsNullOrEmpty(itemPedidos.DETALHES))
                                        {
                                            <div class="col-xs-12">
                                                <i>*@itemPedidos.DETALHES</i><br />
                                            </div>
                                        }

                                        @foreach (var itemComplemento in itemPedidos.ITENSPEDIDOFILHOS.Where(i => !i.DATADELETE.HasValue && i.PRODUTODETALHE.PRODUTOS.CODIGOPRODUTOTIPO == 3))
                                        {
                                            string qtd = itemComplemento.QUANTIDADE == 0 ? "1" : itemComplemento.QUANTIDADE.ToString();
                                            decimal qtdOriginal = itemComplemento.QUANTIDADE.GetValueOrDefault();

                                            if (itemComplemento.ITEMPEDIDOPAI.QUANTIDADE > 0)
                                            {
                                                qtdOriginal = Convert.ToInt32(qtdOriginal / itemComplemento.ITEMPEDIDOPAI.QUANTIDADE);
                                            }

                                            if (itemComplemento.ITEMPEDIDOPAI.CODIGOITEMPEDIDOTIPO != 7)
                                            {
                                                if (qtdOriginal != 1)
                                                {
                                                    qtd = "+" + qtd;
                                                }
                                                else // Se quantidade Original é igual a 1, não exibe para não confundir a multiplicidade
                                                {
                                                    qtd = "+";
                                                }
                                            }
                                            else
                                            {
                                                if (qtdOriginal > 1)
                                                {
                                                    qtd = "+" + "1/" + itemComplemento.ITEMPEDIDOPAI.ITEMPEDIDOPAI.ITENSPEDIDOFILHOS.Count(s => !s.DATADELETE.HasValue && s.CODIGOITEMPEDIDOTIPO == 7).ToString() + " x" + qtdOriginal;
                                                }
                                                else
                                                {
                                                    qtd = "+" + "1/" + itemComplemento.ITEMPEDIDOPAI.ITEMPEDIDOPAI.ITENSPEDIDOFILHOS.Count(s => !s.DATADELETE.HasValue && s.CODIGOITEMPEDIDOTIPO == 7).ToString();
                                                }
                                            }

                                            <div class="col-xs-12 pl-lg">
                                                <span>@qtd @itemComplemento.NOMEPRODUTO </span>
                                            </div>
                                        }

                                        @foreach (var itemFilho in itemPedidos.ITENSPEDIDOFILHOS.Where(i => !i.DATADELETE.HasValue && i.QUANTIDADE > 0 && i.PRODUTODETALHE.PRODUTOS.CODIGOPRODUTOTIPO != 3 && (!i.PRODUTODETALHE.PRODUTOS.CODIGOCOZINHA.HasValue || i.PRODUTODETALHE.PRODUTOS.CODIGOCOZINHA == i.ITEMPEDIDOPAI.PRODUTODETALHE.PRODUTOS.CODIGOCOZINHA)))
                                        {
                                            <div class="col-xs-12 pl-lg modo-preparo" data-load="@Url.Action("ModoPreparo", new { id = itemFilho.CODIGO })">
                                                <b><span style="background-color:#fff;padding: 0 4px">@((itemFilho.QUANTIDADE / (itemPedidos.QUANTIDADE > 0 ? itemPedidos.QUANTIDADE : 1)).GetValueOrDefault().ToString("0.##"))</span> @itemFilho.NomeComDetalheQuebraLinha.Replace("<br />", " ")</b>


                                                @foreach (var itemComplemento in itemFilho.ITENSPEDIDOFILHOS.Where(i => !i.DATADELETE.HasValue && i.PRODUTODETALHE.PRODUTOS.CODIGOPRODUTOTIPO == 3))
                                                {

                                                    string qtd = itemComplemento.QUANTIDADE == 0 ? "1" : itemComplemento.QUANTIDADE.ToString();
                                                    decimal qtdOriginal = itemComplemento.QUANTIDADE.GetValueOrDefault();

                                                    if (itemComplemento.ITEMPEDIDOPAI.QUANTIDADE > 0)
                                                    {
                                                        qtdOriginal = Convert.ToInt32(qtdOriginal / itemComplemento.ITEMPEDIDOPAI.QUANTIDADE);
                                                    }

                                                    if (itemComplemento.ITEMPEDIDOPAI.CODIGOITEMPEDIDOTIPO != 7)
                                                    {
                                                        if (qtdOriginal != 1)
                                                        {
                                                            qtd = "+" + qtd;
                                                        }
                                                        else // Se quantidade Original é igual a 1, não exibe para não confundir a multiplicidade
                                                        {
                                                            qtd = "+";
                                                        }
                                                    }
                                                    else
                                                    {
                                                        if (qtdOriginal > 1)
                                                        {
                                                            qtd = "+" + "1/" + itemComplemento.ITEMPEDIDOPAI.ITEMPEDIDOPAI.ITENSPEDIDOFILHOS.Count(s => !s.DATADELETE.HasValue && s.CODIGOITEMPEDIDOTIPO == 7).ToString() + " x" + qtdOriginal;
                                                        }
                                                        else
                                                        {
                                                            qtd = "+" + "1/" + itemComplemento.ITEMPEDIDOPAI.ITEMPEDIDOPAI.ITENSPEDIDOFILHOS.Count(s => !s.DATADELETE.HasValue && s.CODIGOITEMPEDIDOTIPO == 7).ToString();
                                                        }
                                                    }

                                                    <span>@qtd @itemComplemento.NOMEPRODUTO </span>
                                                }
                                            </div>
                                        }
                                    </p>
                                </div>
                            </div>
                            <div class="panel-footer">
                                <div class="row">
                                    <div class="col-xs-6">
                                        @{
                                            string NomeExibir = "Não Definido";
                                            if (itemPedidos.CONTATOS != null)
                                            {
                                                NomeExibir = itemPedidos.CONTATOS.NOME; //se existe um coloaborador atribuído ao item, será exibido o nome dele
                                            }
                                            else
                                            {
                                                if (itemPedidos.PEDIDOS.CONTATOSCOLABORADOR != null)
                                                {
                                                    NomeExibir = itemPedidos.PEDIDOS.CONTATOSCOLABORADOR.NOME;  // senão exibe o colaborador atribuído ao pedido
                                                }
                                            }
                                        }
                                        <p class="text-nowrap" style="overflow: hidden; text-overflow: ellipsis"><i class="fa fa-user" /> @NomeExibir</p>
                                    </div>
                                    <div class="col-xs-6 text-right">
                                        <p class="text-nowrap" style="overflow: hidden; text-overflow: ellipsis"><i class="fa fa-clock-o" /> @(((TimeSpan)(DateTime.Now - itemPedidos.DATAHORACADASTRO)).ToString("h'h 'm'm 's's'"))</p>
                                    </div>
                                </div>
                            </div>
                            <div class="panel-footer">
                                <div class="row">
                                    @if (ViewBag.Acao == "Produção")
                                    {
                                        <div class="col-xs-6">
                                            <a class="btn btn-purple btn-block navbar-btn btn-xl btn-pronto" title="Pronto" href="@Url.Action("MarcarProduzido")" data-codigoitempedido="@itemPedidos.CODIGO" data-codigocozinha="@itemPedidos.PRODUTODETALHE.PRODUTOS.CODIGOCOZINHA" data-nome="@itemPedidos.NOMEPRODUTO" data-codigopedido="@itemPedidos.PEDIDOS.CODIGO"><strong>@Consumer.Resources.Mobile.Comanda.Views.Home.AddPagamentoRes.Pronto</strong></a>
                                        </div>
                                        <div class="col-xs-6">
                                            <a class="btn btn-success btn-block navbar-btn btn-xl btn-pronto-entregue" title="Pronto e Entregue" href="@Url.Action("MarcarProduzidoEntregue")" data-codigoitempedido="@itemPedidos.CODIGO" data-codigocozinha="@itemPedidos.PRODUTODETALHE.PRODUTOS.CODIGOCOZINHA" data-nome="@itemPedidos.NOMEPRODUTO" data-codigopedido="@itemPedidos.PEDIDOS.CODIGO"><strong>@Consumer.Resources.Mobile.Comanda.Views.Home.AddPagamentoRes.Entregue</strong></a>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="col-xs-12">
                                            <a class="btn btn-success btn-block navbar-btn btn-xl btn-entregue" title="Entregue" href="@Url.Action("MarcarEntregue")" data-codigoitempedido="@itemPedidos.CODIGO" data-codigocozinha="@itemPedidos.PRODUTODETALHE.PRODUTOS.CODIGOCOZINHA" data-nome="@itemPedidos.NOMEPRODUTO" data-codigopedido="@itemPedidos.PEDIDOS.CODIGO"><strong>@Consumer.Resources.Mobile.SmartDelivery.Views.IndexRes.Entregue</strong></a>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
}
else
{
    <div class="well-lg alert alert-success">
        @Consumer.Resources.Mobile.Comanda.Views.Monitor.ProduzirPorPedidoRes.TodosPedidosAtendidos
    </div>
}

<script>
    $(function () {
        $('[data-toggle="popover"]').popover()
    })
</script>

<style>
    .popover {
        color: #000
    }
</style>