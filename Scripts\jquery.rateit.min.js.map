{"version": 3, "sources": ["jquery.rateit.js"], "names": ["$", "touchHandler", "event", "touches", "originalEvent", "changedTouches", "first", "type", "simulatedEvent", "document", "createEvent", "initMouseEvent", "window", "screenX", "screenY", "clientX", "clientY", "target", "dispatchEvent", "preventDefault", "rateit", "aria", "reset<PERSON><PERSON><PERSON>", "ratingLabel", "fn", "p1", "p2", "index", "options", "mode", "capitaliseFirstLetter", "string", "char<PERSON>t", "toUpperCase", "substr", "this", "length", "tp1", "undefined", "extend", "defaults", "data", "each", "item", "itemdata", "key", "value", "ariakey", "range", "find", "attr", "arguments", "apply", "setup", "prop", "fld", "nodeName", "getAttribute", "val", "trigger", "min", "max", "step", "hasClass", "addClass", "ltr", "css", "unbind", "Math", "icon", "isNaN", "readonly", "resetable", "backingfld", "starwidth", "starheight", "ispreset", "hide", "parseInt", "Number", "selectedOption", "element", "html", "append", "replace", "removeClass", "JSON", "parse", "stringify", "isfont", "height", "stars", "txt", "i", "text", "width", "presetclass", "score", "resetbtn", "bind", "e", "blur", "Event", "isDefaultPrevented", "calcRawScore", "pageX", "offsetx", "offset", "left", "ceil", "setHover", "w", "h", "show", "setSelection", "mousemove", "mouseleave", "mouseup", "keyup", "which", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;CAGA,SAAWA,GA6XP,QAASC,GAAaC,GAElB,GAAIC,GAAUD,EAAME,cAAcC,eAC1BC,EAAQH,EAAQ,GAChBI,EAAO,EACf,QAAQL,EAAMK,MACV,IAAK,YAAaA,EAAO,WAAa,MACtC,KAAK,WAAYA,EAAO,SAAW,MACnC,SAAS,OAGb,GAAIC,GAAiBC,SAASC,YAAY,aAC1CF,GAAeG,eAAeJ,GAAM,GAAM,EAAMK,OAAQ,EAClCN,EAAMO,QAASP,EAAMQ,QACrBR,EAAMS,QAAST,EAAMU,SAAS,GAC9B,GAAO,GAAO,EAAO,EAAW,MAEtDV,EAAMW,OAAOC,cAAcV,GAC3BN,EAAMiB,iBA9YVnB,EAAEoB,QACEC,MACIC,WAAY,eACZC,YAAa,WAIrBvB,EAAEwB,GAAGJ,OAAS,SAAUK,EAAIC,GAExB,GAAIC,GAAQ,EACRC,KAAkBC,EAAO,OACzBC,EAAwB,SAAUC,GAClC,MAAOA,GAAOC,OAAO,GAAGC,cAAgBF,EAAOG,OAAO,GAG1D,IAAoB,IAAhBC,KAAKC,OAAgB,MAAOD,KAGhC,IAAIE,GAAMrC,EAAEO,KAAKkB,EACjB,IAAW,UAAPY,GAA0BC,SAAPb,GAA2B,OAAPA,EACvCG,EAAU5B,EAAEuC,UAAWvC,EAAEwB,GAAGJ,OAAOoB,SAAUf,OAE5C,CAAA,GAAW,UAAPY,GAA0B,UAAPZ,GAAyBa,SAAPZ,EAC1C,MAAOS,MAAKM,KAAK,SAAWX,EAAsBL,GAEtC,WAAPY,IACLR,EAAO,YAGX,MAAOM,MAAKO,KAAK,WACb,GAAIC,GAAO3C,EAAEmC,MAITS,EAAW,SAAUC,EAAKC,GAE1B,GAAa,MAATA,EAAe,CAEf,GAAIC,GAAU,cAAwB,SAAPF,EAAkB,MAAQA,GACrDG,EAAQL,EAAKM,KAAK,gBACKX,SAAvBU,EAAME,KAAKH,IACXC,EAAME,KAAKH,EAASD,GAM5B,MADAK,WAAU,GAAK,SAAWrB,EAAsBe,GACzCF,EAAKF,KAAKW,MAAMT,EAAMQ,WAIjC,IAAU,SAAN1B,EAAe,CACf,GAAI4B,GAAQT,EAAS,OACrB,KAAK,GAAIU,KAAQD,GACbV,EAAKF,KAAKa,EAAMD,EAAMC,GAG1B,IAAIV,EAAS,cAAe,CACxB,GAAIW,GAAMvD,EAAE4C,EAAS,cAEE,WAAnBW,EAAI,GAAGC,UAAwE,UAAhDD,EAAI,GAAGE,aAAa,wBACnDF,EAAID,KAAK,gBAAiBV,EAAS,UAGnCW,EAAIG,IAAId,EAAS,UAErBW,EAAII,QAAQ,UACRJ,EAAI,GAAGK,MAAOL,EAAI,GAAGK,IAAMhB,EAAS,QACpCW,EAAI,GAAGM,MAAON,EAAI,GAAGM,IAAMjB,EAAS,QACpCW,EAAI,GAAGO,OAAQP,EAAI,GAAGO,KAAOlB,EAAS,SAE9CD,EAAKgB,QAAQ,SAIZhB,EAAKoB,SAAS,WAAapB,EAAKqB,SAAS,SAE9C,IAAIC,GAA+B,OAAzBtB,EAAKuB,IAAI,YAGnB,IAAY,YAARrC,EAAoB,CACpB,IAAKe,EAAS,QAAW,KAAM,6BAY/B,IARU,YAANnB,GAA0B,GAANC,GAAekB,EAAS,cAC5CD,EAAKM,KAAK,iBAAiBkB,SAC3BvB,EAAS,SAAS,IAGZ,SAANnB,IACAC,EAAY,MAANA,EAAckB,EAAS,OAASwB,KAAKP,IAAIjB,EAAS,OAAQwB,KAAKR,IAAIhB,EAAS,OAAQlB,KAE1FkB,EAAS,cAAe,CAIxB,GAAIW,GAAMvD,EAAE4C,EAAS,cAEE,WAAnBW,EAAI,GAAGC,UAAwE,UAAhDD,EAAI,GAAGE,aAAa,wBACzC,SAANhC,GAAiB8B,EAAID,KAAK,gBAAiB5B,GAGrC,SAAND,GAAiB8B,EAAIG,IAAIhC,GAEvB,OAAND,GAAe8B,EAAI,GAAGK,MAAOL,EAAI,GAAGK,IAAMlC,GACpC,OAAND,GAAe8B,EAAI,GAAGM,MAAON,EAAI,GAAGM,IAAMnC,GACpC,QAAND,GAAgB8B,EAAI,GAAGO,OAAQP,EAAI,GAAGO,KAAOpC,GAGrDkB,EAASnB,EAAIC,GAKjB,IAAKkB,EAAS,QAAS,CAiBnB,GAdAA,EAAS,OAAQA,EAAS,SAAWhB,EAAQC,MAC7Ce,EAAS,OAAQA,EAAS,SAAWhB,EAAQyC,MAC7CzB,EAAS,MAAO0B,MAAM1B,EAAS,QAAUhB,EAAQgC,IAAMhB,EAAS,QAChEA,EAAS,MAAO0B,MAAM1B,EAAS,QAAUhB,EAAQiC,IAAMjB,EAAS,QAChEA,EAAS,OAAQA,EAAS,SAAWhB,EAAQkC,MAC7ClB,EAAS,WAAqCN,SAAzBM,EAAS,YAA4BA,EAAS,YAAchB,EAAQ2C,UACzF3B,EAAS,YAAuCN,SAA1BM,EAAS,aAA6BA,EAAS,aAAehB,EAAQ4C,WAC5F5B,EAAS,aAAcA,EAAS,eAAiBhB,EAAQ6C,YACzD7B,EAAS,YAAaA,EAAS,cAAgBhB,EAAQ8C,WACvD9B,EAAS,aAAcA,EAAS,eAAiBhB,EAAQ+C,YACzD/B,EAAS,QAASwB,KAAKP,IAAIjB,EAAS,OAAQwB,KAAKR,IAAIhB,EAAS,OAAU0B,MAAM1B,EAAS,UAAkC0B,MAAM1C,EAAQkB,OAAyBlB,EAAQgC,IAAxBhC,EAAQkB,MAArDF,EAAS,YAC5GA,EAAS,WAAqCN,SAAzBM,EAAS,YAA4BA,EAAS,YAAchB,EAAQgD,UAGrFhC,EAAS,cAAe,CAExB,GAAIW,GAAMvD,EAAE4C,EAAS,eAAeiC,MAcpC,KAZItB,EAAIL,KAAK,aAAeK,EAAIL,KAAK,cACjCN,EAAS,YAAY,GAGF,SAAnBW,EAAI,GAAGC,WACY,SAAfD,EAAI,GAAGhD,MAAkC,QAAfgD,EAAI,GAAGhD,OAEjCqC,EAAS,MAAOkC,SAASvB,EAAIL,KAAK,SAAWN,EAAS,QACtDA,EAAS,MAAOkC,SAASvB,EAAIL,KAAK,SAAWN,EAAS,QACtDA,EAAS,OAAQkC,SAASvB,EAAIL,KAAK,UAAYN,EAAS,WAGzC,UAAnBW,EAAI,GAAGC,UAAwBD,EAAI,GAAG3B,QAAQQ,OAAS,EAAG,CAEN,UAAhDmB,EAAI,GAAGE,aAAa,yBACpBb,EAAS,MAAS0B,MAAM1B,EAAS,QAA4BmC,OAAOxB,EAAI,GAAG3B,QAAQ,GAAGD,OAA3CiB,EAAS,QACpDA,EAAS,MAAOmC,OAAOxB,EAAI,GAAG3B,QAAQ2B,EAAI,GAAGnB,OAAS,GAAGT,QACzDiB,EAAS,OAAQmC,OAAOxB,EAAI,GAAG3B,QAAQ,GAAGD,OAASoD,OAAOxB,EAAI,GAAG3B,QAAQ,GAAGD,UAG5EiB,EAAS,MAAS0B,MAAM1B,EAAS,QAA4BmC,OAAOxB,EAAI,GAAG3B,QAAQ,GAAGkB,OAA3CF,EAAS,QACpDA,EAAS,MAAOmC,OAAOxB,EAAI,GAAG3B,QAAQ2B,EAAI,GAAGnB,OAAS,GAAGU,QACzDF,EAAS,OAAQmC,OAAOxB,EAAI,GAAG3B,QAAQ,GAAGkB,OAASiC,OAAOxB,EAAI,GAAG3B,QAAQ,GAAGkB,QAGhF,IAAIkC,GAAiBzB,EAAIN,KAAK,mBACD,IAAzB+B,EAAe5C,SAEqC,UAAhDmB,EAAI,GAAGE,aAAa,wBACpBb,EAAS,QAASoC,EAAe,GAAGrD,OAGpCiB,EAAS,QAASoC,EAAetB,YAOzCd,GAAS,QAASW,EAAIG,OAS9B,GAAIuB,GAA8B,OAApBtC,EAAK,GAAGa,SAAoB,MAAQ,MAClD7B,IAEA,IAAIuD,GAAO,uGAAyGlF,EAAEoB,OAAOC,KAAKC,WAAa,uKAAyKtB,EAAEoB,OAAOC,KAAKE,YAAc,uDAAyDqB,EAAS,OAAS,oBAAsBA,EAAS,OAAS,oBAAsBA,EAAS,SAAW,qKACjfD,GAAKwC,OAAOD,EAAKE,QAAQ,cAAezD,GAAOyD,QAAQ,gBAAiBH,IAGnEhB,IACDtB,EAAKM,KAAK,iBAAiBiB,IAAI,QAAS,SACxCvB,EAAKM,KAAK,oBAAoBe,SAAS,uBACvCrB,EAAKM,KAAK,iBAAiBe,SAAS,qBAGhB,QAApBpB,EAAS,QACTD,EAAKqB,SAAS,eAAeqB,YAAY,aAGzC1C,EAAKqB,SAAS,aAAaqB,YAAY,eAG3CzC,EAAS,OAAQ0C,KAAKC,MAAMD,KAAKE,UAAU7C,EAAKF,UAGpD,GAAIgD,GAA6B,QAApB7C,EAAS,OAMjB6C,IACD9C,EAAKM,KAAK,mCAAmCyC,OAAO9C,EAAS,cAIjE,IAAII,GAAQL,EAAKM,KAAK,gBACtB,IAAIwC,EAAQ,CAMR,IAAI,GAJApB,GAAOzB,EAAS,QAChB+C,EAAQ/C,EAAS,OAASA,EAAS,OAEnCgD,EAAM,GACFC,EAAI,EAAGA,EAAGF,EAAOE,IACrBD,GAAOvB,CAGXrB,GAAMC,KAAK,OAAO6C,KAAKF,GAGvBhD,EAAS,YAAaI,EAAM+C,SAAWnD,EAAS,OAASA,EAAS,aAIlEI,GAAM+C,MAAMnD,EAAS,cAAgBA,EAAS,OAASA,EAAS,SAAS8C,OAAO9C,EAAS,cAK7F,IAAIoD,GAAc,iBAAmB,EAAQ,GAAK,OASlD,IARIpD,EAAS,YACTD,EAAKM,KAAK,oBAAoBe,SAASgC,GAGvCrD,EAAKM,KAAK,oBAAoBoC,YAAYW,GAIrB,MAArBpD,EAAS,SAAkB,CAC3B,GAAIqD,IAASrD,EAAS,SAAWA,EAAS,QAAUA,EAAS,YAC7DD,GAAKM,KAAK,oBAAoB8C,MAAME,GAIxC,GAAIC,GAAWvD,EAAKM,KAAK,gBACrBiD,GAASzD,KAAK,YAAa,GAC3ByD,EAASC,KAAK,QAAS,SAAUC,GAC7BA,EAAEjF,iBAEF+E,EAASG,MAET,IAAInG,GAAQF,EAAEsG,MAAM,cAEpB,OADA3D,GAAKgB,QAAQzD,IACTA,EAAMqG,uBAIV5D,EAAKvB,OAAO,QAAS,UACrBuB,GAAKgB,QAAQ,YACdlB,KAAK,SAAS,EAKrB,IAAI+D,GAAe,SAAUvB,EAAS/E,GAClC,GAAIuG,GAASvG,EAAoB,eAAIA,EAAMG,eAAe,GAAGoG,MAAQvG,EAAMuG,MAEvEC,EAAUD,EAAQzG,EAAEiF,GAAS0B,SAASC,IAK1C,OAJK3C,KAAOyC,EAAU1D,EAAM+C,QAAUW,GAClCA,EAAU1D,EAAM+C,UAAWW,EAAU1D,EAAM+C,SAC3CW,EAAU,IAAKA,EAAU,GAEtBT,EAAQ7B,KAAKyC,KAAKH,EAAU9D,EAAS,cAAgB,EAAIA,EAAS,WAIzEkE,EAAW,SAAUb,GACrB,GAAIc,GAAId,EAAQrD,EAAS,aAAeA,EAAS,QAC7CoE,EAAIhE,EAAMC,KAAK,gBACnB,IAAI+D,EAAEvE,KAAK,UAAYsE,EAAG,CACtB/D,EAAMC,KAAK,oBAAoB4B,OAC/BmC,EAAEjB,MAAMgB,GAAGE,OAAOxE,KAAK,QAASsE,EAChC,IAAItE,IAASwD,EAAQrD,EAAS,QAAWA,EAAS,OAClDD,GAAKgB,QAAQ,QAASlB,GAAMkB,QAAQ,OAAQlB,KAIhDyE,EAAe,SAAUpE,GACzB,GAAI5C,GAAQF,EAAEsG,MAAM,cAEpB,OADA3D,GAAKgB,QAAQzD,GAAQ4C,KACjB5C,EAAMqG,uBAIV3D,EAAS,QAASE,GACdF,EAAS,gBAEc,UAAnBW,EAAI,GAAGC,UAAwE,UAAhDD,EAAI,GAAGE,aAAa,wBACnDzD,EAAE4C,EAAS,eAAeU,KAAK,gBAAiBR,GAAOa,QAAQ,UAG/D3D,EAAE4C,EAAS,eAAec,IAAIZ,GAAOa,QAAQ,WAGjDf,EAAS,cACTI,EAAMC,KAAK,oBAAoBoC,YAAYW,GAC3CpD,EAAS,YAAY,IAEzBI,EAAMC,KAAK,iBAAiB4B,OAC5B7B,EAAMC,KAAK,oBAAoB8C,MAAMjD,EAAQF,EAAS,aAAgBA,EAAS,OAASA,EAAS,cAAeqE,OAChHtE,EAAKgB,QAAQ,SAAU,OAAOA,QAAQ,QAAS,OAAOA,QAAQ,SAAUb,KACjE,GAGNF,GAAS,YA8CVsD,EAASrB,QA1CJjC,EAAS,cACVsD,EAASrB,OAIRjC,EAAS,WACVI,EAAMmD,KAAK,qBAAsBlG,GACjC+C,EAAMmE,UAAU,SAAUf,GACtB,GAAIH,GAAQO,EAAarE,KAAMiE,EAC/BU,GAASb,KAGbjD,EAAMoE,WAAW,SAAUhB,GACvBpD,EAAMC,KAAK,iBAAiB4B,OAAOkB,MAAM,GAAGtD,KAAK,QAAS,IAC1DE,EAAKgB,QAAQ,SAAU,OAAOA,QAAQ,QAAS,OAC/CX,EAAMC,KAAK,oBAAoBgE,SAGnCjE,EAAMqE,QAAQ,SAAUjB,GACpB,GAAIH,GAAQO,EAAarE,KAAMiE,GAC3BtD,EAASmD,EAAQrD,EAAS,QAAWA,EAAS,MAClDsE,GAAapE,GACbE,EAAMqD,SAIVrD,EAAMsE,MAAM,SAAUlB,GACH,IAAXA,EAAEmB,OAAenB,EAAEmB,QAAUtD,EAAM,GAAK,KACxCiD,EAAa9C,KAAKR,IAAIhB,EAAS,SAAWA,EAAS,QAASA,EAAS,SAE1D,IAAXwD,EAAEmB,OAAenB,EAAEmB,QAAUtD,EAAM,GAAK,KACxCiD,EAAa9C,KAAKP,IAAIjB,EAAS,SAAWA,EAAS,QAASA,EAAS,WAI7EA,EAAS,SAAS,IAElBA,EAAS,cACTsD,EAASe,QAOjBjE,EAAME,KAAK,gBAAiBN,EAAS,gBA2B7C5C,EAAEwB,GAAGJ,OAAOoB,UAAaoB,IAAK,EAAGC,IAAK,EAAGC,KAAM,GAAKjC,KAAM,KAAMwC,KAAM,IAAKK,UAAW,GAAIC,WAAY,GAAIJ,UAAU,EAAOC,WAAW,EAAMI,UAAU,GAGtJ5E,EAAE,WAAcA,EAAE,2BAA2BoB,YAE9CoG", "file": "jquery.rateit.min.js", "sourcesContent": ["/*! RateIt | v1.1.0 / 10/20/2016\r\n    https://github.com/gjunge/rateit.js | Twitter: @gjunge\r\n*/\r\n(function ($) {\r\n    $.rateit = {\r\n        aria: {\r\n            resetLabel: 'reset rating',\r\n            ratingLabel: 'rating'\r\n        }\r\n    };\r\n\r\n    $.fn.rateit = function (p1, p2) {\r\n        //quick way out.\r\n        var index = 1;\r\n        var options = {}; var mode = 'init';\r\n        var capitaliseFirstLetter = function (string) {\r\n            return string.charAt(0).toUpperCase() + string.substr(1);\r\n        };\r\n\r\n        if (this.length === 0) { return this; }\r\n\r\n\r\n        var tp1 = $.type(p1);\r\n        if (tp1 == 'object' || p1 === undefined || p1 === null) {\r\n            options = $.extend({}, $.fn.rateit.defaults, p1); //wants to init new rateit plugin(s).\r\n        }\r\n        else if (tp1 == 'string' && p1 !== 'reset' && p2 === undefined) {\r\n            return this.data('rateit' + capitaliseFirstLetter(p1)); //wants to get a value.\r\n        }\r\n        else if (tp1 == 'string') {\r\n            mode = 'setvalue';\r\n        }\r\n\r\n        return this.each(function () {\r\n            var item = $(this);\r\n\r\n\r\n            //shorten all the item.data('rateit-XXX'), will save space in closure compiler, will be like item.data('XXX') will become x('XXX')\r\n            var itemdata = function (key, value) {\r\n\r\n                if (value != null) {\r\n                    //update aria values\r\n                    var ariakey = 'aria-value' + ((key == 'value') ? 'now' : key);\r\n                    var range = item.find('.rateit-range');\r\n                    if (range.attr(ariakey) != undefined) {\r\n                        range.attr(ariakey, value);\r\n                    }\r\n\r\n                }\r\n\r\n                arguments[0] = 'rateit' + capitaliseFirstLetter(key);\r\n                return item.data.apply(item, arguments); ////Fix for WI: 523\r\n            };\r\n\r\n            //handle programmatic reset\r\n            if (p1 == 'reset') {\r\n                var setup = itemdata('init'); //get initial value\r\n                for (var prop in setup) {\r\n                    item.data(prop, setup[prop]);\r\n                }\r\n\r\n                if (itemdata('backingfld')) { //reset also backingfield\r\n                    var fld = $(itemdata('backingfld'));\r\n                    // If backing field is a select box with valuesrc option set to \"index\", reset its selectedIndex property; otherwise, reset its value.\r\n                    if (fld[0].nodeName == 'SELECT' && fld[0].getAttribute('data-rateit-valuesrc') === 'index') {\r\n                        fld.prop('selectedIndex', itemdata('value'));\r\n                    }\r\n                    else {\r\n                        fld.val(itemdata('value'));\r\n                    }\r\n                    fld.trigger('change');\r\n                    if (fld[0].min) { fld[0].min = itemdata('min'); }\r\n                    if (fld[0].max) { fld[0].max = itemdata('max'); }\r\n                    if (fld[0].step) { fld[0].step = itemdata('step'); }\r\n                }\r\n                item.trigger('reset');\r\n            }\r\n\r\n            //add the rate it class.\r\n            if (!item.hasClass('rateit')) { item.addClass('rateit'); }\r\n\r\n            var ltr = item.css('direction') != 'rtl';\r\n\r\n            // set value mode\r\n            if (mode == 'setvalue') {\r\n                if (!itemdata('init')) { throw 'Can\\'t set value before init'; }\r\n\r\n\r\n                //if readonly now and it wasn't readonly, remove the eventhandlers.\r\n                if (p1 == 'readonly' && p2 == true && !itemdata('readonly')) {\r\n                    item.find('.rateit-range').unbind();\r\n                    itemdata('wired', false);\r\n                }\r\n                //when we receive a null value, reset the score to its min value.\r\n                if (p1 == 'value') {\r\n                    p2 = (p2 == null) ? itemdata('min') : Math.max(itemdata('min'), Math.min(itemdata('max'), p2));\r\n                }\r\n                if (itemdata('backingfld')) {\r\n                    //if we have a backing field, check which fields we should update. \r\n                    //In case of input[type=range], although we did read its attributes even in browsers that don't support it (using fld.attr())\r\n                    //we only update it in browser that support it (&& fld[0].min only works in supporting browsers), not only does it save us from checking if it is range input type, it also is unnecessary.\r\n                    var fld = $(itemdata('backingfld'));\r\n                    // If backing field is a select box with valuesrc option set to \"index\", update its selectedIndex property; otherwise, update its value.\r\n                    if (fld[0].nodeName == 'SELECT' && fld[0].getAttribute('data-rateit-valuesrc') === 'index') {\r\n                        if (p1 == 'value') { fld.prop('selectedIndex', p2); }\r\n                    }\r\n                    else {\r\n                        if (p1 == 'value') { fld.val(p2); }\r\n                    }\r\n                    if (p1 == 'min' && fld[0].min) { fld[0].min = p2; }\r\n                    if (p1 == 'max' && fld[0].max) { fld[0].max = p2;}\r\n                    if (p1 == 'step' && fld[0].step) { fld[0].step = p2; }\r\n                }\r\n\r\n                itemdata(p1, p2);\r\n            }\r\n\r\n\r\n            //init rateit plugin\r\n            if (!itemdata('init')) {\r\n\r\n                //get our values, either from the data-* html5 attribute or from the options.\r\n                itemdata('mode', itemdata('mode') || options.mode)\r\n                itemdata('icon', itemdata('icon') || options.icon)\r\n                itemdata('min', isNaN(itemdata('min')) ? options.min : itemdata('min'));\r\n                itemdata('max', isNaN(itemdata('max')) ? options.max : itemdata('max'));\r\n                itemdata('step', itemdata('step') || options.step);\r\n                itemdata('readonly', itemdata('readonly') !== undefined ? itemdata('readonly') : options.readonly);\r\n                itemdata('resetable', itemdata('resetable') !== undefined ? itemdata('resetable') : options.resetable);\r\n                itemdata('backingfld', itemdata('backingfld') || options.backingfld);\r\n                itemdata('starwidth', itemdata('starwidth') || options.starwidth);\r\n                itemdata('starheight', itemdata('starheight') || options.starheight);\r\n                itemdata('value', Math.max(itemdata('min'), Math.min(itemdata('max'), (!isNaN(itemdata('value')) ? itemdata('value') : (!isNaN(options.value) ? options.value : options.min)))));\r\n                itemdata('ispreset', itemdata('ispreset') !== undefined ? itemdata('ispreset') : options.ispreset);\r\n                //are we LTR or RTL?\r\n\r\n                if (itemdata('backingfld')) {\r\n                    //if we have a backing field, hide it, override defaults if range or select.\r\n                    var fld = $(itemdata('backingfld')).hide();\r\n\r\n                    if (fld.attr('disabled') || fld.attr('readonly')) {\r\n                        itemdata('readonly', true); //http://rateit.codeplex.com/discussions/362055 , if a backing field is disabled or readonly at instantiation, make rateit readonly.\r\n                    }\r\n\r\n                    if (fld[0].nodeName == 'INPUT') {\r\n                        if (fld[0].type == 'range' || fld[0].type == 'text') { //in browsers not support the range type, it defaults to text\r\n\r\n                            itemdata('min', parseInt(fld.attr('min')) || itemdata('min')); //if we would have done fld[0].min it wouldn't have worked in browsers not supporting the range type.\r\n                            itemdata('max', parseInt(fld.attr('max')) || itemdata('max'));\r\n                            itemdata('step', parseInt(fld.attr('step')) || itemdata('step'));\r\n                        }\r\n                    }\r\n                    if (fld[0].nodeName == 'SELECT' && fld[0].options.length > 1) {\r\n                        // If backing field is a select box with valuesrc option set to \"index\", use the indexes of its options; otherwise, use the values.\r\n                        if (fld[0].getAttribute('data-rateit-valuesrc') === 'index') {\r\n                            itemdata('min', (!isNaN(itemdata('min')) ? itemdata('min') : Number(fld[0].options[0].index)));\r\n                            itemdata('max', Number(fld[0].options[fld[0].length - 1].index));\r\n                            itemdata('step', Number(fld[0].options[1].index) - Number(fld[0].options[0].index));\r\n                        }\r\n                        else {\r\n                            itemdata('min', (!isNaN(itemdata('min')) ? itemdata('min') : Number(fld[0].options[0].value)));\r\n                            itemdata('max', Number(fld[0].options[fld[0].length - 1].value));\r\n                            itemdata('step', Number(fld[0].options[1].value) - Number(fld[0].options[0].value));\r\n                        }\r\n                        //see if we have a option that as explicity been selected\r\n                        var selectedOption = fld.find('option[selected]');\r\n                        if (selectedOption.length == 1) {\r\n                            // If backing field is a select box with valuesrc option set to \"index\", use the index of selected option; otherwise, use the value.\r\n                            if (fld[0].getAttribute('data-rateit-valuesrc') === 'index') {\r\n                                itemdata('value', selectedOption[0].index);\r\n                            }\r\n                            else {\r\n                                itemdata('value', selectedOption.val());\r\n                            }\r\n                        }\r\n                    }\r\n                    else {\r\n                        //if it is not a select box, we can get's it's value using the val function. \r\n                        //If it is a selectbox, we always get a value (the first one of the list), even if it was not explicity set.\r\n                        itemdata('value', fld.val());\r\n                    }\r\n\r\n                   \r\n                }\r\n\r\n              \r\n\r\n                //Create the necessary tags. For ARIA purposes we need to give the items an ID. So we use an internal index to create unique ids\r\n                var element = item[0].nodeName == 'DIV' ? 'div' : 'span';\r\n                index++;\r\n\r\n                var html = '<button id=\"rateit-reset-{{index}}\" type=\"button\" data-role=\"none\" class=\"rateit-reset\" aria-label=\"' + $.rateit.aria.resetLabel + '\" aria-controls=\"rateit-range-{{index}}\"><span></span></button><{{element}} id=\"rateit-range-{{index}}\" class=\"rateit-range\" tabindex=\"0\" role=\"slider\" aria-label=\"' + $.rateit.aria.ratingLabel + '\" aria-owns=\"rateit-reset-{{index}}\" aria-valuemin=\"' + itemdata('min') + '\" aria-valuemax=\"' + itemdata('max') + '\" aria-valuenow=\"' + itemdata('value') + '\"><{{element}} class=\"rateit-empty\"></{{element}}><{{element}} class=\"rateit-selected\"></{{element}}><{{element}} class=\"rateit-hover\"></{{element}}></{{element}}>';\r\n                item.append(html.replace(/{{index}}/gi, index).replace(/{{element}}/gi, element));\r\n\r\n                //if we are in RTL mode, we have to change the float of the \"reset button\"\r\n                if (!ltr) {\r\n                    item.find('.rateit-reset').css('float', 'right');\r\n                    item.find('.rateit-selected').addClass('rateit-selected-rtl');\r\n                    item.find('.rateit-hover').addClass('rateit-hover-rtl');\r\n                }\r\n\r\n                if (itemdata('mode') == 'font') {\r\n                    item.addClass('rateit-font').removeClass('rateit-bg');\r\n                }\r\n                else {\r\n                    item.addClass('rateit-bg').removeClass('rateit-font');\r\n                }\r\n\r\n                itemdata('init', JSON.parse(JSON.stringify(item.data()))); //cheap way to create a clone\r\n            }\r\n\r\n            var isfont = itemdata('mode') == 'font';\r\n\r\n            \r\n\r\n\r\n            //resize the height of all elements, \r\n            if (!isfont) {\r\n                item.find('.rateit-selected, .rateit-hover').height(itemdata('starheight'));\r\n            }\r\n\r\n\r\n            var range = item.find('.rateit-range');\r\n            if (isfont) {\r\n                //fill the ranges with the icons\r\n                var icon = itemdata('icon');\r\n                var stars = itemdata('max') - itemdata('min');\r\n\r\n                var txt = '';\r\n                for(var i = 0; i< stars; i++){\r\n                    txt += icon;\r\n                }\r\n                \r\n                range.find('> *').text(txt);\r\n                \r\n\r\n                itemdata('starwidth', range.width() / (itemdata('max') - itemdata('min')))\r\n            }\r\n            else {\r\n                //set the range element to fit all the stars.\r\n                range.width(itemdata('starwidth') * (itemdata('max') - itemdata('min'))).height(itemdata('starheight'));\r\n            }\r\n\r\n\r\n            //add/remove the preset class\r\n            var presetclass = 'rateit-preset' + ((ltr) ? '' : '-rtl');\r\n            if (itemdata('ispreset')) {\r\n                item.find('.rateit-selected').addClass(presetclass);\r\n            }\r\n            else {\r\n                item.find('.rateit-selected').removeClass(presetclass);\r\n            }\r\n\r\n            //set the value if we have it.\r\n            if (itemdata('value') != null) {\r\n                var score = (itemdata('value') - itemdata('min')) * itemdata('starwidth');\r\n                item.find('.rateit-selected').width(score);\r\n            }\r\n\r\n            //setup the reset button\r\n            var resetbtn = item.find('.rateit-reset');\r\n            if (resetbtn.data('wired') !== true) {\r\n                resetbtn.bind('click', function (e) {\r\n                    e.preventDefault();\r\n\r\n                    resetbtn.blur();\r\n\r\n                    var event = $.Event('beforereset');\r\n                    item.trigger(event);\r\n                    if (event.isDefaultPrevented()) {\r\n                        return false;\r\n                    }\r\n\r\n                    item.rateit('value', null);\r\n                    item.trigger('reset');\r\n                }).data('wired', true);\r\n\r\n            }\r\n\r\n            //this function calculates the score based on the current position of the mouse.\r\n            var calcRawScore = function (element, event) {\r\n                var pageX = (event.changedTouches) ? event.changedTouches[0].pageX : event.pageX;\r\n\r\n                var offsetx = pageX - $(element).offset().left;\r\n                if (!ltr) { offsetx = range.width() - offsetx };\r\n                if (offsetx > range.width()) { offsetx = range.width(); }\r\n                if (offsetx < 0) { offsetx = 0; }\r\n\r\n                return score = Math.ceil(offsetx / itemdata('starwidth') * (1 / itemdata('step')));\r\n            };\r\n\r\n            //sets the hover element based on the score.\r\n            var setHover = function (score) {\r\n                var w = score * itemdata('starwidth') * itemdata('step');\r\n                var h = range.find('.rateit-hover');\r\n                if (h.data('width') != w) {\r\n                    range.find('.rateit-selected').hide();\r\n                    h.width(w).show().data('width', w);\r\n                    var data = [(score * itemdata('step')) + itemdata('min')];\r\n                    item.trigger('hover', data).trigger('over', data);\r\n                }\r\n            };\r\n\r\n            var setSelection = function (value) {\r\n                var event = $.Event('beforerated');\r\n                item.trigger(event, [value]);\r\n                if (event.isDefaultPrevented()) {\r\n                    return false;\r\n                }\r\n\r\n                itemdata('value', value);\r\n                if (itemdata('backingfld')) {\r\n                    // If backing field is a select box with valuesrc option set to \"index\", update its selectedIndex property; otherwise, update its value.\r\n                    if (fld[0].nodeName == 'SELECT' && fld[0].getAttribute('data-rateit-valuesrc') === 'index') {\r\n                        $(itemdata('backingfld')).prop('selectedIndex', value).trigger('change');\r\n                    }\r\n                    else {\r\n                        $(itemdata('backingfld')).val(value).trigger('change');\r\n                    }\r\n                }\r\n                if (itemdata('ispreset')) { //if it was a preset value, unset that.\r\n                    range.find('.rateit-selected').removeClass(presetclass);\r\n                    itemdata('ispreset', false);\r\n                }\r\n                range.find('.rateit-hover').hide();\r\n                range.find('.rateit-selected').width(value * itemdata('starwidth') - (itemdata('min') * itemdata('starwidth'))).show();\r\n                item.trigger('hover', [null]).trigger('over', [null]).trigger('rated', [value]);\r\n                return true;\r\n            };\r\n\r\n            if (!itemdata('readonly')) {\r\n                //if we are not read only, add all the events\r\n\r\n                //if we have a reset button, set the event handler.\r\n                if (!itemdata('resetable')) {\r\n                    resetbtn.hide();\r\n                }\r\n\r\n                //when the mouse goes over the range element, we set the \"hover\" stars.\r\n                if (!itemdata('wired')) {\r\n                    range.bind('touchmove touchend', touchHandler); //bind touch events\r\n                    range.mousemove(function (e) {\r\n                        var score = calcRawScore(this, e);\r\n                        setHover(score);\r\n                    });\r\n                    //when the mouse leaves the range, we have to hide the hover stars, and show the current value.\r\n                    range.mouseleave(function (e) {\r\n                        range.find('.rateit-hover').hide().width(0).data('width', '');\r\n                        item.trigger('hover', [null]).trigger('over', [null]);\r\n                        range.find('.rateit-selected').show();\r\n                    });\r\n                    //when we click on the range, we have to set the value, hide the hover.\r\n                    range.mouseup(function (e) {\r\n                        var score = calcRawScore(this, e);\r\n                        var value = (score * itemdata('step')) + itemdata('min');\r\n                        setSelection(value);\r\n                        range.blur();\r\n                    });\r\n\r\n                    //support key nav\r\n                    range.keyup(function (e) {\r\n                        if (e.which == 38 || e.which == (ltr ? 39 : 37)) {\r\n                            setSelection(Math.min(itemdata('value') + itemdata('step'), itemdata('max')));\r\n                        }\r\n                        if (e.which == 40 || e.which == (ltr ? 37 : 39)) {\r\n                            setSelection(Math.max(itemdata('value') - itemdata('step'), itemdata('min')));\r\n                        }\r\n                    });\r\n\r\n                    itemdata('wired', true);\r\n                }\r\n                if (itemdata('resetable')) {\r\n                    resetbtn.show();\r\n                }\r\n            }\r\n            else {\r\n                resetbtn.hide();\r\n            }\r\n\r\n            range.attr('aria-readonly', itemdata('readonly'));\r\n        });\r\n    };\r\n\r\n    //touch converter http://ross.posterous.com/2008/08/19/iphone-touch-events-in-javascript/\r\n    function touchHandler(event) {\r\n\r\n        var touches = event.originalEvent.changedTouches,\r\n                first = touches[0],\r\n                type = \"\";\r\n        switch (event.type) {\r\n            case \"touchmove\": type = \"mousemove\"; break;\r\n            case \"touchend\": type = \"mouseup\"; break;\r\n            default: return;\r\n        }\r\n\r\n        var simulatedEvent = document.createEvent(\"MouseEvent\");\r\n        simulatedEvent.initMouseEvent(type, true, true, window, 1,\r\n                              first.screenX, first.screenY,\r\n                              first.clientX, first.clientY, false,\r\n                              false, false, false, 0/*left*/, null);\r\n\r\n        first.target.dispatchEvent(simulatedEvent);\r\n        event.preventDefault();\r\n    };\r\n\r\n    //some default values.\r\n    $.fn.rateit.defaults = { min: 0, max: 5, step: 0.5, mode: 'bg', icon: '★', starwidth: 16, starheight: 16, readonly: false, resetable: true, ispreset: false };\r\n\r\n    //invoke it on all .rateit elements. This could be removed if not wanted.\r\n    $(function () { $('div.rateit, span.rateit').rateit(); });\r\n\r\n})(jQuery);"]}