﻿@using Consumer.Resources.Mobile.SmartDelivery.Views.EmProducao;
@{
    Layout = null;
}

<div class="small">
    <table class="table table-hover" id="table-orders-producing">
        <thead>
            <tr>
                <th scope="col">Pedido</th>
                <th class="sticky-top bg-light" scope="col">@IndexRes.Origem</th>
                <th class="sticky-top bg-light" scope="col">@IndexRes.Cliente</th>
                <th class="sticky-top bg-light" scope="col">@IndexRes.Atendido</th>
                <th class="sticky-top bg-light" scope="col">@IndexRes.Iniciou</th>
                <th class="sticky-top bg-light" scope="col">@IndexRes.Tempo</th>
                <th class="sticky-top bg-light" scope="col"></th>
            </tr>
        </thead>
        <tbody>
            @*JS*@
        </tbody>
    </table>
</div>
<p class="small text-right font-italic">@IndexRes.AlterarStatus</p>

<script id="template-grid-item-sd-order-producing" type="text/template">
    <tr>
        <th scope="colgroup" colspan="7" class="bg-light"><b>Grupo #{{groupId}}</b> {{groupCreatedByFormatted}} ({{groupQuantityOfOrders}})</th>
    </tr>
    {{items}}
</script>

<script id="template-grid-item-sd-order-producing-items" type="text/template">
    <tr data-orderid="{{orderId}}">
        <th scope="row"><span class="badge badge-primary">{{sequence}}º</span> #{{orderId}}</th>
        <td class="sd-textredcolor">{{orderOrigin}}</td>
        <td>{{orderCustomerName}}</td>
        <td> {{orderCreatedDateTimeFormatted}}</td>
        <td> {{orderPreparationDateTimeFormatted}}</td>
        <td class="font-weight-bold text-{{orderStatusExpiration}}" data-statusdatetime="{{orderCreatedDateTime}}" data-relativedatetime>@IndexRes.Ha</td>
        <td>
            <a href="#" data-open-order><i class="material-icons md-24 gray align-text-bottom">info</i></a>
            <div class="dropdown d-inline-block">
                <a href="#" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="material-icons md-24 gray align-text-bottom">more_vert</i>
                </a>
                <div class="dropdown-menu">
                    <h6 class="dropdown-header">@IndexRes.Opcoes</h6>
                    <button type="button" class="dropdown-item" onclick="inProduction.undoGroup(event)" data-id="{{orderId}}">
                        <i class="material-icons md-24 gray align-text-bottom">delete</i>@IndexRes.Desagrupar
                    </button>
                </div>
            </div>
        </td>
    </tr>
</script>

