﻿@using Consumer.Resources.Mobile.Comanda.App.Home;

@model List<RAL.Consumer.Data.Context.MesaOuComandas>
@{
    ViewBag.Title = @QRCodeRes.Impressao;
    Layout = "~/Views/Shared/_LayoutPage.cshtml";
}

<section class="text-center">
    <h2 class="text-danger">@ViewBag.Title</h2>

    @foreach (var item in Model)
    {
        <div style="margin-bottom: 100px">
            <a href="@ViewBag.Url/Cardapio/Login/Mesa/@item.Numero">
                <img src="GetQRCode?url=@ViewBag.Url/Cardapio/Login/Mesa/@item.Numero" style="width: 150px">
                <br />
                @QRCodeRes.Mesa @item.Numero
            </a>
        </div>
    }
</section>
