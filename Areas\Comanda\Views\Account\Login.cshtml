﻿@using RAL.Consumer.Mobile.Areas.Comanda.Models
@using Consumer.Resources.Mobile.Comanda.Views
@model LoginViewModel
@{
    ViewBag.Title = LoginRes.Titulo;
}


<section id="loginForm">

    @using (Html.BeginForm("Login", "Account", new { ReturnUrl = ViewBag.ReturnUrl }, FormMethod.Post, new { @class = "form-horizontal", role = "form" }))
    {
        @Html.AntiForgeryToken()
        <h2><img src="@System.Web.VirtualPathUtility.ToAbsolute(System.Configuration.ConfigurationManager.AppSettings["Logo"])" alt="Logo" class="img-responsive" style="margin: auto;" /></h2>

        <div class="row">
            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            <div class="col-md-12">
                <p class="text-center">@LoginRes.UtilizeUsuarioCadastrado <a href="#" data-toggle="modal" data-target="#myModal">@LoginRes.VejaAqui</a>.</p>

                <div class="form-group">
                    @Html.TextBoxFor(m => m.Login, new { @maxlength="20", @class = "form-control input-lg", placeholder = "Usuário no Consumer" })
                    @Html.ValidationMessageFor(m => m.Login, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group">
                    @Html.PasswordFor(m => m.Password, new { @class = "form-control input-lg", placeholder = "Senha no Consumer", maxlength = 100 })
                    @Html.ValidationMessageFor(m => m.Password, "", new { @class = "text-danger" })
                    @*<div class="checkbox text-center">
                        <label for="RememberMe">
                            @Html.CheckBoxFor(m => m.RememberMe)Lembrar
                        </label>
                    </div>*@
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group">
                    <button class="btn btn-lg btn-primary btn-block" type="submit" data-loading-text="@LoginRes.Carregando">@LoginRes.Login</button>
                </div>
            </div>
            <div class="col-md-12 text-right">
                <div class="form-group">
                    @LoginRes.Versao @HttpContext.Current.ApplicationInstance.GetType().BaseType.Assembly.GetName().Version.ToString()
                </div>
            </div>
        </div>

    }

</section>

<!-- Modal -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <p>@LoginRes.UtilizeAdministrador</p>
                <img src="~/Images/colaboradores_consumer.png" class="img-responsive" />
                <img src="~/Images/colaboradores_consumer-permissoes.png" class="img-responsive" />
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-lg btn-default" data-dismiss="modal">@LoginRes.Fechar</button>
            </div>
        </div>
    </div>
</div>


@section Scripts {
    @Scripts.Render("~/bundles/jqueryval")
}


