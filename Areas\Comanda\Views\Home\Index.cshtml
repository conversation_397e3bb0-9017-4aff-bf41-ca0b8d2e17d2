﻿@using Consumer.Resources.Mobile.Comanda.Views.Home
@model Consumer.ServiceAbstractions.Services.Pedidos.PedidoModels.Mesas

@{
    ViewBag.Title = @IndexRes.Mesas;
}

@if (Session["PedidoModel"] != null)
{
    <div class=" alert alert-danger">
        @IndexRes.PedidoPendente @Html.ActionLink("Revisar", "Review", "Pedido")
    </div>
}
<div class="row">
    <div class="col-sm-12" style="padding: 0 5px">
        <div class="form-group">
            <input type="tel" class="form-control" id="querymesa" placeholder="@IndexRes.DigiteMesa">
        </div>
    </div>
</div>

<div class="row" id="ListaComandas">
    @Html.Partial("_ListaMesasComandas", Model)
</div>

<div class="rodape">
    <div id="toggle">
        <div class="toggleSwitch">
            <div id="switch" class="switchDisabled"></div>
        </div>
        <label id="labelToggleSwitch">Exibir total de conta e duração</label>
    </div>
    <div class="range">
        <a id="btnUltimosItens" class="btn btn-default" href="@Url.Action("UltimosItens", "Home")" title="@UltimosItensViewModelRes.UltimosItens">@UltimosItensViewModelRes.UltimosItens</a>
    </div>
</div>
