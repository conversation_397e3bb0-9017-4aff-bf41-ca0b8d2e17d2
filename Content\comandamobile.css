﻿body {
    padding-top: 50px;
    padding-bottom: 20px;
}

/* Set padding to keep content from hitting the edges */
.body-content {
    padding-top: 15px;
    padding-left: 15px;
    padding-right: 15px;
}

/* Override the default bootstrap behavior where horizontal description lists 
   will truncate terms that are too long to fit in the left column 
*/
.dl-horizontal dt {
    white-space: normal;
}

/* Aqui começou as customs */

@media (min-width: 768px) {
    .navbar-right {
        margin-right: 0;
    }
}

.navbar-brand {
    font-size: 16px;
}

.navbar-inverse .navbar-brand:hover, .navbar-inverse .navbar-brand:focus {
    color: #eee;
}

.navbar-inverse .navbar-brand {
    color: #fff;
}

body {
    padding-bottom: 190px;
}

#loginForm {
    max-width: 330px;
    padding: 15px;
    margin: 0 auto;
}

#item-produto-selected {
    padding-bottom: 0;
    margin-bottom: 0;
}

.item-produto, .item-categoria {
    text-align: initial;
    white-space: initial;
}

.item-produto {
    text-decoration: none;
}

    .item-produto:hover {
        text-decoration: none;
    }

    .item-produto .alert-info {
        cursor: pointer;
        box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.4);
        transition: all 0.4s;
        border-radius: 3px;
        touch-action: manipulation;
    }

        .item-produto .alert-info:hover {
            color: #ffffff;
            background-color: #771e86;
            border-color: rgba(0, 0, 0, 0);
            text-decoration: none;
        }

.item-produto-nome, .item-categoria-nome {
    display: block;
    height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: larger;
}

.item-categoria-nome {
    white-space: nowrap;
    line-height: 60px;
    text-align: center;
    font-size: 16px;
}

#item-produto-selected .item-produto-nome {
    height: initial;
    line-height: 14px;
    display: inline;
}

.item-produto-qtde:before {
    content: "x";
}

.parte-tamanho-preco {
    font-size: 70%;
}

#actions {
    /*padding-bottom: 10px;*/ /* Removdo para teste de aumentar área útil */
}

/* Custom Checkbox */
input[type=checkbox] {
    display: none;
}
    /* to hide the checkbox itself */
    input[type=checkbox] + label:after {
        font-family: 'Glyphicons Halflings';
        display: inline-block;
    }

    input[type=checkbox] + label:after {
        content: "\e157";
    }
    /* unchecked icon */
    input[type=checkbox]:checked + label:after {
        content: "\e067";
    }
/* checked icon */

input[type=radio] {
    visibility: hidden;
    position: absolute;
}

    input[type=radio] + label:after {
        font-family: 'FontAwesome';
        display: inline-block;
    }

    input[type=radio] + label:after {
        content: "\f10c";
    }
    /* unchecked icon */
    input[type=radio]:checked + label:after {
        content: "\f05d";
    }
    /* checked icon */

    input[type=checkbox].pull-right + label:after, input[type=radio].pull-right + label:after {
        float: right;
    }


.field-validation-error {
    color: #e80c4d;
}

/* ========================================================================
   Component: notify.less
 ========================================================================== */
.uk-notify {
    position: fixed;
    top: 50px;
    left: 50px;
    z-index: 1040;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    width: 350px;
}

.uk-notify-top-right,
.uk-notify-bottom-right {
    left: auto;
    right: 50px;
}

.uk-notify-top-center,
.uk-notify-bottom-center {
    left: 50%;
    margin-left: -175px;
}

.uk-notify-bottom-left,
.uk-notify-bottom-right,
.uk-notify-bottom-center {
    top: auto;
    bottom: 50px;
}

@media (max-width: 480px) {
    .uk-notify {
        left: 10px;
        right: 10px;
        width: auto;
        margin: 0;
    }
}

.uk-notify-message {
    position: relative;
    margin-bottom: 10px;
    padding: 15px;
    font-size: 16px;
    line-height: 22px;
    border-radius: 3px;
    padding-right: 35px;
    cursor: pointer;
}

    .uk-notify-message.alert.alert-normal {
        background: #444444;
        color: #ffffff;
    }

    .uk-notify-message > .close {
        visibility: hidden;
    }

    .uk-notify-message:hover > .close {
        visibility: visible;
    }

.tabela-pedido-itens {
    font-size: 16px;
}

    .tabela-pedido-itens .linha-pai {
        background-color: #f9f9f9;
    }

    .tabela-pedido-itens small, .tabela-pedido-itens .small {
        font-size: 85%;
    }

    .tabela-pedido-itens .linha-filho td {
        font-size: 85%;
        padding: 3px 5px
    }

    .tabela-pedido-itens .linha-neto td {
        font-size: 80%;
        padding: 3px 5px
    }

    .tabela-pedido-itens tr > td {
        text-align: right;
    }

        .tabela-pedido-itens tr > td:first-child {
            text-align: left;
        }

        .tabela-pedido-itens tr > td:nth-child(3):before {
            content: "x";
        }

    .tabela-pedido-itens.ultimos-itens tr > td:nth-child(3):before {
        content: "";
    }

    .tabela-pedido-itens .linha-filho > td:first-child {
        padding-left: 15px;
    }

    .tabela-pedido-itens .linha-neto > td:first-child {
        padding-left: 30px;
    }

    .tabela-pedido-itens .linha-total {
        font-size: 120%;
        font-family: monospace;
        background-color: #FDEAD4;
    }

        .tabela-pedido-itens .linha-total:last-child {
            font-weight: bold;
        }

    .tabela-pedido-itens .linha-processando {
        font-size: 90%;
        background-color: #FDEAD4;
        color: #764a16;
    }


.tabela-pagamentos {
    font-size: 16px;
}

    .tabela-pagamentos .linha-pai {
        background-color: #f9f9f9;
    }

    .tabela-pagamentos .linha-filho td, .tabela-pagamentos .linha-neto td {
        font-size: 90%;
        padding: 3px 5px
    }

    .tabela-pagamentos tr > td {
        text-align: right;
    }

        .tabela-pagamentos tr > td:first-child {
            text-align: left;
        }

    .tabela-pagamentos .linha-filho > td:first-child {
        padding-left: 15px;
    }

    .tabela-pagamentos .linha-neto > td:first-child {
        padding-left: 30px;
    }

    .tabela-pagamentos .linha-total {
        font-size: 120%;
        font-family: monospace;
        background-color: #FDEAD4;
    }

        .tabela-pagamentos .linha-total:last-child {
            font-weight: bold;
        }

tr + tr.linha-pai td {
}

/* Button Pagar */
.btn-pagar {
    color: #ffffff;
    background-color: #611BBD;
    border-color: #130269;
}

    .btn-pagar:hover,
    .btn-pagar:focus,
    .btn-pagar:active,
    .btn-pagar.active,
    .open .dropdown-toggle.btn-pagar {
        color: #ffffff;
        background-color: #49247A;
        border-color: #130269;
    }

    .btn-pagar:active,
    .btn-pagar.active,
    .open .dropdown-toggle.btn-pagar {
        background-image: none;
    }

    .btn-pagar.disabled,
    .btn-pagar[disabled],
    fieldset[disabled] .btn-pagar,
    .btn-pagar.disabled:hover,
    .btn-pagar[disabled]:hover,
    fieldset[disabled] .btn-pagar:hover,
    .btn-pagar.disabled:focus,
    .btn-pagar[disabled]:focus,
    fieldset[disabled] .btn-pagar:focus,
    .btn-pagar.disabled:active,
    .btn-pagar[disabled]:active,
    fieldset[disabled] .btn-pagar:active,
    .btn-pagar.disabled.active,
    .btn-pagar[disabled].active,
    fieldset[disabled] .btn-pagar.active {
        background-color: #611BBD;
        border-color: #130269;
    }

    .btn-pagar .badge {
        color: #611BBD;
        background-color: #ffffff;
    }

/*@media only screen and (max-width: 359px) {*/ /*iPhone SE e similares -- Comentado testar aumentar a área útil em todos dispositivos */
.col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12 {
    padding-right: 5px;
    padding-left: 5px;
}

.row {
    margin-right: -5px;
    margin-left: -5px;
}
/*}*/

.valores-financeiros {
    font-family: monospace;
    font-size: 110%;
}

/* Teste de fontes */
@media only screen and (min-width: 360px) /*Galaxy S' da vida e iPhone 6 a 8*/
{
    body, .form-control {
        font-size: 18px;
    }

    .form-control, .input-group-btn button, .select2-container--bootstrap .select2-selection--single {
        height: 40px;
    }

    h3, .h3 {
        font-size: 30px;
    }

    h4, .h4 {
        font-size: 22px;
    }

    .panel-title {
        font-size: 20px;
    }

    .btn-lg, .btn-group-lg > .btn {
        font-size: 22px;
    }

    .radio label, .checkbox label {
        padding-left: 25px;
    }

    .c-checkbox span, .c-radio span {
        margin-left: -25px;
        width: 25px;
        height: 25px;
    }
        /*Icon*/
        .c-checkbox span:before, .c-radio span:before {
            font-size: 15px;
        }
}

@media only screen and (min-width: 414px) /*iPhone 6+ a 8+*/
{
    body, .form-control {
        font-size: 21px;
    }

    .form-control, .input-group-btn button, .select2-container--bootstrap .select2-selection--single {
        height: 45px;
    }

    h3, .h3 {
        font-size: 36px;
    }

    h4, .h4 {
        font-size: 26px;
    }

    .panel-title {
        font-size: 22px;
    }

    .btn-lg, .btn-group-lg > .btn {
        font-size: 26px;
    }

    .radio label, .checkbox label {
        padding-left: 30px;
    }

    .c-checkbox span, .c-radio span {
        margin-left: -30px;
        width: 30px;
        height: 30px;
    }
        /*Icon*/
        .c-checkbox span:before, .c-radio span:before {
            font-size: 18px;
        }
}
/*Fim Teste de Fontes*/
#pedido-actions {
    padding-left: 5px;
    padding-right: 5px;
}

    #pedido-actions .btn.btn-lg small {
        font-size: 60%;
        text-align: center;
    }

.slider-formas {
    overflow: hidden;
    transition: all 1s;
    max-height: 100vh;
}

    .slider-formas.ocultar {
        max-height: 0;
    }

    .slider-formas .btn-group .btn {
        border-radius: 0 !important;
        border-color: #e9e9e9;
    }

.btn-primary:active, .btn-primary.active, .btn-primary:active:hover, .btn-primary.active:hover, .btn-primary:active:focus, .btn-primary.active:focus, .btn-primary:active.focus, .btn-primary.active.focus {
    color: #fff;
    background-color: #449d44;
    border-color: #398439;
}

/* Customs Radio e Checkbox */
.c-radio {
    margin-right: 4px;
}

    .c-checkbox *,
    .c-radio * {
        cursor: pointer;
    }

    .c-checkbox input,
    .c-radio input {
        opacity: 0;
        position: absolute !important;
        margin-left: 0 !important;
    }

    .c-checkbox span,
    .c-radio span {
        position: relative;
        display: inline-block;
        vertical-align: top;
        margin-left: -20px;
        width: 20px;
        height: 20px;
        border-radius: 2px;
        border: 1px solid #ccc;
        margin-right: 5px;
        text-align: center;
    }

        .c-checkbox span:before,
        .c-radio span:before {
            margin-left: 1px;
        }

    .c-checkbox:hover span,
    .c-radio:hover span {
        border-color: #5d9cec;
    }

.form-inline .c-checkbox span,
.form-inline .c-radio span {
    margin-left: 0;
}

.c-checkbox.c-checkbox-rounded span,
.c-radio.c-checkbox-rounded span,
.c-checkbox.c-radio-rounded span,
.c-radio.c-radio-rounded span {
    border-radius: 500px;
}
/* override for radio */
.c-radio span {
    border-radius: 500px;
}
    /* the icon */
    .c-checkbox span:before,
    .c-radio span:before {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        opacity: 0;
        text-align: center !important;
        font-size: 12px;
        line-height: 18px;
        vertical-align: middle;
    }
/* Checked state */
.c-checkbox,
.c-radio {
    /* override for radio */
    /* Disable state */
    /* override for radio */
}

    .c-checkbox input[type=checkbox]:checked + span:before,
    .c-radio input[type=checkbox]:checked + span:before,
    .c-checkbox input[type=radio]:checked + span:before,
    .c-radio input[type=radio]:checked + span:before {
        color: #fff;
        opacity: 1;
        transition: color 0.3s ease-out;
    }

    .c-checkbox input[type=checkbox]:checked + span,
    .c-radio input[type=checkbox]:checked + span,
    .c-checkbox input[type=radio]:checked + span,
    .c-radio input[type=radio]:checked + span {
        border-color: #5d9cec;
        background-color: #5d9cec;
    }

    .c-checkbox input[type=radio]:checked + span,
    .c-radio input[type=radio]:checked + span {
        background-color: #fff;
    }

        .c-checkbox input[type=radio]:checked + span:before,
        .c-radio input[type=radio]:checked + span:before {
            color: #5d9cec;
        }

    .c-checkbox input[type=checkbox]:disabled + span,
    .c-radio input[type=checkbox]:disabled + span,
    .c-checkbox input[type=radio]:disabled + span,
    .c-radio input[type=radio]:disabled + span {
        border-color: #dddddd !important;
        background-color: #dddddd !important;
    }

    .c-checkbox input[type=radio]:disabled + span,
    .c-radio input[type=radio]:disabled + span {
        background-color: #fff !important;
    }

        .c-checkbox input[type=radio]:disabled + span:before,
        .c-radio input[type=radio]:disabled + span:before {
            color: #dddddd;
        }

    .c-radio.c-radio-nofont {
        /* override for radio */
        /* Disable state */
        /* override for radio */
    }

        .c-radio.c-radio-nofont span:before {
            content: "";
            width: 10px;
            height: 10px;
            top: 50%;
            left: 50%;
            margin-top: -5px;
            margin-left: -5px;
            border-radius: 500px;
        }

        .c-radio.c-radio-nofont input[type=radio]:checked + span:before {
            color: #fff;
            opacity: 1;
            transition: color 0.3s ease-out;
        }

        .c-radio.c-radio-nofont input[type=radio]:checked + span {
            border-color: #5d9cec;
            background-color: #5d9cec;
        }

        .c-radio.c-radio-nofont input[type=radio]:checked + span {
            background-color: #fff;
        }

            .c-radio.c-radio-nofont input[type=radio]:checked + span:before {
                background-color: #5d9cec;
            }

        .c-radio.c-radio-nofont input[type=radio]:disabled + span {
            border-color: #dddddd !important;
            background-color: #dddddd !important;
        }

        .c-radio.c-radio-nofont input[type=radio]:disabled + span {
            background-color: #fff !important;
        }

            .c-radio.c-radio-nofont input[type=radio]:disabled + span:before {
                background-color: #dddddd;
            }

    .c-checkbox.c-right span, .c-radio.c-right span {
        float: right
    }

.radio.c-right label, .checkbox.c-right label {
    padding-left: 0;
}

/* Remover sombra no iOS */
input[type=number] {
    background-clip: padding-box;
}

.mb {
    margin-bottom: 10px;
}

.linha-pai .item-produto-qtde, .linha-pai .item-produto-valor {
    font-size: 85%;
    line-height: 2.3;
}

.loader-text {
    display: inline-block;
    margin-top: 6px;
    margin-left: 8px;
}

.loader-content {
    width: 100%;
    height: 60px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}

.loader {
    margin-top: 5px;
    border: 3px solid #d1a067;
    border-radius: 50%;
    border-top: 3px solid #764a16;
    width: 24px;
    height: 24px;
    -webkit-animation: spin 2s linear infinite; /* Safari */
    animation: spin 2s linear infinite;
    display: inline-block;
}

/* Safari */
@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(360deg);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* layout mesas */

.abrirMesa {
    text-decoration: none;
    color: #fff;
    font-weight: bold;
    font-size: 2.5rem;
}

.mesasTitulo {
    font-size: 1.6rem;
    color: #000;
    font-weight: 700;
}

.listaDeMesas {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: flex-start;
    margin-top: 10px;
}

.listaDeMesas a {
    text-decoration: none;
}

.mesas {
    position: relative;    
    width: 110px;
    height: 110px;
    border-radius: 1px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: .3s;
    padding: 5px;
}

    .mesas small {
        color: #fff;
        font-size: .9rem;
    }

    .mesas:hover {
        filter: brightness(1.1);
        text-decoration: none;
        color: #fff;
    }


.mesasOcupadas .mesas {
    background-color: #2e8b57;
}

    .mesasOcupadas .emFechamento {
        background-color: #dcaa37;
    }

.mesasLivres .mesas {
    background-color: #696969;
}

#nomeCliente {
    width: 100%;
    text-align: center;
    font-size: 1rem;
    color: #fff;
    white-space: nowrap;
    overflow: hidden;
    font-weight: 500;
    text-overflow: ellipsis;
    line-height: 15px;
    min-height: 30px;
    position: absolute;
    bottom: 4px;
    padding-left: 5px;
    padding-right: 5px;
    letter-spacing: 1px;
}

.duracao, .valor {
    color: #fff;
    font-size: 1.1rem;
    position: absolute;
}

.duracao {
    right: 5px;
    top: 3px;
}

.valor {
    left: 5px;
    bottom: 3px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 90%;
}

.iconeMesaBloqueada {
    position: absolute;
    left: 10px;
    top: 0px;
    color: #000;
    font-size: 22px;
}

.rodape {
    padding-right: 50px;
    padding-left: 50px;
    position: fixed;
    height: 70px;
    width: 100vw;
    background-color: #f4f4f4;
    bottom: 0;
    left: -.5px;
    box-shadow: -2px -2px 5px rgba(0,0,0,.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.toggleSwitch {
    width: 60px;
    height: 20px;
    background-color: #d3d3d3;
    border-radius: 3px;
    margin-right: 10px;
    position: relative;
}

    .toggleSwitch:has(div):has(.switchEnabled) {
        background-color: #8c8c8c;
    }

.switchEnabled {
    position: absolute;
    right: 0;
    top: 0;
    width: 25px;
    height: 20px;
    border-radius: 5px;
    background-color: #000;
    animation: animacaoEnabled .5s ease-in-out forwards;
}

.switchDisabled {
    position: absolute;
    left: 0;
    top: 0;
    width: 25px;
    height: 20px;
    border-radius: 5px;
    background-color: #000;
    animation: animacaoDisabled .5s ease-in-out forwards;
}

#labelToggleSwitch {
    width: 100%;
    font-size: 1.1rem;
    word-break: break-word;
    white-space: break-spaces;
    margin: 0;
}

@media (max-width: 767px) {
    .listaDeMesas {
        justify-content: center;
    }
}

@media (max-width: 490px) {
    .mesas {
        width: 90px;
        height: 90px;
    }

    .rodape {
        padding-right: 10px;
        padding-left: 10px;
    }

    #toggle {
        flex-direction: column;
        align-items: flex-start;
        justify-items: flex-start
    }
}

@keyframes animacaoEnabled {
    from {
        right: 30px;
    }

    to {
        right: 0;
    }
}

@keyframes animacaoDisabled {
    from {
        left: 30px;
    }

    to {
        left: 0;
    }
}
