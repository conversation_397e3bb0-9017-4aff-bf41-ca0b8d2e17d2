﻿'use strict';

// Será usado no capacete do Entregador para exibir os dados de contato
//infowindow = infowindow || new google.maps.InfoWindow({});
//infowindow.setContent(`<span class="font-weight-bold">${group.groupDeliveryManName} (${group.groupQuantityOfOrders})</span><br />${group.groupDeliveryManPhone}`);
//infowindow.open(marker.getMap(), marker);

let mapRouteProduced = {
    map: null,
    initMap: function (mapElement) {
        mapRouteProduced.map = new google.maps.Map(mapElement, {
            zoom: 13,
            center: appSettings.PontoCentral,
            mapTypeControlOptions: {
                mapTypeIds: [
                    "roadmap",
                    "styled_map_compacto",
                    "hybrid"
                ]
            }
        });

        mapRouteProduced.map.mapTypes.set("styled_map_compacto", styledMapTypeCompacto);

        const marker = new google.maps.Marker({
            position: appSettings.PontoCentral,
            map: mapRouteProduced.map,
            icon: '/content/smartdelivery/assets/sd-home.png'
        });
        marker.addListener('click', function () {
            infowindow.setContent('Meu Local');
            infowindow.open(mapRouteProduced.map, marker);
        });

        mapRouteProduced.map.addListener('drag', function () {
            if (mapRouteProduced.autoFitBounds) {
                mapRouteProduced.autoFitBounds = false;
                $('#mapRouteProducedCenter').show();
            }
        });

        var centerControlDiv = document.createElement('div');
        centerControlDiv.id = 'mapRouteProducedCenter';
        centerControlDiv.style = 'display: none; padding-top: 15px';
        new CustomControl(centerControlDiv, mapRouteProduced.fitBounds, null, 'Centralizar', 'Clique para Centralizar o Mapa');
        mapRouteProduced.map.controls[google.maps.ControlPosition.BOTTOM_LEFT].push(centerControlDiv);
    },
    autoFitBounds: true,
    fitBounds: function () {
        let bounds = new google.maps.LatLngBounds();
        bounds.extend(appSettings.PontoCentral);
        mapRouteProduced.markers.forEach(m => {
            bounds.extend(m.getPosition())
        });
        mapRouteProduced.map.fitBounds(bounds);
        mapRouteProduced.autoFitBounds = true;
        $('#mapRouteProducedCenter').hide();
    },
    groups: [],
    markers: [],
    addMarker: function (groupId, orderId, location, status) {
        const icon = appSettings.markerIconsRota[status.replace(/ /g, '').toLowerCase()].icon;
        const marker = new google.maps.Marker({
            position: location,
            map: this.map,
            icon: icon
        });

        marker.orderId = orderId;
        marker.groupId = groupId;
        marker.defaultIcon = icon;

        marker.addListener('click', function () {
            mapRouteProduced.selectOrder(this.orderId)
        });

        this.markers.push(marker);
    },
    selectOrder: function (orderId) {
        $(`[data-orderid=${orderId}]`).find(`a[data-open-order]`).trigger('click');
    },
    getOrders: function () {
        $.post('/smartdelivery/paraentrega/getorders', function (response) {
            if (response.Success) {

                let groups = response.Model;

                let totalOrders = groups.reduce((a, b) => a + b.groupQuantityOfOrders, 0);
                $('#orderProducedQty').html(`(${totalOrders})`);
                $('#table-orders-produced > p').toggleClass('d-none', groups.length !== 0);

                for (let i = 0; i < groups.length; i++) {
                    let currentGroup = groups[i];

                    //Add List
                    let $listItem = $(`#table-orders-produced [data-groupid=${currentGroup.groupId}]`);
                    if ($listItem.length === 0) {
                        $listItem = iterateAndReplace('template-grid-item-sd-order-produced', currentGroup);
                        $('#table-orders-produced').append($listItem);
                    }
                    else {
                        //Upd List
                        let old = mapRouteProduced.groups.find(e => e.groupId === currentGroup.groupId);
                        if (old && old.hash != currentGroup.hash) {
                            let $newListItem = $(iterateAndReplace('template-grid-item-sd-order-produced', currentGroup));
                            $listItem.replaceWith($newListItem);
                        }
                    }

                    // Markers
                    for (let j = 0; j < currentGroup.items.length; j++) {

                        let currentOrder = currentGroup.items[j];
                        let position = new google.maps.LatLng(currentOrder.lat, currentOrder.lng);
                        const marker = mapRouteProduced.markers.find(m => m.orderId === currentOrder.orderId);
                        if (marker) {
                            if (marker.getIcon() !== appSettings.SelectedPin)
                                marker.setIcon(appSettings.markerIconsRota[currentOrder.orderDeliveryStatus.replace(/ /g, '').toLowerCase()].icon);
                            marker.defaultIcon = appSettings.markerIconsRota[currentOrder.orderDeliveryStatus.replace(/ /g, '').toLowerCase()].icon;
                            marker.setPosition(position);
                        }
                        else
                            mapRouteProduced.addMarker(currentGroup.groupId, currentOrder.orderId, position, currentOrder.orderDeliveryStatus);
                    }
                }

                // Del List
                $('#table-orders-produced [data-groupid]').each(function () {
                    const groupExist = groups.find(m => m.groupId === $(this).data('groupid'));
                    if (!groupExist) {
                        $(this).slideUp(() => $(this).remove());
                    }
                });

                // Del Markers
                mapRouteProduced.markers = mapRouteProduced.markers.filter(function (marker) {
                    const groupExist = groups.find(m => m.groupId === marker.groupId);
                    if (!groupExist) {
                        marker.setMap(null);
                    }
                    return groupExist;
                });

                if (mapRouteProduced.autoFitBounds)
                    mapRouteProduced.fitBounds();

                calcTimeLapse();
                mapRouteProduced.groups = groups;
            }
            else {
                if (response.ErrorMessage)
                    $.notify(`<i class="material-icons align-bottom">cancel</i> ${response.ErrorMessage}`, { timeout: 0, status: 'danger' });
                else // Erro desconhecido
                    $.notify(`<i class="material-icons align-bottom">cancel</i> Não foi possível obter os dados, por favor, recarregue a página.`, { timeout: 15, status: 'danger' });
            }

            //após obter pedidos, agenda uma nova chamada 
            clearTimeout(mapRouteProduced.nextUpdateTimeout);
            mapRouteProduced.nextUpdateTimeout = setTimeout(mapRouteProduced.getOrders, appSettings.TempoAtualizacaoEmMS);
        });
    },
    highlightMarkers: (groupId, orderId = null) => {
        let filterMarkers = [];
        if (orderId > 0)
            filterMarkers = mapRouteProduced.markers.filter(element => element.orderId === orderId);
        else
            filterMarkers = mapRouteProduced.markers.filter(element => element.groupId === groupId);

        if (filterMarkers.length) {
            const isSelected = filterMarkers[0].getIcon() === appSettings.SelectedPin;

            for (let i in filterMarkers) {
                if (!isSelected) {
                    filterMarkers[i].setIcon(appSettings.SelectedPin);
                    filterMarkers[i].setAnimation(3);
                }
                else {
                    filterMarkers[i].setIcon(filterMarkers[i].defaultIcon);
                    filterMarkers[i].setAnimation(4);
                }
            }
        }
    }
};

// essa função é chamada no sd.init.js, de acordo com a aba selecionada
function initTabToDelivery() {
    mapRouteProduced.getOrders();
}

$(function () {
    // Evento para selecionar pela grid
    $(document).on('click', '#table-orders-produced [data-marker-group]', function () { mapRouteProduced.highlightMarkers($(this).data('marker-group')); });
    $(document).on('click', '#table-orders-produced [data-marker-order]', function () { mapRouteProduced.highlightMarkers(null, $(this).data('marker-order')); });
});
