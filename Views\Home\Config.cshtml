﻿@using Consumer.Resources.Mobile.Comanda.App.Home;

@{
    ViewBag.Title = @ConfigRes.Config;
    Layout = "~/Views/Shared/_LayoutPage.cshtml";
}

<div class="row">
    <div class="col-md-offset-2 col-md-8 col-lg-offset-3 col-lg-6">
        <section class="m">
            <h1 class="text-danger">@ViewBag.Title</h1>
            <h4>@ConfigRes.ConfigMobile</h4>

            <div class="row">
                <div class="col-sm-6 mb">
                    <a href="#" data-toggle="modal" data-target="#modalConsumer" class="btn btn-lg btn-primary btn-block">@ConfigRes.ConfigConsumer</a>
                </div>
                <div class="col-sm-6 mb">
                    <a href="#" data-toggle="modal" data-target="#modalManual" class="btn btn-lg btn-primary btn-block">@ConfigRes.ConfigManual</a>
                </div>
            </div>
        </section>
    </div>
</div>

@section BodyArea {
    <!-- Modal -->
    <div class="modal fade" id="modalConsumer" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content" style="font-size: 150%">
                <div class="modal-body">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h3>@ConfigRes.Instrucoes</h3>
                    <ol>
                        <li class="m">@ConfigRes.AbraConsumer</li>
                        <li class="m">
                            @ConfigRes.Acesse<strong>@ConfigRes.Apps</strong>
                            <img src="~/Images/configuracoes.png" class="img-responsive" />
                        </li>
                        <li class="m">
                            Clique em <strong>@ConfigRes.ConfigConexao</strong>
                            <img src="~/Images/configuracoes2_v2.png" class="img-responsive" />
                        </li>
                    </ol>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default btn-lg" data-dismiss="modal">Voltar</button>
                    <a class="btn btn-primary btn-lg" href="/"><i class="fa fa-check"></i>@ConfigRes.Feito</a>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="modalManual" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            @using (Html.BeginForm())
            {
                <div class="modal-content">
                    <div class="modal-body">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h3>@ConfigRes.Dados</h3>
                        <div class="form-group form-group-lg">
                            <label>@ConfigRes.Servidor</label>
                            <input type="text" name="SERVER" value="LOCALHOST" class="form-control" placeholder="LOCALHOST" />
                        </div>
                        <div class="form-group form-group-lg">
                            <label>@ConfigRes.BancoDados</label>
                            <input type="text" name="DATABASE" value="" class="form-control" placeholder="Ex: C:\Users\<USER>\AppData\Local\Empresa\Sistema\Banco.FDB" />
                        </div>
                        <div class="form-group form-group-lg">
                            <label>@ConfigRes.Instancia</label><br />
                            <input type="radio" name="INSTANCE" value="Default" required /> Default&nbsp;
                            <input type="radio" name="INSTANCE" value="FirebirdConsumer" /> FirebirdConsumer
                        </div>
                        <div class="form-group form-group-lg">
                            <label>
                                <input type="checkbox" name="POOL" checked="checked" />
                                @ConfigRes.Pool
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default btn-lg" data-dismiss="modal">@ConfigRes.Voltar</button>
                        <button class="btn btn-success btn-lg" title="@ConfigRes.Salvar"><i class="fa fa-check"></i> @ConfigRes.Salvar</button>
                    </div>
                </div>
            }
        </div>
    </div>
}