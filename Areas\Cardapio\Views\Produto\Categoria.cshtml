﻿@using Consumer.Resources.Mobile.Cardapio.Views.Produto
@model IEnumerable<string>
@{
    ViewBag.Title = @CategoriaRes.Cardapio;
}

@foreach (var item in Model)
{
    <div class="col-lg-3 col-md-6 col-xs-12 pv">
        <a href="@Url.Action(item, "Produto")" class="link-unstyled text-dark">
            <div class="panel b categoria">
                <div class="panel-body text-center text-ellipsis">
                    <em class="fa fa-3x fa-cutlery mb-lg"></em>
                    <br>
                    <span class="h4">@item</span>
                </div>
            </div>
        </a>
    </div>
}
