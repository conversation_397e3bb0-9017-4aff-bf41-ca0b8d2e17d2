﻿@using Consumer.Resources.Mobile.Comanda.Views.Shared
@model List<RAL.Consumer.Mobile.Models.ProdutoViewModel>
<h4>@Consumer.Resources.Mobile.Cardapio.Views.Shared.ComplementosProdutoTamanhoPartialRes.Complementos</h4>
@if (Model.Count() == 0)
{
    <div class="alert alert-warning">
        <i class="glyphicon glyphicon-warning-sign"></i> @ComplementosProdutoTamanhoPartialRes.NaoHaComp
    </div>
}
else
{
    <ul class="list-group">
        @for (int i = 0; i < Model.Count(); i++)
        {
            var g = Model;
            <li class="list-group-item list-group-item-success clearfix">
                <input type="hidden" name="SelectedComplementos[@i].Codigo" value="@g[i].Codigo" />
                <label style="line-height: 35px">
                    @g[i].Descricao (@g[i].Valor.ToString("C"))
                </label>
                <div class="pull-right" style="width: 130px">
                    <div class="input-group">
                        <span class="input-group-btn">
                            <button type="button" class="btn btn-default btn-minus">
                                <span class="fa fa-minus text-danger"></span>
                            </button>
                        </span>
                        <input type="number" class="form-control text-center" name="SelectedComplementos[@i].Qtd" id="com-@g[i].Codigo" value="0" data-val-range-min="0" data-val-range-max="99">
                        <span class="input-group-btn">
                            <button type="button" class="btn btn-default btn-plus">
                                <span class="fa fa-plus text-success"></span>
                            </button>
                        </span>
                    </div>
                </div>
            </li>
        }
    </ul>
}