﻿'use strict';

const STATUS_OPTIONS = ['success', 'warning', 'danger'];
let appSettings = {};
let infowindow; // é usado em todos os mapas

/* Funções úteis e compartilhadas */

// Retorna HTML a partir de um template e lista de objetos
function MapObjectToHtmlFromList(templateId, dataObject) {
    let htmlResult = '';

    for (let index in dataObject) {
        htmlResult += iterateAndReplace(templateId, dataObject[index]);
    }

    return htmlResult;
}

// Substitui valores do objeto no template passado
function iterateAndReplace(templateIdOrHtml, obj, parentObj) {
    let htmlResult = templateIdOrHtml;
    if (htmlResult.indexOf(' ') === -1) {
        let $el = $(`#${templateIdOrHtml}`);
        if ($el.length)
            htmlResult = $el.html();
    }

    for (let property in obj) {
        if (obj.hasOwnProperty(property)) {
            if (typeof obj[property] === "object")
                if (!Array.isArray(obj[property]))
                    htmlResult = iterateAndReplace(htmlResult, obj[property], parentObj + '.' + property || property);
                else {
                    // array - vai chamar outro template com o nome da propriedade
                    let templateIdInner = `${templateIdOrHtml}-${property}`;
                    let htmlInnerResult = MapObjectToHtmlFromList(templateIdInner, obj[property]);

                    let nameProperty = property;
                    if (parentObj)
                        nameProperty = parentObj + '.' + property;
                    htmlResult = htmlResult.replace(new RegExp('{{' + nameProperty + '}}', 'g'), htmlInnerResult);
                }
            else {
                let nameProperty = property;
                if (parentObj)
                    nameProperty = parentObj + '.' + property;
                htmlResult = htmlResult.replace(new RegExp('{{' + nameProperty + '}}', 'g'), obj[property]);
            }
        }
    }
    return htmlResult;
}

// Atualiza tempo relativo em todos componentes
function calcTimeLapse() {
    $('[data-relativedatetime]').each(function () {
        let $el = $(this);
        let $time = $el.closest('[data-statusdatetime]')
        let relativeTime = moment($time.data('statusdatetime')).fromNow();
        $el.text(relativeTime);
    });

    $('[data-statusdatetime]').each(function () {
        let $el = $(this);
        let refDateTime = moment($el.data('statusdatetime'));

        let classToAdd = getStatusExpiration(refDateTime, appSettings.TempoLimiteEmMinutos);

        if ($el.hasClass(classToAdd) === false) {
            $el.removeClass(STATUS_OPTIONS);
            $el.addClass(classToAdd);
        }
    });
}


//Calcular status de expiração baseado no tempo limite
function getStatusExpiration(refDateTime, limitTimeInMinutes) {
    refDateTime = moment(refDateTime);

    let currentDate = new Date();

    let diffInMinutes = moment(currentDate).diff(refDateTime, 'minutes', true);

    if (diffInMinutes > limitTimeInMinutes)
        diffInMinutes = limitTimeInMinutes;

    let faixaAtual = diffInMinutes * STATUS_OPTIONS.length / limitTimeInMinutes; // Convertendo para um número entre as faixas disponíveis

    let index = Math.ceil(faixaAtual) - 1;

    return STATUS_OPTIONS[index];
}

// Google Maps
function initMap() {
    infowindow = infowindow || new google.maps.InfoWindow({}); // inicializar infoWindows

    let mapProduceElement = document.getElementById('mapProduce');
    if (mapProduceElement) {
        mapProduce.initMap(mapProduceElement);
    }
    let mapRouteProducedElement = document.getElementById('mapRouteProduced');
    if (mapRouteProducedElement) {
        mapRouteProduced.initMap(mapRouteProducedElement);
    }
}

// Inicia a aplicação
$(function () {
    // Configura o 'relative time' com abreviação
    moment.updateLocale('pt-br', {
        relativeTime: {
            future: 'em %s',
            past: 'há %s',
            s: '%ds',
            ss: '%ds',
            m: '1m',
            mm: '%dm',
            h: '1h',
            hh: '%dh',
            d: '1d',
            dd: '%dd',
            M: '1 mês',
            MM: '%d meses',
            y: '1 ano',
            yy: '%d anos',
        }
    });

    // Iniciar popovers
    $('[data-toggle="popover"]').popover();

    $.getJSON('/smartdelivery/app/settings', { asJson: true }, function (response) {
        if (response.Success) {
            appSettings = response.Model;
            appSettings.SelectedPin = `/content/smartdelivery/assets/pin/${appSettings.PinSize}/pin-blue.png`;
            appSettings.TempoAtualizacaoEmMS = appSettings.TempoAtualizacaoEmSegundos * 1000;

            appSettings.markerIcons = {
                success: { icon: `/content/smartdelivery/assets/pin/${appSettings.PinSize}/pin-green.png` },
                warning: { icon: `/content/smartdelivery/assets/pin/${appSettings.PinSize}/pin-orange.png` },
                danger: { icon: `/content/smartdelivery/assets/pin/${appSettings.PinSize}/pin-red.png` }
            };

            appSettings.markerIconsRota = {
                entregue: { icon: `/content/smartdelivery/assets/pin/${appSettings.PinSize}/pin-green.png` },
                emrota: { icon: `/content/smartdelivery/assets/pin/${appSettings.PinSize}/pin-orange.png` },
                pendente: { icon: `/content/smartdelivery/assets/pin/${appSettings.PinSize}/pin-gray.png` }
            };

            initMap();
            if (appSettings.Ativo === true) {
                initTabToProduce();
                initTabInProduction();
                initTabToDelivery();

                $('#tab-paraproduzir').on('shown.bs.tab', initTabToProduce);
                $('#tab-emproducao').on('shown.bs.tab', initTabInProduction);
                $('#tab-paraentrega').on('shown.bs.tab', initTabToDelivery);
            }
            else {
                $('#modalSettings').modal({ backdrop: 'static', keyboard: false });
            }
        }
        else {
            // Não foi possível obter as configurações, exibe popup de configuração para ativação...
            $('#modalSettings').modal({ backdrop: 'static', keyboard: false });
        }
    });

    // Abrir modal com detalhes do pedido nos links com orderid
    $(document).on('click', 'a[data-open-order]', function (e) {
        e.stopPropagation();
        e.preventDefault();
        $('#modalOrder').find('.modal-body').data('orderid', $(this).closest('[data-orderid]').data('orderid'));
        $('#modalOrder').modal('show');
    });

    // Ao abrir o modal, carrega os detalhes do pedido 
    $('#modalOrder').on('show.bs.modal', function () {
        var $modalBody = $(this).find('.modal-body');
        $.get('/smartdelivery/app/getorder', { id: $modalBody.data('orderid') }, function (response) {
            if (response.Success) {
                let templateHtml = $('#template-sd-order-detail').html();
                let resultHtml = iterateAndReplace(templateHtml, response.Model);
                $modalBody.html(resultHtml);

                calcTimeLapse();

                // carregar itens do pedido
                $.get('/smartdelivery/app/getorderitens', { id: $modalBody.data('orderid') }, function (response) {
                    if (response.Success) {
                        let htmlResult = MapObjectToHtmlFromList('template-sd-order-detail-item', response.Model)
                        $modalBody.find('#table-order-itens tbody').html(htmlResult);
                    }
                    else {
                        $.notify(`<i class="material-icons align-bottom">cancel</i> ${response.ErrorMessage}`, { timeout: 0, status: 'danger' });
                    }
                });
            }
            else {
                $.notify(`<i class="material-icons align-bottom">cancel</i> ${response.ErrorMessage}`, { timeout: 0, status: 'danger' });
            }
        });
    });

    // Ao abrir o modal, carrega configurações
    $('#modalSettings').on('show.bs.modal', function () {
        var $modalContent = $(this).find('.modal-content');
        $.get('/smartdelivery/app/settings', function (response) {
            if (response.Success !== false) { // Sucesso, retornou a PartialView
                $modalContent.html(response);

                $.validator.unobtrusive.parse("#modalSettings");
                // Iniciar popovers
                //$('#btnHelp').popover({ content: $('#help-content').html() });
                $('#btnHelp').click(() => {
                    const $content = $('#help-content');
                    $content.slideToggle();
                });

                $('#Ativo').click(() => {
                    const $content = $('#help-content');
                    if($('#Ativo').prop('checked') === true)
                        $content.slideDown();
                    else
                        $content.slideUp();
                });

                // popular eventos após carregamento
                $('#Ativo').change(function () {
                    if ($(this).prop('checked') === true) {
                        $('#TempoLimiteEmMinutos')
                            .removeAttr('disabled')
                            .val($('#TempoLimiteEmMinutos').data('oldvalue') || $('#TempoLimiteEmMinutos').val()) // ao habilitar, retorna valor antigo
                            .removeData('oldvalue');

                        $('#AutoAgrupamentoAtivo').removeAttr('disabled');

                        if ($('#AutoAgrupamentoAtivo').prop('checked') === true) {
                            $('#AutoAgrupamentoAposSelecionar')
                                .removeAttr('disabled')
                                .val($('#AutoAgrupamentoAposSelecionar').data('oldvalue') || $('#AutoAgrupamentoAposSelecionar').val()) // ao habilitar, retorna valor antigo
                                .removeData('oldvalue');
                        }
                    }
                    else {
                        $('#TempoLimiteEmMinutos')
                            .data('oldvalue', $('#TempoLimiteEmMinutos').val()) // armazena valor para preencher ao reabilitar
                            .val(null)
                            .attr('disabled', 'disabled');

                        $('#AutoAgrupamentoAtivo').attr('disabled', 'disabled');

                        $('#AutoAgrupamentoAposSelecionar')
                            .data('oldvalue', $('#AutoAgrupamentoAposSelecionar').val()) // armazena valor para preencher ao reabilitar
                            .val(null)
                            .attr('disabled', 'disabled');
                    }
                }).trigger('change');

                $('#AutoAgrupamentoAtivo').change(function () {
                    if ($(this).prop('checked') === true) {
                        $('#AutoAgrupamentoAposSelecionar')
                            .removeAttr('disabled')
                            .val($('#AutoAgrupamentoAposSelecionar').data('oldvalue') || $('#AutoAgrupamentoAposSelecionar').val()) // ao habilitar, retorna valor antigo
                            .removeData('oldvalue');
                    }
                    else {
                        $('#AutoAgrupamentoAposSelecionar')
                            .data('oldvalue', $('#AutoAgrupamentoAposSelecionar').val()) // armazena valor para preencher ao reabilitar
                            .val(null)
                            .attr('disabled', 'disabled');
                    }
                }).trigger('change');
            }
            else {
                $.notify(`<i class="material-icons align-bottom">cancel</i> ${response.ErrorMessage}`, { timeout: 0, status: 'danger' });
            }
        });
    });

    //links nas abas
    let url = location.href.replace(/\/$/, "");

    if (location.hash) {
        const hash = url.split("#");
        $('ul.nav a[href="#' + hash[1] + '"]').tab("show");
        url = location.href.replace(/\/#/, "#");
        history.replaceState(null, null, url);
        setTimeout(() => {
            $(window).scrollTop(0);
        }, 400);
    }

    $('a[data-toggle="pill"]').on("click", function () {
        let newUrl;
        const hash = $(this).attr("href");
        newUrl = url.split("#")[0] + hash;
        newUrl += "/";
        history.replaceState(null, null, newUrl);
        document.title = $(this).attr('title');
        document.title += " - Smart Delivery";
    });
});