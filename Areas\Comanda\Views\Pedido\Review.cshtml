﻿@using Consumer.Resources.Mobile.Comanda.Views.Pedido
@using RAL.Common
@using Consumer.Services.V20.Pedidos.Extensoes;

@model RAL.Consumer.Mobile.Areas.Comanda.Models.PedidoRevisaoViewModel

@{
    ViewBag.Title = @ReviewRes.ConfirmarPedido + Model.Pedido.Numero;
    var classOffset = string.Empty;
}

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()

    @Html.HiddenFor(model => model.Pedido.Codigo)
    @Html.HiddenFor(model => model.Pedido.Numero)
    @Html.HiddenFor(model => model.Pedido.TipoEnum)

    if (Model.Pedido.NomeTelefoneCliente != null)
    {
        <div class="form-group">
            @Html.LabelFor(model => model.Pedido.NomeTelefoneCliente, @ReviewRes.Cliente, htmlAttributes: new { @class = "control-label" })
            <div class="input-group">
                @Html.EditorFor(model => model.Pedido.NomeTelefoneCliente, new { htmlAttributes = new { @class = "form-control", @readonly = "readonly" } })

                @if (Model.Pedido.ClienteIncluidoMobile)
                {
                    <span class="input-group-btn">
                        <a id="btn-limpa-cli"
                           class="btn btn-danger"
                           title=""
                           data-path="@Url.Action("LimparCliente", "Pedido")"
                           data-recordid="@Model.Pedido.Numero"
                           data-recordtitle="">
                            <span class="hidden-xs"></span><small class="visible-xs"></small>X
                        </a>
                    </span>
                }

                <input type="hidden" id="Pedido_CodigoContatoCliente" name="Pedido.CodigoContatoCliente" value="@Model.Pedido.CodigoContatoCliente" />
            </div>
        </div>
    }
    else
    {
        <div class="form-group">
            @Html.LabelFor(model => model.Pedido.CodigoContatoCliente, @ReviewRes.Cliente, htmlAttributes: new { @class = "control-label" })
            <div class="input-group">
                @Html.DropDownListFor(model => model.Pedido.CodigoContatoCliente, new SelectList(new List<RAL.Consumer.Data.Entities.CONTATOS>(), "id", "text"), null, new { @class = "form-control" })
                <span class="input-group-btn">
                    <button class="btn btn-default" type="button" onclick="createCliente();"><i class="fa fa-plus"></i></button>
                </span>
            </div>
            @Html.ValidationMessageFor(model => model.Pedido.CodigoContatoCliente, "", new { @class = "text-danger" })
        </div>
    }

    <div class="form-group">
        @Html.LabelFor(model => model.Pedido.Observacao, @ReviewRes.Observacoes, htmlAttributes: new { @class = "control-label" })
        @Html.EditorFor(model => model.Pedido.Observacao, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Pedido.Observacao, "", new { @class = "text-danger" })
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.Pedido.QuantidadePessoas, @ReviewRes.QtdPessoas, htmlAttributes: new { @class = "control-label" })
        @Html.EditorFor(model => model.Pedido.QuantidadePessoas, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Pedido.QuantidadePessoas, "", new { @class = "text-danger" })
    </div>

    <h4>@ReviewRes.ItensRevisao</h4>

    if (Model.Itens == null || Model.Itens.Count() == 0)
    {
        <div class="alert alert-warning">
            <i class="glyphicon glyphicon-warning-sign"></i> @ReviewRes.SemItens
        </div>

        if (Model.Pedido.ErroEnvio != null)
        {
            <div class="alert alert-danger">
                <i class="glyphicon glyphicon-danger-sign"></i> @Model.Pedido.ErroEnvio
            </div>
        }
    }
    else
    {
        <table class="table table-condensed table-responsive tabela-pedido-itens">
            @foreach (var item in Model.Itens)
            {
                //var subtotalDescendentes = 0M;
                <tr id="<EMAIL>"
                    class="linha-pai item-produto-data <EMAIL>"
                    data-codigo="@item.Sequence"
                    data-codigoproduto="@item.Content.Principal.Item.CodigoProdutoDetalhe"
                    data-qtde="@item.Content.Principal.Item.Quantidade"
                    @*data-itemporkg="@item.PRODUTODETALHE.PRODUTOS.PorKgOuLt"*@
                    data-precovenda="@item.Content.Principal.Item.ValorUnitario"
                    data-precocusto="@item.Content.Principal.Item.PrecoCusto"
                    data-valortotal="@item.Content.Principal.Item.ValorTotal"
                    data-combo="@Json.Encode(item.Content.Principal.Item.TipoItem == PedidoItemTipos.Combo)"
                    data-nomeproduto="@item.Content.Principal.Item.NomeProduto">
                    <td @(item.Content.Principal.Item.Quantidade == 0 ? "colspan=4" : "")>
                        <i class="glyphicon glyphicon-chevron-right"></i>
                        @Html.Raw(item.Content.Principal.Item.NomeProduto)
                        @{ string obs = item.Content.Principal.Item.FormatarObservacao();
                            if (!string.IsNullOrWhiteSpace(obs))
                            {
                                <i class="small">*@obs</i>
                            }
                        }

                        @if (item.Content.Principal.Item.TipoItem == PedidoItemTipos.Produto ||
                            item.Content.Principal.Item.TipoItem == PedidoItemTipos.ProdutoTamanho ||
                            item.Content.Principal.Item.TipoItem == PedidoItemTipos.ProdutoTamanhoPersonalizado
                            )
                        {
                            <br />
                            <a class="btn-obs btn-link" title="@ReviewRes.Observacoes" data-codigo="@item.Sequence" data-origem="Review"><i class="glyphicon glyphicon-edit"></i> @ReviewRes.Editar</a>
                        }
                    </td>
                    @if (item.Content.Principal.Item.Quantidade > 0)
                    {
                        <td class="item-produto-valor">@(item.Content.Principal.Item.ValorUnitario > 0 ? item.Content.Principal.Item.ValorUnitario.Value.ToString("C") : "")</td>
                        <td class="item-produto-qtde">@item.Content.Principal.Item.Quantidade</td>
                        <td class="item-produto-valor-total">@(item.Content.Principal.Item.ValorItem > 0 ? item.Content.Principal.Item.ValorTotal.Value.ToString("C") : "")</td>
                    }
                </tr>
                //Complementos
                foreach (var complemento in item.Content.Principal.Complementos)
                {
                    //subtotalDescendentes += complemento.Object.ValorTotal.GetValueOrDefault();
                    <tr id="<EMAIL><EMAIL>"
                        class="linha-filho item-produto-data <EMAIL>"
                        data-codigo="@item.Sequence"
                        data-codigofilho="@complemento.Sequence"
                        data-precovenda="@complemento.Content.ValorUnitario">
                        <td>
                            <i class="glyphicon glyphicon-plus-sign"></i>
                            @Html.Raw(complemento.Content.NomeProduto)
                            @{ string obsc = complemento.Content.FormatarObservacao();
                                if (!string.IsNullOrWhiteSpace(obsc))
                                {
                                    <i class="small">*@obsc</i>
                                }
                            }
                        </td>
                        <td class="item-produto-valor">@complemento.Content.ValorUnitario.GetValueOrDefault().ToString("C")</td>
                        <td class="item-produto-qtde">@complemento.Content.Quantidade</td>
                        <td class="item-produto-valor-total">@complemento.Content.ValorItem.GetValueOrDefault().ToString("C")</td>
                    </tr>
                }
                //Filho
                //foreach (var itemFilho in item.Filhos.OrderBy(c => Array.IndexOf(new int[] { 1, 3, 4, 5, 6, 7, 8 }, c.CodigoItemPedidoTipo)))
                foreach (var itemFilho in item.Content.Filhos)
                {
                    //subtotalDescendentes += itemFilho.Item.ValorTotal.GetValueOrDefault();
                    <tr id="<EMAIL><EMAIL>"
                        class="linha-filho item-produto-data <EMAIL>"
                        data-codigo="@item.Sequence"
                        data-codigofilho="@itemFilho.Sequence"
                        data-precovenda="@itemFilho.Content.Item.ValorUnitario">
                        <td>
                            <i class="glyphicon glyphicon-@((itemFilho.Content.Item.TipoItem == PedidoItemTipos.ProdutoFracionado) ? "adjust" : "inbox")"></i>
                            @Html.Raw(itemFilho.Content.Item.NomeProduto)
                            @{ string obsf = itemFilho.Content.Item.FormatarObservacao();
                                if (!string.IsNullOrWhiteSpace(obsf))
                                {
                                    <i class="small">*@obsf</i>
                                }
                            }
                            <br />
                            <a class="btn-obs btn-link" title="@ReviewRes.Observacoes" data-codigo="@item.Sequence" data-codigofilho="@itemFilho.Sequence" data-origem="Review"><i class="glyphicon glyphicon-edit"></i> @ReviewRes.Editar @(itemFilho.Content.Item.TipoItem == PedidoItemTipos.ProdutoFracionado ? @ReviewRes.EstaParte : @ReviewRes.EsteItem)</a>
                        </td>
                        <td class="item-produto-valor">@itemFilho.Content.Item.ValorUnitario.GetValueOrDefault().ToString("C")</td>
                        <td class="item-produto-qtde">@itemFilho.Content.Item.Quantidade</td>
                        <td class="item-produto-valor-total">@itemFilho.Content.Item.ValorItem.GetValueOrDefault().ToString("C")</td>
                    </tr>
                    //Neto
                    foreach (var itemNeto in itemFilho.Content.Complementos)
                    {
                        @* Os cálculos de quantidade e valor total abaixo foram alterados devido a alteração para permitir cobrança proporcional de complementos *@
                        //subtotalDescendentes += itemNeto.Object.ValorTotal.GetValueOrDefault();
                        <tr id="<EMAIL><EMAIL><EMAIL>"
                            class="linha-neto <EMAIL>"
                            data-precovenda="@itemNeto.Content.ValorUnitario">
                            <td>
                                <i class="glyphicon glyphicon-plus-sign"></i>
                                @Html.Raw(itemNeto.Content.NomeProduto)
                                @{ string obsn = itemNeto.Content.FormatarObservacao();
                                    if (!string.IsNullOrWhiteSpace(obsn))
                                    {
                                        <i class="small">*@obsn</i>
                                    }
                                }
                            </td>
                            <td class="item-produto-valor">@itemNeto.Content.ValorUnitario.GetValueOrDefault().ToString("C")</td>
                            <td class="item-produto-qtde">@(itemNeto.Content.Quantidade)</td>
                            <td class="item-produto-valor-total">@itemNeto.Content.ValorItem.GetValueOrDefault().ToString("C")</td>
                        </tr>
                    }
                }

                if (item.Content.ErroEnvio != null)
                {
                    <tr>
                        <td colspan="4">
                            <div class="small text-danger">
                                <i class="text-danger"></i> @item.Content.ErroEnvio
                            </div>
                        </td>
                    </tr>
                }

                <tr class="linha-subtotal <EMAIL>">
                    <td colspan="3">
                        <div class="item-produto-actions">
                            @if (item.Content.Principal.Item.TipoItem == PedidoItemTipos.ProdutoTamanhoPersonalizado)
                            {
                                classOffset = "col-xs-offset-6";
                            }
                            else
                            {
                                classOffset = string.Empty;
                                <div class="col-xs-3">
                                    <a class="btn-add btn btn-default btn-block btn-link mb" data-codigo="@item.Sequence"><i class="glyphicon glyphicon-plus text-success"></i></a>
                                </div>
                                <div class="col-xs-3">
                                    <a class="btn-del btn btn-default btn-block btn-link mb" data-codigo="@item.Sequence"><i class="glyphicon glyphicon-minus text-danger"></i></a>
                                </div>
                            }
                            <div class="col-xs-3 @classOffset">
                                <a class="btn-del-all btn btn-default btn-block btn-link mb" data-codigo="@item.Sequence"><i class="glyphicon glyphicon-trash text-danger"></i></a>
                            </div>
                            @if (item.Content.Principal.Item.TipoItem != PedidoItemTipos.Combo)
                            {
                                <div class="col-xs-3">
                                    <a class="btn-obs btn btn-default btn-block btn-link mb" data-codigo="@item.Sequence" data-origem="Review" title="@ReviewRes.ComplementosEObs"><i class="glyphicon glyphicon-list text-primary"></i></a>
                                </div>
                            }
                        </div>
                    </td>
                    <td colspan="1" class="item-produto-subtotal">
                        <strong>@(item.Content.Principal.Item.ValorTotal.GetValueOrDefault().ToString("C"))</strong>
                    </td>
                </tr>
            }
            <tr>
                <td colspan="4" style="text-align: center;">
                    <a href="@Url.Action("Create", "Pedido", new { Numero = Model.Pedido.Numero })">@ReviewRes.AdicionarItens</a>
                </td>
            </tr>
        </table>
    }

    <nav id="actions" class="navbar navbar-default navbar-fixed-bottom">
        <div id="pedido-actions" class="container-fluid">
            <div class="col-xs-4">
                <a id="btn-voltar" class="btn btn-lg btn-default btn-block navbar-btn" title="@ReviewRes.Voltar"><i class="glyphicon glyphicon-circle-arrow-left"></i> <span class="hidden-xs">@ReviewRes.Voltar</span><small class="visible-xs">@ReviewRes.Voltar1</small></a>
            </div>

            @if (Model.Itens != null && Model.Itens.Count() > 0)
            {
                <div class="col-xs-4">
                    <a id="btn-pedido-cancel" href="Cancel" class="btn btn-lg btn-danger btn-block navbar-btn" title="@ReviewRes.ExcluirPedido"><i class="glyphicon glyphicon-trash"></i> <span class="hidden-xs">@ReviewRes.Excluir</span><small class="visible-xs">@ReviewRes.Excluir1</small></a>
                </div>
                <div class="col-xs-4">
                    <button id="btn-pedido-review" class="btn btn-lg btn-success btn-block navbar-btn" title="@ReviewRes.RevisarPedido"><i class="glyphicon glyphicon-check"></i> <span class="hidden-xs">@ReviewRes.Enviar</span><small class="visible-xs">@ReviewRes.Enviar1</small></button>
                </div>
            }
        </div>
    </nav>
}

@section Scripts{
    <script>
        window.cliqueEnviarPedido = false;

        $(document).ready(function () {
            $("select").select2({
                language: "pt-BR",
                theme: "bootstrap",
                placeholder: "@ReviewRes.SelecioneCliente",
                allowClear: true,
                ajax: {
                    url: '@Url.Action("GetClientes")',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            q: params.term, // search term
                            page: params.page
                        };
                    },
                    processResults: function (data, params) {
                        // parse the results into the format expected by Select2
                        // since we are using custom formatting functions we do not need to
                        // alter the remote JSON data, except to indicate that infinite
                        // scrolling can be used
                        params.page = params.page || 1;

                        return {
                            results: data.items,
                            pagination: {
                                more: (params.page * 30) < data.total_count
                            }
                        };
                    },
                    cache: true
                },
                escapeMarkup: function (markup) { return markup; }, // let our custom formatter work
                minimumInputLength: 1
            });
        });

        $('#btn-limpa-cli').on('click', function (e) {
            var path = $(this).data('path');

            $.ajax({
                url: path,
                type: 'POST',
                cache: false,
                async: true,
                success: function (result) {
                    if (result !== null) {
                        if (result.sucesso) {
                            $("#Pedido_CodigoContatoCliente").val("");
                            location.reload();
                        }
                    }
                },
                error: function (result) {
                    $.notify("<i class='" + result.icone + "'></i> " + result.mensagem);
                }
            });
        });

        $(window).on('beforeunload', function () {
            if (!window.cliqueEnviarPedido) {
                var parametros = {
                    codigoCliente: $("#Pedido_CodigoContatoCliente").val(),
                    observacao: $("#Pedido_Observacao").val(),
                    qtdePessoas: $("#Pedido_QuantidadePessoas").val()
                };

                var queryString = $.param(parametros);

                var url = "/Comanda/Pedido/AtualizarDadosPedido?" + queryString;

                navigator.sendBeacon(url);
            }
        });

        $('#btn-pedido-review').on('click', function (e) {
            window.cliqueEnviarPedido = true;
        });
    </script>
}