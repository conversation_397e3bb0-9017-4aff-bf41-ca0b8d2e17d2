﻿@model List<RAL.Consumer.Data.Entities.COZINHAS>

@{
    ViewBag.Title = @Consumer.Resources.Mobile.Comanda.Views.Monitor.IndexRes.MonitorPreparo;
}

<link href="@Styles.Url("~/Content/app/css/app.css")" rel="stylesheet" type="text/css" id="maincss" />
<style>

    #conteudo {
        padding-bottom: 5rem;
        margin-top: 10px;
    }

    .body-content {
        padding: 5px;
    }

    .navbar-fixed-bottom {
        min-height: 20px;
    }

    .navbar-brand {
        display: block;
    }

    .content-wrapper {
        border-top: none;
    }

    .btn-xl {
        box-shadow: 6px 6px 6px #999;
        -webkit-transition: background 0.05s linear;
        -moz-transition: background 0.05s linear;
        -o-transition: background 0.05s linear;
        transition: background 0.05s linear;
        background-size: 1px 200px;
        transition: all .05s linear;
    }

        .btn-xl:active {
            box-shadow: 2px 2px 2px #777;
            background: #CF4647;
            transform: translateY(3px);
        }

    #modoPreparoModal {
        z-index: 10001;
    }

    @@media only screen and (max-width: 1024px) {
        .modal-dialog {
            /*position: fixed;*/
            margin: 0;
            padding: 0;
            height: 100%;
            width: 100%;
        }

        .modal-header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            z-index: 1;
        }

        .modal-content {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            border: 0;
            border-radius: 0;
            box-shadow: none;
        }

        .modal-body {
            position: absolute;
            padding: 5px;
            top: 60px;
            bottom: 0;
            font-size: 15px;
            overflow: auto;
            margin-bottom: 90px;
            padding: 0 15px 0;
            width: 100%;
        }

        .modal-footer {
            position: absolute;
            right: 0;
            bottom: 0;
            left: 0;
            height: 80px;
            padding: 10px;
            background: #fff;
        }
        /* to delete the scrollbar */
        /*
            ::-webkit-scrollbar {
                -webkit-appearance: none;
                background: #f1f3f5;
                border-left: 1px solid darken(#f1f3f5, 10%);
                width: 10px;
            }
            ::-webkit-scrollbar-thumb {
                background: darken(#f1f3f5, 20%);
            }
            */
        .modal-backdrop {
            background: #ffffff;
        }

            .modal-backdrop.in {
                opacity: .97;
                filter: alpha(opacity=97);
            }

        .foto {
            height: 200px;
        }

        .sweet-overlay {
            background-color: rgba(0,0,0,.8);
        }
    }
</style>

<audio id="clickDown">
    <source src="~/Content/audio/click.mp3" type="audio/mpeg" />
    <source src="~/Content/audio/click.ogg" type="audio/ogg" />
</audio>

<audio id="clickUp">
    <source src="~/Content/audio/clickUp.mp3" type="audio/mpeg" />
    <source src="~/Content/audio/clickUp.ogg" type="audio/ogg" />
</audio>

<audio id="novoItem">
    <source src="~/Content/audio/novoItemCozinha.mp3" type="audio/mpeg" />
    <source src="~/Content/audio/novoItemCozinha.ogg" type="audio/ogg" />
</audio>

<div id="conteudo">
    <h4>@Consumer.Resources.Mobile.Comanda.Views.Monitor.IndexRes.SelecioneCozinha</h4>
    <div class="portlets-wrapper">
        <div class="row">
            @foreach (var Cozinhas in Model)
            {
                <div data-toggle="portlet" class="col-lg-4 col-sm-6">
                    <div id="panelPortlet6" class="panel panel-default" style="border-color:#808080">
                        <div class="panel-heading">
                            <div class="row">
                                <div class="col-xs-12 text-left">
                                    <h4>
                                        @Cozinhas.DESCRICAO
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <div class="panel-wrapper">
                            <div class="panel-footer">
                                <div class="row">
                                    <div class="col-xs-12 col-md-6">
                                        <p>@Consumer.Resources.Mobile.Comanda.Views.Monitor.IndexRes.Producao</p>
                                        <a class="btn btn-xl btn-purple btn-block navbar-btn" href="@Url.Action("Produzir", new { id = @Cozinhas.CODIGO })"><strong>@Consumer.Resources.Mobile.Comanda.Views.Monitor.IndexRes.PorItem</strong></a>
                                        <a class="btn btn-xl btn-purple btn-block navbar-btn" href="@Url.Action("ProduzirPorPedido", new { id = @Cozinhas.CODIGO })"><strong>@Consumer.Resources.Mobile.Comanda.Views.Monitor.IndexRes.PorPedido</strong></a>
                                    </div>
                                    <div class="col-xs-12 col-md-6">
                                        <p>@Consumer.Resources.Mobile.Comanda.Views.Monitor.IndexRes.Entrega</p>
                                        <a class="btn btn-xl btn-warning btn-block navbar-btn" href="@Url.Action("Produzir", new { id = @Cozinhas.CODIGO, acao = "Entregar" })"><strong>@Consumer.Resources.Mobile.Comanda.Views.Monitor.IndexRes.PorItem</strong></a>
                                        <a class="btn btn-xl btn-warning btn-block navbar-btn" href="@Url.Action("ProduzirPorPedido", new { id = @Cozinhas.CODIGO, acao = "Entregar" })"><strong>@Consumer.Resources.Mobile.Comanda.Views.Monitor.IndexRes.PorPedido</strong></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<div class="modal fade" id="modoPreparoModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            @*Preenchido por ajax*@
        </div>
    </div>
</div>

@section Scripts {

    <script>
        function clickUp() {
            $("#clickUp")[0].play();
        }
        var currentUrl = "";
        var intervalUpdate = null;
        var idUltimoItemPedido = 0;
        var lastIdItemPedido = 0;

        function listarItens(timeOutCallback) {
            $.ajax({
                url: currentUrl,
                cache: false,
                type: "GET",
                dataType: "html",
                success: function (data, textStatus, XMLHttpRequest) {
                    var xResponse = JSON.parse(XMLHttpRequest.getResponseHeader('x-responded-json'))
                    if (xResponse && xResponse.status != 200) {
                        window.location = window.location.href; // Erro ou não autorizado
                    }

                    $("#conteudo").html(data);
                    //Deixando todos cards do mesmo tamanho
                    var heights = $(".panel-body-item").map(function () {
                        return $(this).innerHeight();
                    }).get();

                    maxHeight = Math.max.apply(null, heights);
                    $(".panel-body").css("height", maxHeight + "px");
                    $(".navbar-brand").text("@Consumer.Resources.Mobile.Comanda.Views.Monitor.IndexRes.Index_MonitorDe " + $("#acao").text());
                    carregar();

                    $('#cardsArea').css('margin-bottom', $('#barraStatusInferior').height() + 'px');
                },
                complete: function () {
                    if (timeOutCallback !== undefined)
                        timeOutCallback();
                }
            });
        }

        function carregar() {
            $('body').on('click', '.modo-preparo', function (e) {
                e.preventDefault();

                if (!$(e.target).hasClass('btn')) {

                    $(this).attr('data-target', '#modoPreparoModal');
                    $(this).attr('data-toggle', 'modal');

                    var url = $(this).data('load');
                    $('.modal-content').load(url, function () {
                        $.validator.unobtrusive.parse(this);

                        if (typeof localStorage !== "undefined") {
                            $(".modal-body").css('font-size', parseInt(localStorage.modoPreparoFont));
                        }
                    });
                }

            });

            $('#modoPreparoModal').on('hidden.bs.modal', function () {
                $(this).removeData('bs.modal');
                $(".modal-content").html("");
            });

            var currentItemIdPedido = $("#maxItemPedido").val();

            if (currentItemIdPedido != null) {
                if (currentItemIdPedido > lastIdItemPedido) {
                    $("#novoItem")[0].play();
                    lastIdItemPedido = currentItemIdPedido;
                }
            }

            $('a.btn').click(function (e) {
                e.preventDefault();
                $("#clickDown")[0].play();

                if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|Windows Phone|Opera Mini|IEMobile|Mobile/i.test(navigator.userAgent))
                    setTimeout(function () {
                        clickUp();
                    }, 50);
                else
                    $("#clickDown").on("ended", clickUp);

                if ($(this).hasClass("btn-pronto") || $(this).hasClass("btn-pronto-entregue") || $(this).hasClass("btn-entregue")) {

                    var ehItemIndividualPorPedido = $(this).hasClass("btn-item-individual");
                    var $divItemIndividualPorPedido = $(this).closest('.panel-body');

                    var $buttonClick = $(this);
                    var confirmar = @Json.Encode((bool)ViewBag.MonitorPreparoSolicitarConfirmacao);

                    if (confirmar) {
                        swal({
                            title: $($buttonClick).data("nome"),
                            text: "Pedido: " + $($buttonClick).data("codigopedido"),
                            type: "info",
                            showCancelButton: true,
                            confirmButtonClass: "btn-success",
                            confirmButtonText: "@Consumer.Resources.Mobile.Comanda.Views.Monitor.IndexRes.Confirmar",
                            cancelButtonText: "@Consumer.Resources.Mobile.Cardapio.Views.Home.IndexRes.Voltar",
                        },
                            function () {
                                $.ajax({
                                    url: $($buttonClick).attr('href'),
                                    data: { codigoItemPedido: $($buttonClick).data("codigoitempedido"), codigoPedido: $($buttonClick).data("codigopedido"), codigoCozinha: $($buttonClick).data("codigocozinha") },
                                    cache: false,
                                    type: "POST",
                                    dataType: "json",
                                    success: function (data, textStatus, XMLHttpRequest) {

                                        if (ehItemIndividualPorPedido) {
                                            $divItemIndividualPorPedido.animo({
                                                animation: "bounceOut"
                                            }, function () {
                                                $divItemIndividualPorPedido.remove();
                                                $.notify("<i class='fa fa-check'></i> " + data.mensagem);
                                            });
                                        }
                                        else {
                                            var itemId = data.codigoItemPedido || data.codigoPedido;

                                            $('#item' + itemId).animo({
                                                animation: "bounceOut"
                                            }, function () {
                                                $('#item' + itemId).remove();
                                                $.notify("<i class='fa fa-check'></i> " + data.mensagem);
                                            });
                                        }
                                    }
                                });
                            }
                        );
                    }
                    else {
                        $.ajax({
                            url: $(this).attr('href'),
                            data: { codigoItemPedido: $(this).data("codigoitempedido"), codigoPedido: $(this).data("codigopedido"), codigoCozinha: $(this).data("codigocozinha") },
                            cache: false,
                            type: "POST",
                            dataType: "json",
                            success: function (data, textStatus, XMLHttpRequest) {

                                if (ehItemIndividualPorPedido) {
                                    $divItemIndividualPorPedido.animo({
                                        animation: "bounceOut"
                                    }, function () {
                                        $divItemIndividualPorPedido.remove();
                                        $.notify("<i class='fa fa-check'></i> " + data.mensagem);
                                    });
                                }
                                else {
                                    var itemId = data.codigoItemPedido || data.codigoPedido;

                                    $('#item' + itemId).animo({
                                        animation: "bounceOut"
                                    }, function () {
                                        $('#item' + itemId).remove();
                                        $.notify("<i class='fa fa-check'></i> " + data.mensagem);
                                    });
                                }
                            }
                        });
                    }

                    e.preventDefault();
                }
                else {
                    currentUrl = $(this).attr('href');
                    listarItens();
                    //clearInterval(intervalUpdate);
                    //intervalUpdate = setInterval(listarItens, 15000);

                    clearTimeout(intervalUpdate);
                    var delaylistaitens = 15000;
                    intervalUpdate = setTimeout(function verificarLista() {
                        listarItens(function () {
                            intervalUpdate = setTimeout(verificarLista, delaylistaitens);
                        });
                    }, delaylistaitens);
                }
            });


        }

        $(document).ready(function () {
            carregar();

            $(document).on("click", ".font", function () {
                var qtd = parseInt($(".modal-body").css('font-size')) + (parseInt($(this).data("qtd")));
                if (qtd < 10)
                    qtd = 10;
                else if (qtd > 50)
                    qtd = 50;
                if (typeof localStorage !== "undefined") {
                    localStorage.modoPreparoFont = qtd;
                }
                $(".modal-body").css('font-size', qtd)
            });

            $(document).on("click", "#btnLimparTodos", function (e) {
                e.preventDefault();
                var btnLimparTodos = $(this);

                swal({
                    title: "Tem certeza que deseja limpar todos os itens pendentes?",
                    text: "Todos os itens serão finalizados",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonClass: "btn-success",
                    confirmButtonText: "Limpar",
                    cancelButtonText: "Voltar",
                },
                    function () {
                        $.ajax({
                            url: $(btnLimparTodos).attr('href'),
                            data: { acao: $(btnLimparTodos).data("acao"), codigoCozinha: $(btnLimparTodos).data("codigocozinha") },
                            cache: false,
                            type: "POST",
                            dataType: "json",
                            success: function (data, textStatus, XMLHttpRequest) {
                                $.notify("<i class='fa fa-check'></i> " + data.mensagem);
                                listarItens();
                                clearInterval(intervalUpdate);
                                intervalUpdate = setInterval(listarItens, 15000); //DESCOMENTAR
                            }
                        });
                    }
                );

            });

            @if (Request.QueryString["ReturnUrl"] != null) { // Tratamento para listar os itens da cozinha passado por link direto
                <text>
                    currentUrl = '@Request.QueryString["ReturnUrl"]';
                    listarItens();
                    clearInterval(intervalUpdate);
                    intervalUpdate = setInterval(listarItens, 15000);
                </text>
            }
        });
    </script>
}



