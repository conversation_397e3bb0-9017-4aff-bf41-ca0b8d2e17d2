﻿var appCurrent = {
	CultureInterface: "pt-BR",
	CultureFormat: "pt-BR",
	CurrencySymbol: "R$",
	DecimalSeparator: ",",
};

function initCultureApp() {

	var cookieCultureSerialize = getCookie("Culture");
	if (cookieCultureSerialize == null || cookieCultureSerialize == undefined)
		return;

	appCurrent = cookieCultureSerialize;
}

function getCookie(cName) {
	try {
		var cookie = document.cookie;

		if (cookie != null || cookie != undefined) {
			var cookieList = cookie.split("; ");
			for (var i = 0; i < cookieList.length; i++) {

				if (cookieList[i].includes(cName)) {
					var cookieDecoded = decodeURIComponent(cookieList[i]);
					var cookieName = cName + "=";
					var cookieTemp = cookieDecoded.replace(cookieName, '');
					var jsonCookie = JSON.parse(cookieTemp);
					return jsonCookie;
				}
			}
        }

		return null;
	} catch (e) {
		return null;
	}
}

function getLocalizedContent(content, locale) {

	if (content == null || content == undefined)
		content = "ALGO_MAIS";

	if (locale == null || locale == undefined) {
		var culture = appCurrent.CultureFormat;
		locale = culture;
	}

	var culture = locale.substring(0, 2);

	var messages = {
		"pt": {
			"ALGO_MAIS": "Algo mais?",
			"SIM": "Sim",
			"NAO": "Não",
			"AdicioneAteRes": "Adicione até ",
			"SaboresRes": " Sabores"
		},
		"en": {
			"ALGO_MAIS": "Something else?",
			"SIM": "Yes",
			"NAO": "No",
			"AdicioneAteRes": "Add up to ",
			"SaboresRes": " Flavors"
		},
		"es": {
			"ALGO_MAIS": "¿algo mas?",
			"SIM": "Sí",
			"NAO": "No",
			"AdicioneAteRes": "Sumar ",
			"SaboresRes": " Sabores"
		}
	}

	return messages[culture][content];
}