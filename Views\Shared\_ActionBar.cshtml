﻿@using Consumer.Resources.Mobile.Comanda.App.Shared;
@model RAL.Consumer.Data.Entities.PEDIDOS
<nav class="navbar navbar-default navbar-fixed-bottom" role="navigation">
    @if (Model != null && Model.ITENSPEDIDO.Count > 0)
    {
        <div class="col-xs-4">
            <button class="btn btn-lg btn-block btn-square btn-warning navbar-btn" title="@_ActionBarRes.ChamarGarcom" onclick="chamarGarcom(event)"><i class="fa fa-bell"></i><br /><span class="hidden-xs">@_ActionBarRes.Chamar</span>@_ActionBarRes.Garcom</button>
        </div>
        <div class="col-xs-8">
            <a href="@Url.Action("Review", "Pedido", new { Area = "Cardapio" })" class="btn btn-lg btn-block btn-square btn-success navbar-btn">
                <i class="fa fa-sticky-note"></i>
                <div class="label label-danger" style="top: -10px; right: 5px; padding: 2px 5px;">@Model.ITENSPEDIDO.Count</div>
                <br />@_ActionBarRes.EnviarPedido
            </a>
        </div>
    }
    else
    {
        <div class="col-xs-12">
            <button class="btn btn-lg btn-block btn-square btn-warning navbar-btn" title="_ActionBarRes.ChamarGarcom" onclick="chamarGarcom(event)"><i class="fa fa-bell"></i><br /><span class="hidden-xs">@_ActionBarRes.Chamar</span>@_ActionBarRes.Garcom</button>
        </div>
    }
</nav>

