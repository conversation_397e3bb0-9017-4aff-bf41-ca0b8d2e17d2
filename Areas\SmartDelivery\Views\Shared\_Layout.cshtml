﻿@{
    Layout = null;
}

<!DOCTYPE html>
<html lang="pt-br">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="/content/smartdelivery/css/main.css">
    <link rel="stylesheet" href="/content/smartdelivery/css/notify.css">
    @Styles.Render("~/bundles/sweetalertcss")

    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <title>@ViewBag.Title</title>
</head>

<body class="d-flex flex-column">

    @RenderBody()

    <!-- jQuery first, then Popper.js, then Bootstrap JS -->
    <script src="/content/smartdelivery/js/jquery.js"></script>
    <script src="/content/smartdelivery/js/popper.min.js"></script>
    <script src="/content/smartdelivery/js/bootstrap.js"></script>
    <script src="/content/smartdelivery/js/moment-with-locales.js"></script>

    @Scripts.Render("~/bundles/jqueryval")
    @Scripts.Render("~/bundles/notify")
    @Scripts.Render("~/bundles/sweetalert")

    @RenderSection("Scripts", false)
</body>

</html>