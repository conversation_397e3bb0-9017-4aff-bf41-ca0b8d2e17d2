﻿@using Consumer.Resources.Mobile.Comanda.Views.Pedido
@model RAL.Consumer.Mobile.Models.ObservacaoViewModel

<h4>@AddProdutoTamanhoRes.Observacoes</h4>
@if (Model.TodasObservacoes.Count() == 0)
{
    <div class="alert alert-warning">
        <i class="glyphicon glyphicon-warning-sign"></i>@AddProdutoTamanhoRes.NaoHaObs
    </div>
}
else
{
    <ul class="list-group">
        @foreach (var g in Model.TodasObservacoes)
        {
            <li class="list-group-item list-group-item-warning">
                <input type="checkbox"
                       name="SelectedObservacoes"
                       value="@g.Chave" id="<EMAIL>"
                       class="pull-right" />
                <label for="<EMAIL>" style="width: 100%">
                    @g.Descricao
                </label>
            </li>
        }

        <li class="list-group-item list-group-item-warning">
            @Html.TextBoxFor(m => m.ObservacoesAdicionais, new { @class = "form-control", placeholder = AddProdutoTamanhoRes.ObsAdicionais })
        </li>
    </ul>
}