﻿@using Consumer.Resources.Mobile.SmartDelivery.Views.ParaEntrega;
@{
    Layout = null;
}

<div class="row h-100">
    <div class="col-xl-9 col-lg-8 col-md-8 col-sm-7 col-12 p-0">
        <div id="mapRouteProduced" class="h-100"></div>
    </div>
    <div class="col-xl-3 col-md-4 col-sm-5 d-none d-sm-block bg-dark h-100 overflow-auto">
        <div class="text-white" id="table-orders-produced">
            <p class="text-center small">@IndexRes.NenhumPedido</p>
            @* JS *@
        </div>
    </div>
</div>

<script id="template-grid-item-sd-order-produced" type="text/template">
    <div class="row sd-group-delivery mb-2" data-groupid="{{groupId}}">
        <div class="col">
            <div data-marker-group="{{groupId}}">
                <img class="sd-imgsize24" src="/content/smartdelivery/assets/capGray.png" alt="" /> <small class="text-white"><b>{{groupDeliveryManName}} ({{groupQuantityOfOrders}})</b></small>
            </div>
            <div class="sd-group-delivery-orders mt-2">
                {{items}}
            </div>
        </div>
    </div>
</script>

<script id="template-grid-item-sd-order-produced-items" type="text/template">
    <div class="row mb-2" data-orderid="{{orderId}}">
        <div class="col-1"></div>
        <div class="col-1">
            <img class="sd-imgsize24" src="/content/smartdelivery/assets/flagGreen.png" alt="">
        </div>
        <div class="col-8 small" data-marker-order="{{orderId}}">
            <b><span class="badge badge-primary">{{sequence}}º</span> #{{orderId}}</b>
            <span style="color: #ccc">{{orderDeliveryAddress}}</span>
            <p class="p-0 m-0 small {{orderDeliveryStatusCss}}"><b>{{orderDeliveryStatus}}</b></p>
        </div>
        <div class="col-auto">
            <a href="#" data-open-order><i class="material-icons md-24 gray">info</i></a>
        </div>
    </div>
</script>
