/* ========================================================================
   Component: layout
 ========================================================================== */
body,
.wrapper > section {
  background-color: #f5f7fa;
}
.wrapper > .aside {
  background-color: #3a3f51;
}
/* ========================================================================
   Component: top-navbar
 ========================================================================== */
.topnavbar {
  background-color: #fff;
}
.topnavbar .navbar-header {
  background-color: transparent;
  background-image: -webkit-linear-gradient(left, #2b957a 0%, #37bc9b 100%);
  background-image: -o-linear-gradient(left, #2b957a 0%, #37bc9b 100%);
  background-image: linear-gradient(to right, #2b957a 0%, #37bc9b 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff2b957a', endColorstr='#ff37bc9b', GradientType=1);
}
@media only screen and (min-width: 768px) {
  .topnavbar .navbar-header {
    background-image: none;
  }
}
.topnavbar .navbar-nav > li > .navbar-text {
  color: #2b957a;
}
.topnavbar .navbar-nav > li > a,
.topnavbar .navbar-nav > .open > a {
  color: #2b957a;
}
.topnavbar .navbar-nav > li > a:hover,
.topnavbar .navbar-nav > .open > a:hover,
.topnavbar .navbar-nav > li > a:focus,
.topnavbar .navbar-nav > .open > a:focus {
  color: #144639;
}
.topnavbar .navbar-nav > .active > a,
.topnavbar .navbar-nav > .open > a,
.topnavbar .navbar-nav > .active > a:hover,
.topnavbar .navbar-nav > .open > a:hover,
.topnavbar .navbar-nav > .active > a:focus,
.topnavbar .navbar-nav > .open > a:focus {
  background-color: transparent;
}
.topnavbar .navbar-nav > li > [data-toggle='navbar-search'] {
  color: #ffffff;
}
.topnavbar .nav-wrapper {
  background-color: #2b957a;
  background-image: -webkit-linear-gradient(left, #2b957a 0%, #37bc9b 100%);
  background-image: -o-linear-gradient(left, #2b957a 0%, #37bc9b 100%);
  background-image: linear-gradient(to right, #2b957a 0%, #37bc9b 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff2b957a', endColorstr='#ff37bc9b', GradientType=1);
}
@media only screen and (min-width: 768px) {
  .topnavbar {
    background-color: #2b957a;
    background-image: -webkit-linear-gradient(left, #2b957a 0%, #37bc9b 100%);
    background-image: -o-linear-gradient(left, #2b957a 0%, #37bc9b 100%);
    background-image: linear-gradient(to right, #2b957a 0%, #37bc9b 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff2b957a', endColorstr='#ff37bc9b', GradientType=1);
  }
  .topnavbar .navbar-nav > .open > a,
  .topnavbar .navbar-nav > .open > a:hover,
  .topnavbar .navbar-nav > .open > a:focus {
    box-shadow: 0 -3px 0 rgba(255, 255, 255, 0.5) inset;
  }
  .topnavbar .navbar-nav > li > .navbar-text {
    color: #ffffff;
  }
  .topnavbar .navbar-nav > li > a,
  .topnavbar .navbar-nav > .open > a {
    color: #ffffff;
  }
  .topnavbar .navbar-nav > li > a:hover,
  .topnavbar .navbar-nav > .open > a:hover,
  .topnavbar .navbar-nav > li > a:focus,
  .topnavbar .navbar-nav > .open > a:focus {
    color: #144639;
  }
}
/* ========================================================================
   Component: sidebar
 ========================================================================== */
.sidebar {
  background-color: #3a3f51;
}
.sidebar .nav-heading {
  color: #919da8;
}
.sidebar .nav > li > a,
.sidebar .nav > li > .nav-item {
  color: #e1e2e3;
}
.sidebar .nav > li > a:focus,
.sidebar .nav > li > .nav-item:focus,
.sidebar .nav > li > a:hover,
.sidebar .nav > li > .nav-item:hover {
  color: #2b957a;
}
.sidebar .nav > li > a > em,
.sidebar .nav > li > .nav-item > em {
  color: inherits;
}
.sidebar .nav > li.active,
.sidebar .nav > li.open,
.sidebar .nav > li.active > a,
.sidebar .nav > li.open > a,
.sidebar .nav > li.active .nav,
.sidebar .nav > li.open .nav {
  background-color: #383d4e;
  color: #2b957a;
}
.sidebar .nav > li.active > a > em,
.sidebar .nav > li.open > a > em {
  color: #2b957a;
}
.sidebar .nav > li.active {
  border-left-color: #2b957a;
}
.sidebar-subnav {
  background-color: #3a3f51;
}
.sidebar-subnav > .sidebar-subnav-header {
  color: #e1e2e3;
}
.sidebar-subnav > li > a,
.sidebar-subnav > li > .nav-item {
  color: #e1e2e3;
}
.sidebar-subnav > li > a:focus,
.sidebar-subnav > li > .nav-item:focus,
.sidebar-subnav > li > a:hover,
.sidebar-subnav > li > .nav-item:hover {
  color: #2b957a;
}
.sidebar-subnav > li.active > a,
.sidebar-subnav > li.active > .nav-item {
  color: #2b957a;
}
.sidebar-subnav > li.active > a:after,
.sidebar-subnav > li.active > .nav-item:after {
  border-color: #2b957a;
  background-color: #2b957a;
}
/* ========================================================================
   Component: offsidebar
 ========================================================================== */
.offsidebar {
  border-left: 1px solid #cccccc;
  background-color: #ffffff;
  color: #515253;
}
