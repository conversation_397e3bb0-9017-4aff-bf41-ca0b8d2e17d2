﻿@using Consumer.Resources.Mobile.Comanda.App.Shared;

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="description" content="@_LayoutPageRes.Title">
    <meta name="keywords" content="consumer mobile, cardapio digital, cardapio qr-code, comanda mobile">
    <title>Consumer Mobile</title>
    <!-- =============== VENDOR STYLES ===============-->
    @Styles.Render("~/bundles/fontawesome")
    @Styles.Render("~/bundles/simpleLineIcons")
    @Styles.Render("~/bundles/animatecss")
    @Styles.Render("~/bundles/whirl")
    @Styles.Render("~/bundles/sweetalertcss")
    <!-- =============== BOOTSTRAP STYLES ===============-->
    <link href="@Styles.Url("~/Content/app/css/bootstrap.css")" rel="stylesheet" type="text/css" id="bscss" />
    <!-- =============== APP STYLES ===============-->
    <link href="@Styles.Url("~/Content/app/css/app.css")" rel="stylesheet" type="text/css" id="maincss" />
    <link href="@Styles.Url("~/Content/mvc-override.css")" rel="stylesheet" type="text/css" />
    <link href="@Styles.Url("~/Content/app/css/theme-d.css")" rel="stylesheet" type="text/css" />
    <!-- =============== PAGE VENDOR STYLES =============== -->
    @if (IsSectionDefined("Styles"))
    {
        @RenderSection("Styles", required: false)
    }
</head>

<body>
    <div class="wrapper">
        @{ Html.RenderPartial("_Alerts"); }
        @RenderBody()
    </div>

    @if (IsSectionDefined("BodyArea"))
    {@RenderSection("BodyArea", required: false)}
    <!-- =============== VENDOR SCRIPTS ===============-->
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/bundles/matchMedia")
    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
    @Scripts.Render("~/bundles/storage")
    @Scripts.Render("~/bundles/jqueryEasing")
    @Scripts.Render("~/bundles/animo")
    @Scripts.Render("~/bundles/slimscroll")
    @Scripts.Render("~/bundles/screenfull")
    @Scripts.Render("~/bundles/localize")
    @Scripts.Render("~/bundles/overlay")
    @Scripts.Render("~/bundles/sweetalert")
    <!-- =============== PAGE VENDOR SCRIPTS ===============-->
    @RenderSection("scripts", required: false)
    <!-- =============== APP SCRIPTS ===============-->
    @Scripts.Render("~/bundles/Angle")
</body>
</html>