﻿'use strict';

// Funções úteis para library Google Maps JS

/**
 * The CustomControl adds a control to the map that execeute a function
 */
class CustomControl {
    constructor(controlDiv, customFunction, paramFunction, text, title) {
        controlDiv.style.clear = "both";
        // Set CSS for the control border
        const goCenterUI = document.createElement("div");
        goCenterUI.id = "goCenterUI";
        goCenterUI.title = title;
        goCenterUI.style = "background-color: #fff; border: 2px solid #fff; border-radius: 3px; box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3); cursor: pointer; float: left; margin-bottom: 22px; text-align: center;"
        controlDiv.appendChild(goCenterUI);
        // Set CSS for the control interior
        const goCenterText = document.createElement("div");
        goCenterText.id = "goCenterText";
        goCenterText.innerHTML = text;
        goCenterText.style = "color: #191919; font-family: <PERSON><PERSON>, <PERSON><PERSON>, sans-serif; font-size: 18px; line-height: 25px; padding-left: 5px; padding-right: 5px;";
        goCenterUI.appendChild(goCenterText);
        // Set up the click event listener for 'Center Map': Set the center of
        // the map
        // to the current center of the control.
        goCenterUI.addEventListener("click", () => {
            customFunction(paramFunction);
        });
    }
}

/**
 * Style Map 
 */

//Mapa Completo "Default"
const styledMapTypeCompleto = new google.maps.StyledMapType(
    [],
    {
        name: "Completo"
    }
);

//Mapa Compacto "Sem locais (Pontos de Interesse)"
const styledMapTypeCompacto = new google.maps.StyledMapType(
    [
        {
            "featureType": "poi",
            "stylers": [
                {
                    "visibility": "off"
                }
            ]
        }
    ],
    {
        name: "Sem locais"
    }
);

// Calcular distâncias em metros
function getDistanceMeters(fromLatLngLiteral, toLatLngLiteral) {
    return google.maps.geometry.spherical.computeDistanceBetween(new google.maps.LatLng(fromLatLngLiteral), new google.maps.LatLng(toLatLngLiteral));
}

// Calcular rumo em relação ao norte
function getHeadingNorth(fromLatLngLiteral, toLatLngLiteral) {
    return google.maps.geometry.spherical.computeHeading(new google.maps.LatLng(fromLatLngLiteral), new google.maps.LatLng(toLatLngLiteral));
}

//Draw SemiCircle
//ref: https://gist.github.com/lmendoza92/3095f2e0d1dcbac504d3
function drawSector(map, lat, lng, r, azimuth, width) {
    let centerPoint = new google.maps.LatLng(lat, lng);
    let PRlat = (r / 3963) * (180 / Math.PI); // using 3963 miles as earth's radius 
    let PRlng = PRlat / Math.cos(lat * ((Math.PI / 180)));
    let PGpoints = [];
    PGpoints.push(centerPoint);

    let lat1 = lat + (PRlat * Math.cos(((Math.PI / 180)) * (azimuth - width / 2)));
    let lon1 = lng + (PRlng * Math.sin(((Math.PI / 180)) * (azimuth - width / 2)));
    PGpoints.push(new google.maps.LatLng(lat1, lon1));

    let lat2 = lat + (PRlat * Math.cos(((Math.PI / 180)) * (azimuth + width / 2)));
    let lon2 = lng + (PRlng * Math.sin(((Math.PI / 180)) * (azimuth + width / 2)));

    var theta = 0;
    var gamma = ((Math.PI / 180)) * (azimuth + width / 2);

    for (var a = 1; theta < gamma; a++) {
        theta = ((Math.PI / 180)) * (azimuth - width / 2 + a);
        let PGlon = lng + (PRlng * Math.sin(theta));
        let PGlat = lat + (PRlat * Math.cos(theta));

        PGpoints.push(new google.maps.LatLng(PGlat, PGlon));
    }

    PGpoints.push(new google.maps.LatLng(lat2, lon2));
    PGpoints.push(centerPoint);

    let poly = new google.maps.Polygon({
        path: PGpoints,
        strokeColor: '#4B0082',
        strokeOpacity: .2,
        map: map
    });

    poly.setMap(map);
    return poly;
} 

// Algoritmos de Combinação

//Ref: https://www.geeksforgeeks.org/print-all-possible-combinations-of-r-elements-in-a-given-array-of-size-n/
function combinationUtil(arr, data, start, end, index, r) {
    // Current combination is  
    // ready to be printed,  
    // print it 
    if (index == r) {

        for (let j = 0; j < r; j++)
            console.log(data[j] + " ");
        console.log("");

        return data;
    }

    // replace index with all 
    // possible elements. The  
    // condition "end-i+1 >=  
    // r-index" makes sure that  
    // including one element 
    // at index will make a  
    // combination with remaining  
    // elements at remaining positions 
    for (let i = start; i <= end && end - i + 1 >= r - index; i++) {
        data[index] = arr[i];
        combinationUtil(arr, data, i + 1, end, index + 1, r);
    }
}

// The main function that prints 
// all combinations of size r 
// in arr[] of size n. This  
// function mainly uses combinationUtil() 
function printCombination(arr, n, r) {
    // A temporary array to store  
    // all combination one by one 
    let data = [];

    // Print all combination  
    // using temprary array 'data[]' 
    combinationUtil(arr, data, 0, n - 1, 0, r);
    let result = [];
    result.push(data);
    //result.sort(function (a, b) {
    //    return a[r] - b[r];
    //});
    console.log(result);
}

/* Outro Teste Combinação C# */
//ref: https://codereview.stackexchange.com/questions/194967/get-all-combinations-of-selecting-k-elements-from-an-n-sized-array
