﻿@using Consumer.Resources.Mobile.Cardapio.Views.Produto
@model IEnumerable<RAL.Consumer.Mobile.Areas.Comanda.Models.ProdutoPartialViewModel>

@{
    ViewBag.Title = ViewBag.Title ?? @IndexRes.Cardapio;
}

@if (Model.Count() == 0)
{
    <h3>Nenhum produto encontrado.</h3>
}

@foreach (var etiqueta in Model.Select(m => m.NomeCategoria).Distinct().OrderBy(e => e))
{
    @*<h3>@etiqueta</h3>*@

    <div class="row mb">
        @foreach (var item in Model.Where(m => m.NomeCategoria == etiqueta))
        {
            <div class="col-md-6">
                <div class="modal-link" data-load="@Url.Action("Add", new { id = item.CodigoProduto  })" data-nome="@item.NomeProduto" data-descricao="@item.DescricaoProduto" data-etiqueta="@item.NomeCategoria" data-precovenda="@Html.DisplayFor(modelItem => item.PrecoVenda)" data-codigoproduto="@item.CodigoProduto" data-haswizard="@Json.Encode(item.HasWizard)" style="cursor: pointer">
                    <div class="panel widget">
                        <div class="portlet-handler">
                            <div class="row">
                                @if (ViewBag.ExibirImagemLista == true)
                                {
                                    <div class="col-sm-3">
                                        @if (System.IO.File.Exists(Server.MapPath(string.Format("~/Content/images/cardapio/{0}.jpg", item.CodigoProduto))))
                                        {
                                            <img src="@Url.Content(string.Format("~/Content/images/cardapio/{0}.jpg?q={1}", item.CodigoProduto, DateTime.Now.Ticks.ToString()))" alt="Foto" class="img-responsive center-block foto" />
                                        }
                                        else
                                        {
                                            <img src="@Url.Content(string.Format("~/Content/Images/Cardapio/{0}", ViewBag.ImagemPadrao))" alt="Foto" class="img-responsive center-block foto" />
                                        }
                                    </div>
                                }
                                <div class="col-sm-@(ViewBag.ExibirImagemLista == true ? "9" : "12")">
                                    <div class="panel-body text-left">
                                        @if (!item.Personalizavel)
                                        {
                                            <span class="text-success pull-right product-price">@Html.DisplayFor(modelItem => item.PrecoVenda)@(item.PorKgOuLt ? "/" + item.UnidadeComercializacao : string.Empty)</span>
                                        }
                                        else
                                        {
                                            <span class="text-success pull-right product-price">@item.PrecoVendaFormatado</span>
                                        }
                                        <h4 class="product-title">
                                            @Html.DisplayFor(modelItem => item.NomeProduto)
                                        </h4>
                                        @if (ViewBag.ExibirDescricaoLista == true)
                                        {
                                            <p class="product-description">
                                                @Html.DisplayFor(modelItem => item.DescricaoProduto)
                                            </p>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
}
@section BodyArea {

    <div id="actionBar" data-load="@Url.Action("_ActionBar", "Home")">
        @Html.Action("_ActionBar", "Home")
    </div>
    <div class="modal fade left" id="produtoModal" tabindex="-1" role="dialog" aria-labelledby="produtoModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                @*Preenchido por ajax*@
            </div>
        </div>
    </div>
}
